<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
        Schema::defaultStringLength(191); // Update defaultStringLength
        //prevent host header attack
        $allowed_host = array('127.0.0.1:8000','devoprex.sig.id','www.oprex.sig.id','oprex.sig.id','www.oprex.sig.id','**********','**********','localhost:8080');
        // dd($_SERVER['HTTP_HOST']);
        //Khusus akses via browser 
        if (!app()->runningInConsole() && (!isset($_SERVER['HTTP_HOST']) || !in_array($_SERVER['HTTP_HOST'], $allowed_host))) 
        {
            header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
            exit;
        }
    }
}
