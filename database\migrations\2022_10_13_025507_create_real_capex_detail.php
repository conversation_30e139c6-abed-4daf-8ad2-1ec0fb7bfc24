<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRealCapexDetail extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('real_capex_detail', function (Blueprint $table) {
            $table->bigIncrements('id_real_capex_detail');
            $table->string('kode_opco', 30);
            $table->string('no_billing', 30)->nullable();
            $table->string('no_project', 30)->nullable();
            $table->date('tanggal')->nullable()->default(NULL);
            $table->float('biaya_capex', 8, 5);
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->softDeletes();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('real_capex_detail');
    }
}
