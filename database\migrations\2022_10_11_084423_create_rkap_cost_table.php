<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRkapCostTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rkap_cost', function (Blueprint $table) {
            $table->bigIncrements('id_rkap_biaya');
            $table->string('kode_opco', 30);
            $table->string('tahun', 4);
            $table->string('bulan', 2);
            $table->bigInteger('rkap_bahan_bakar');
            $table->bigInteger('rkap_bahan_baku');
            $table->bigInteger('rkap_listrik');
            $table->bigInteger('rkap_tenaga_kerja');
            $table->bigInteger('rkap_pemeliharaan');
            $table->bigInteger('rkap_penyusutan');
            $table->bigInteger('rkap_administrasi_umum');
            $table->bigInteger('rkap_pajak_asuransi');
            $table->bigInteger('rkap_elim_bb');
            $table->bigInteger('rkap_elim_penyusutan');
            $table->bigInteger('rkap_elim_administrasi');
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rkap_cost');
    }
}
