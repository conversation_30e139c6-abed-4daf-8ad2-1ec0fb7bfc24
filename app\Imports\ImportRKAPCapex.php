<?php

namespace App\Imports;

use App\Models\RKAPCapex;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
class ImportRKAPCapex implements ToCollection, WithHeadingRow
{
    public $data;

    public function collection(Collection $rows)
    {
        $this->data = $rows;
    }
    // public function model(array $row)
    // {   
    //     //import -> client side(excel->json) 
    //     //validasi data kode_opco berdasarkan m_opco (Uppercase), tahun, bulan, rkap_capex angka atau decimal   
    //     return new RKAPCapex([
    //         'kode_opco'     => $row['opco'],
    //         'tahun'         => $row['tahun'], 
    //         'bulan'         => $row['bulan'],//required function month name to month number 
    //         'rkap_capex'    => $row['rkap_capex'],  
    //     ]);
    // }

    public function rules(): array
    {
        return[
            'kode_opco' =>['required', 'string'],
            'tahun' => ['required', 'numeric'],
            'bulan' => ['required', 'numeric'],
            'rkap_capex'    => ['required', 'numeric']
        ];
    }
}
