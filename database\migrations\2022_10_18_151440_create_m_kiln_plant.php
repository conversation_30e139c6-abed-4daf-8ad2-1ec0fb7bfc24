<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMKilnPlant extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_kiln_plant', function (Blueprint $table) {
            $table->bigIncrements('id_kiln_plant');
            $table->string('kode_plant', 30)->unique();
            $table->string('name_plant', 30)->unique();
            $table->string('kode_opco', 30);
            $table->string('source_system', 30)->nullable();
            $table->string('reference_sap', 30)->nullable();
            $table->string('reference_tis', 30)->nullable();
            $table->string('reference_opc_status', 30)->nullable();
            $table->string('reference_opc_rate', 30)->nullable();
            $table->string('reference_opc_output', 30)->nullable();
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->softDeletes();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable();
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('m_kiln_plant');
    }
}
