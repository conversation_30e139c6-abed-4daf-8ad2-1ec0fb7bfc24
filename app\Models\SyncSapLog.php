<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SyncSapLog extends Model
{
    protected $fillable = [
        'sync_sap_config_id',
        'year',
        'month',
        'status',
        'note',
        'tcode',
        'parameter',
        'config_name',
        'created_at',
        'updated_at'
    ];

    public static function statuses()
    {
        return [
            'success' => 'Success',
            'process' => 'Process',
            'fail' => 'Fail'
        ];
    }

    public function config()
    {
        return $this->belongsTo(SyncSapConfig::class, 'sync_sap_config_id', 'id');
    }
}
