<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MasterKuisioner extends Model
{
    //
    protected $table = 'master_kuisioner';
    protected $fillable = [
        'kuisioner',
        'urutan',
        'status',
        'created_by',
        'updated_by',
    ];

    public function sub_master_kuisioner(){
        return $this->hasMany(SubMasterKuisioner::class, 'master_kuisioner_id', 'id');
    }
}
