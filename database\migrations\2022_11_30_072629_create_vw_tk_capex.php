<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVwTkCapex extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("DROP VIEW IF EXISTS vw_tk_capex");
        DB::statement("CREATE OR REPLACE VIEW vw_tk_capex AS  SELECT rkcp.kode_opco,
                            to_date(concat(rkcp.tahun, '-', rkcp.bulan), 'YYYY-MM'::text) + '1 mon -1 days'::interval AS tanggal,
                                CASE
                                    WHEN tc.rkap_capex IS NULL THEN rkcp.rkap_capex::numeric
                                    ELSE tc.rkap_capex
                                END AS rkap_capex,
                                CASE
                                    WHEN tc.real_capex IS NULL THEN trc.real_capex::numeric
                                    ELSE tc.real_capex
                                END AS real_capex
                        FROM rkap_capex rkcp
                            LEFT JOIN tk_capex tc ON tc.kode_opco::text = rkcp.kode_opco::text AND tc.tanggal = (to_date(concat(rkcp.tahun, '-', rkcp.bulan), 'YYYY-MM'::text) + '1 mon -1 days'::interval)
                            LEFT JOIN ts_realisasi_capex trc ON trc.kode_opco::text = rkcp.kode_opco::text AND trc.tanggal = (to_date(concat(rkcp.tahun, '-', rkcp.bulan), 'YYYY-MM'::text) + '1 mon -1 days'::interval)
                        ORDER BY (to_date(concat(rkcp.tahun, '-', rkcp.bulan), 'YYYY-MM'::text) + '1 mon -1 days'::interval)");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("DROP VIEW IF EXISTS vw_tk_capex");
        DB::commit();
    }
}
