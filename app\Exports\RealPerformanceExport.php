<?php

namespace App\Exports;

use Illuminate\View\View;
use App\Models\RealPerformance;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class RealPerformanceExport implements
    FromCollection,
    // FromView,
    WithHeadings,
    WithEvents,
    WithColumnWidths,
    WithTitle,
    WithStyles
{
    protected $data;

    // Expot data with collection
    public function __construct($data)
    {
        $this->data = $data;
    }

    // Expot data with collection
    public function collection()
    {
        return collect($this->data);
    }

    public function headings(): array
    {
        return [
            'OPCO',
            'PLANT',
            'Date',
            'Oph',
            'UPDT',
            'PDT',
            'Stop Idle',
            'Fy Stop',
            'Freq Updt',
            'Net. Avail',
            'RKAP (TON/DAY)',
            'Koreksi',
            'Act. Prod. (ton)',
            'Act.Idle Prod. (ton)',
            'Rate Gross (ton/day)',
            'Rate Netto (ton/day)'
        ];
    }

    public function registerEvents(): array
    {
        return [

            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet
                    ->getDelegate()
                    ->getStyle('A1:P1')
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('C4D79B');
                $event->sheet->getDelegate()->freezePane('A2');
            },

        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 15,
            'C' => 15,
            'D' => 15,
            'E' => 15,
            'F' => 15,
            'G' => 15,
            'H' => 15,
            'I' => 15,
            'J' => 30,
            'K' => 20,
            'L' => 20,
            'M' => 20,
            'N' => 20,
            'O' => 20,
            'P' => 20,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Data Realisasi Performance';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => [
                    'name' => 'Times New Roman',
                    'bold' => true,
                    'size' => 12,
                ]
            ],
        ];
    }

    // Export data with view
    // public function view(): View
    // {
    //     return view('exports.realPerformance', [
    //         'data' => RealPerformance::all()
    //     ]);
    // }
}
