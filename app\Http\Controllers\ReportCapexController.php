<?php

namespace App\Http\Controllers;

use App\Http\Requests\ReportCapexRequest;
use App\Models\ReportCapex;
use App\Models\Company;
use App\Models\CostcenterStructure;
use App\Models\KoorBudget;
use App\Models\InvestType;
use App\Models\FocusStrategy;
use App\Models\OperasionalCapex;

use DataTables;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\Routes;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Matrix\Operators\Operator;

class ReportCapexController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // $data['company'] = 'berhasil';
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        
        $data['invesment_status'] = OperasionalCapex::get();
        $data['company'] = Company::get();
        // $data['costcenterstructure'] = OperasionalCapex::select('id', 'directorat', 'pic')->get();
        $data['koorbudget'] = KoorBudget::get();
        $data['investtype'] = InvestType::get();
        $data['level'] = OperasionalCapex::get();
        $data['focusstrategy'] = FocusStrategy::get();

        // dd($data);
        // return $data;
        return view('ReportCapex', $data);
    }

    public function datatables(Request $request)
    {
        // $company = (!empty($_GET["company_id"])) ? ($_GET["company_id"]) : ('');

        $query    = OperasionalCapex::join('m_company', 'operasional_capex.company_id', '=', 'm_company.id')->get();
        dd($query);
        // if($company){         
        //     $query = OperasionalCapex::where('company_id', $company)->get();
        // }else{
        //     $query = OperasionalCapex::get();
        // }

        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
 
 
    }

}

