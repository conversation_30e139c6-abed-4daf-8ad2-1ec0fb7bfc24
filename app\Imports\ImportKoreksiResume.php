<?php

namespace App\Imports;

use App\Models\RKAPCapex;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
class ImportKoreksiResume implements ToCollection, WithHeadingRow
{
    public $data;

    public function collection(Collection $rows)
    {
        $data = [];
        foreach($rows as $row){
                if(is_int($row['tanggal'])){
                    // kondisi data jika tanggal format exel
                    $row['tanggal'] = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row['tanggal'])->format('Y-m-d');
                    // dd($row['tanggal']);
                }else{
                    // kondisi data jika tanggal format database atau string
                    $row['tanggal'] = \Carbon\Carbon::createFromFormat('d/m/Y', $row['tanggal'])->format('Y-m-d');
                }
                $data[] = $row;

        }
        $this->data = $data;
    }
    
    public function rules(): array
    {
        return[
            'kode_plant' => ['required', 'string'],
            'tanggal' => ['required'],
            'problem'    => ['required', 'numeric'],
            'oph'    => ['required', 'numeric'],
            'updt'    => ['required', 'numeric'],
            'pdt'    => ['required', 'numeric'],
            'stop_idle'    => ['required', 'numeric'],
            'frek_updt'    => ['required', 'numeric']
        ];
    }
}
