<?php

namespace App\Imports;

use App\Models\InputOpex;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ImportOpex implements ToModel, WithHeadingRow
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        return new InputOpex([
            'fm_area' => $row['fm_area'],
            'fiscal_year' => $row['fiscal_year'], 
            'funds_center' => $row['funds_center'], 
            'commitment_itm' => $row['commitment_itm'], 
            'amount' => $row['amount'],
            'status' => '0',  
        ]);
    }
}
