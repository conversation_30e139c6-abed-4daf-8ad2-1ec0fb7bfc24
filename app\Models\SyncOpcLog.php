<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SyncOpcLog extends Model
{
    protected $fillable = [
        'sync_opc_config_id',
        'year',
        'month',
        'status',
        'note',
        'url',
        'parameter',
        'config_name',
        'created_at',
        'updated_at'
    ];

    public static function statuses()
    {
        return [
            'success' => 'Success',
            'process' => 'Process',
            'fail' => 'Fail'
        ];
    }

    public function config()
    {
        return $this->belongsTo(SyncOpcConfig::class, 'sync_opc_config_id', 'id');
    }
}
