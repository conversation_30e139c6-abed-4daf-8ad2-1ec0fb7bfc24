<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

use DB;

class OperasionalCapex extends Model
{
    protected $table = 'operasional_capex';

    protected $fillable = [     
        'company',   
        'type_investment',
        'profile_projects',
        'coor_invest',
        'asset_code',
        'project_inisiator',
        'cost_center',
        'investment_name',
        'existing_condition',
        'capex_success_indicator',
        'detail_scope',
        'priority',
        'year',
        'intiator',
        'kadept_initiator',
        'focus_strategy',
        'risk_if_not_done',
        'invest_group',
        'start_date',
        'end_date',
        'investment',
        'consequences',
        'likelihood',
        'level',
        'invesment_status',
        // 'planning_commitment',
        // 'po',
        'next_year_po_1',
        'next_year_po_2',
        'next_year_po_3',
        // 'planning_good_receipt',
        // 'gr',
        'no_equipment',
        'depreiasi',
        'quantity',
        'nilai_barang_dan_jasa_import',
        'nilai_barang_lokal',
        'nilai_jasa',
        'eng_mngmt_cost',
        'total_investment',
        // 'komponen_tkdn',
        'benefit_analysis',
        // 'similar_capex_reference',
        // 'wbs',
        'supporting_documents',
        'price_list_OEM',
        'engineering_judgement',
        'critical_part',
    ];
}
