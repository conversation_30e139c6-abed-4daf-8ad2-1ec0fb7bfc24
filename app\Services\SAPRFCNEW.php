<?php
namespace App\Services;
ini_set('max_execution_time', -1); //3 minutes
ini_set("memory_limit", "-1");

use SAPNWRFC\Connection as SapConnection;
use SAPNWRFC\Exception as SapException;

use phpsap\classes\Config\ConfigTypeA;
use phpsap\DateTime\SapDateTime;
use phpsap\saprfc\SapRfc;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;

use DateTime;

class SAPRFCNEW
{
    function configSAP($type = 'SIG')
    {        
        //LPS
        if($type == 'LPS'){
            $config = [
                'ashost' => '*************',
                'sysnr'  => '00',
                'client' => '110',
                'user' => 'sapsupport',
                'passwd' => '2022Juli!',
            ];
        }else{
            //SIG
            $config = [
                'ashost' => '**********',
                'sysnr'  => '00',
                'client' => '030',
                'user' => 'PROJDMM02',
                'passwd' => '$inergi123',
            ];
        }
        return $config;
    }

    function getDataExample()
    {  
        $result = (new SapRfc(
        'ZFM_BPC_GET_FBL3N',
        [
            'XOPSEL' => 'X',
            'R_SAKNR' => [],
            'R_BUKRS'  => [[
                "SIGN" => "I",
                "OPTION" => "EQ",
                "LOW" => "2000",
                "HIGH" => ""
            ]],
            'R_BUDAT'  => [[
                "SIGN" => "I",
                "OPTION" => "BT",
                "LOW" => "********",
                "HIGH" => "20221201"
            ]]
        ],
        new ConfigTypeA([
            ConfigTypeA::JSON_ASHOST => '**********',
            ConfigTypeA::JSON_SYSNR  => '00',
            ConfigTypeA::JSON_CLIENT => '030',
            ConfigTypeA::JSON_USER   => 'ANDRE',
            ConfigTypeA::JSON_PASSWD => 'sisi2015'
        ])
      ))->invoke();
      //The output array contains a DateTime object.
      return $result['T_DATA'];
      exit;
    }

    function getData($param= array())
    {       
        // if($company != 'ALL'){            
        //     $comp[] = array(            
        //         "SIGN" => "I",
        //         "OPTION" => "EQ",
        //         "LOW" => strval($company),
        //         "HIGH" => ""
        //     );
        // }else{     
        //     $comp[] = array(            
        //         "SIGN" => "I",
        //         "OPTION" => "BT",
        //         "LOW" => "0000",
        //         "HIGH" => "9999"
        //     );
        // }

        // if($account != 'ALL'){            
        //     $acc[] = array(            
        //         "SIGN" => "I",
        //         "OPTION" => "EQ",
        //         "LOW" => strval($account),
        //         "HIGH" => ""
        //     );
        // }else{                      
        //     $acc = [];
        // }

        // $sd = date('Ymd', strtotime($start_date));
        // $ed = date('Ymd', strtotime($end_date));
        $tgl[] = array(            
            "SIGN" => "I",
            "OPTION" => "BT",
            "LOW" => "********",
            "HIGH" => "********"
        );
        
        $param = [
            //Project
            'R_PSPID' => [array(
                    "SIGN"      =>  "I",
                    "OPTION"    =>  "EQ",
                    "LOW"       =>  "P3-10003",
                    "HIGH"      =>  ""      
            )],
            //date 
            'R_BUDAT' => [array(
                "SIGN"      =>  "I",
                "OPTION"    =>  "EQ",
                "LOW"       =>  "********",
                "HIGH"      =>  "********"
                )]
        ];
        
        $config = $this->configSAP();
        
        // print_r($config);die;
        try {
            $options = [
                'rtrim' => true
            ];
            $sap = new SapConnection($config);
            $function=$sap->getFunction("Z_CPS_CJI3_FM");
            $result = $function->invoke($param,$options);
            return $result['T_DATA'];
        } catch(SapException $fault) {
            // Log::channel('syslog')->debug('Caught SoapFault :' . $fault->getMessage());
            $response['status'] = false;
            $response['messages'] = $fault->getMessage();
            return $response;
        }

        // return $output;
    }

    function postDataJurnal($data)
    {   
        $head = $data['posting'];
        $DOC_DATE = date('Ymd', strtotime($head->documet_date));
        $PSTNG_DATE = date('Ymd', strtotime($head->posting_date));
        $TRANS_DATE = date('Ymd', strtotime($head->posting_date));
        $DOCUMENTHEADER = array(    
            "OBJ_TYPE" => "",
            "OBJ_KEY" => "",
            "OBJ_SYS" => "",
            "USERNAME" => "JOKO",
            "HEADER_TXT" => "$head->doc_header",
            "OBJ_KEY_R" => "",
            "COMP_CODE" => "$head->company_code",
            "AC_DOC_NO" => "",
            "FISC_YEAR" => "$head->fiscal_year",
            "DOC_DATE" => "$DOC_DATE",
            "PSTNG_DATE" => "$PSTNG_DATE",
            "TRANS_DATE" => "$TRANS_DATE",
            "FIS_PERIOD" => "$head->periode",
            "DOC_TYPE" => "$head->document_type",
            "REF_DOC_NO" => "",
            "COMPO_ACC" => "",
            "REASON_REV" => "",
            "REF_DOC_NO_LONG" => "",
            "ACC_PRINCIPLE" => ""
        );
        foreach($data['detail'] as $row){
            $ITEMNO_ACC = str_pad($row->posting_key,10,0,STR_PAD_LEFT);
            $GL_ACCOUNT = str_pad($row->gl_account,10,0,STR_PAD_LEFT);
            $type_pk = 'debit';
            foreach($data['post_key'] as $row_pk){
                if($row_pk->postkey_code == $row->posting_key){
                    $type_pk = $row_pk->postkey_type;
                }
            }
            $minus = $type_pk == 'debit' ? 1 : -1;
            $ACCOUNTGL[] = array( 
                "ITEMNO_ACC" => "$ITEMNO_ACC",
                "GL_ACCOUNT" => "$GL_ACCOUNT",
                "COMP_CODE" => "$head->company_code",
                "PSTNG_DATE" => "$PSTNG_DATE",
                "DOC_TYPE" => "",
                'AC_DOC_NO' => '',	
                'FISC_YEAR' => "$head->fiscal_year",	
                'FIS_PERIOD' => "$head->periode",	
                'STAT_CON' => '',	
                'REF_KEY_1' => '',	
                'REF_KEY_2' => '',	
                'REF_KEY_3' => '',	
                'CUSTOMER' => '',	
                'VENDOR_NO' => '',	
                'ALLOC_NMBR' => '',	
                'ITEM_TEXT' => '',	
                'BUS_AREA' => '',	
                'COSTCENTER' => '',	
                'ACTTYPE' => '',	
                'ORDERID' => '',	
                'ORIG_GROUP' => '',	
                'COST_OBJ' => '',	
                'PROFIT_CTR' => '',	
                'PART_PRCTR' => '',	
                'WBS_ELEMENT' => '',	
                'NETWORK' => '',	
                'ROUTING_NO' => '',	
                'ORDER_ITNO' => '',	
                'ACTIVITY' => '',	
                'PLANT' => '',	
                'SALES_ORD' => '',	
                'S_ORD_ITEM' => '',	
                'SEGMENT' => '',	
                'PARTNER_SEGMENT' => ''
            );
            $CURRENCYAMOUNT[] = array(    
                'ITEMNO_ACC' => "$ITEMNO_ACC",	
                'CURR_TYPE' => '',	
                'CURRENCY' => "$head->currency",	
                'CURRENCY_ISO' => '',	
                'AMT_DOCCUR' => ($row->amount * $minus),	
                'EXCH_RATE' => 0,	
                'EXCH_RATE_V' => 0
            );
        }
        
        $param = [
            'DOCUMENTHEADER' => $DOCUMENTHEADER,
            'ACCOUNTGL'  => $ACCOUNTGL,
            'CURRENCYAMOUNT'  => $CURRENCYAMOUNT
        ];

        // echo '<pre>';
        // print_r($param);
        // echo '</pre>';
        // exit;

        $config = $this->configSAP();
        try {
            $sap = new SapConnection($config);

            $function=$sap->getFunction("ZFM_BAPI_GL_POSTING");
            $result = $function->invoke($param);

            if (isset($result['RETURN'])) {                
                $dataItem = $result['RETURN'];
                $return = array();
                $no_doc = '-';
                $pesan = '';     
                $type = '';     
                if(count($dataItem) == 1)  {
                    // $pesan += strval($dataItem['MESSAGE']).',';
                    $no_doc = substr($dataItem[0]['MESSAGE_V2'],0,10);
                    $return = array(
                        'doc_no_sap' => $no_doc,
                        'ket_sap' => $dataItem[0]['MESSAGE'],
                        'type_sap' => 'S',
                        'status_post' => 'berhasil'
                    );
                }else{
                    $sts = 'gagal';
                    foreach ($dataItem as $k => $item) {
                        if($item['TYPE'] != 'E'){ //Warning  
                            if($k == 0 && $item['TYPE'] == 'S'){
                                $no_doc = substr($item['MESSAGE_V2'],0,10);  
                            }   
                            $type = 'W';             
                            if($no_doc!="-"){
                                $type = 'S'; 
                                $sts = 'berhasil';
                            }
                            $pesan .= $item['MESSAGE']." (".$item['TYPE'].") ".',';
                        }else{            
                            $type = 'E';
                            if($item['MESSAGE_V1'] != 'BKPFF'){
                                $pesan .= trim($item['MESSAGE'])." (".$type.") ".',';
                            }
                            $sts = 'gagal';
                        }
                    }  

                    $return = array(
                        'doc_no_sap' => $no_doc,
                        'ket_sap' => $pesan,
                        'type_sap' => $type,
                        'status_post' => $sts
                    );
                }            
            }
            return $return;
        } catch(SapException $fault) {
            // Log::channel('syslog')->debug('Caught SoapFault :' . $fault->getMessage());
            $response['status'] = false;
            $response['messages'] = $fault->getMessage();
            return $response;
        }

        // return $output;
    }

    function getData_SPPD()
    {
        // $sap = new SAPConnection();
        // $sap->Connect("login_LSP.conf");
        // if ($sap->GetStatus() == SAPRFC_OK)
        //     $sap->Open();
        // if ($sap->GetStatus() != SAPRFC_OK) {
        //     echo $sap->PrintStatus();
        //     exit;
        // }
        // $fce = $sap->NewFunction("ZFM_GET_SURATTUGAS"); //RFC name

        // if ($fce == false) {
        //     $sap->PrintStatus();
        //     exit;
        // }

        // $fce->I_PBUKR = $va . "000";
        // $fce->I_GJAHR = $thn;
        // //project
        // foreach ($dt as $ky3 => $va3) {//PROJECT CODE
        //     $fce->T_PSPHI->row["SIGN"] = "I";
        //     $fce->T_PSPHI->row["OPTION"] = "EQ"; //BT = BETWEEN || EQ = EQUALES
        //     $fce->T_PSPHI->row["LOW"] = $va3;
        //     $fce->T_PSPHI->row["HIGH"] = NULL;
        //     $fce->T_PSPHI->Append($fce->T_PSPHI->row);
        // }

        // $fce->call();
        // if ($fce->GetStatus() == SAPRFC_OK) {
        //     $fce->RETURN_DATA->Reset();
        //     $berhasil = $gagal = $total = 0;
        //     while ($fce->RETURN_DATA->Next()) {
        //         $dt = $fce->RETURN_DATA->row;
        //         //Memindahkan minus dari belakang ke depan
        //         foreach ($dt as $key => $value) {
        //             if (substr_count($value, '-') == 1 && substr($value, -1) == '-') {
        //                 $dt[$key] = '-' . str_replace("-", "", $value);
        //             }
        //         }
        //         $get['LEVEL1'] = $dt['LEVEL'];
        //         $get['CURR_PROJECT'] = $dt['CURR_PROJECT_C'];
        //         $get['WBS'] = $dt['WBS_C'];
        //         $get['DESCRIPTION'] = str_replace("'", " ", $dt['DESC']);
        //         $get['COMPANY'] = $dt['COMPANY_CODE'];
        //         $get['RS_COSTCENTER'] = $dt['RS_COST'];
        //         $get['ASSIGMENT_COST'] = $dt['ASSIG_COST'];
        //         $get['CREAT_ON'] = $dt['CREAT_ON'];
        //         $get['PLANT'] = $dt['PLANT'];
        //         $get['INV_REASON'] = $dt['INV_REASON'];
        //         $get['PR_RESPON'] = $dt['PR_RESPON'];
        //         $get['PSPRI'] = $dt['PSPRI'];
        //         $get['PRCTR'] = $dt['PRCTR'];
        //         $get['ACTUAL'] = $dt['ACTUAL'];
        //         $get['BUDGET'] = $dt['BUDGET'];
        //         $get['COMMITED'] = $dt['COMMIT'];
        //         $get['AVAILABLE'] = $dt['AVAIL'];
        //         $get['RMORP'] = $dt['RMORP'];
        //         $get['GJAHR'] = $dt['GJAHR'];

        //         $keys = implode(", ", array_keys($get));
        //         $values = implode("', '", $get);

        //         $ins = "MERGE INTO CAPEX_SAP USING dual ON (
        //                 COMPANY = '" . $get['COMPANY'] . "' AND
        //                 WBS = '" . $get['WBS'] . "' AND
        //                 GJAHR = '" . $get['GJAHR'] . "'
        //             )
        //             WHEN MATCHED THEN UPDATE SET
        //                 LEVEL1 = '" . $get['LEVEL1'] . "',
        //                 CURR_PROJECT = '" . $get['CURR_PROJECT'] . "',
        //                 DESCRIPTION = '" . $get['DESCRIPTION'] . "',
        //                 RS_COSTCENTER = '" . $get['RS_COSTCENTER'] . "',
        //                 ASSIGMENT_COST = '" . $get['ASSIGMENT_COST'] . "',
        //                 CREAT_ON = '" . $get['CREAT_ON'] . "',
        //                 PLANT = '" . $get['PLANT'] . "',
        //                 INV_REASON = '" . $get['INV_REASON'] . "',
        //                 PR_RESPON = '" . $get['PR_RESPON'] . "',
        //                 PSPRI = '" . $get['PSPRI'] . "',
        //                 PRCTR = '" . $get['PRCTR'] . "',
        //                 ACTUAL = '" . $get['ACTUAL'] . "',
        //                 BUDGET = '" . $get['BUDGET'] . "',
        //                 COMMITED = '" . $get['COMMITED'] . "',
        //                 AVAILABLE = '" . $get['AVAILABLE'] . "',
        //                 RMORP = '" . $get['RMORP'] . "',
        //                 CREATE_TIME = TO_DATE('" . date('Ymd h:i:s') . "', 'yyyymmdd HH24:MI:SS')
        //             WHEN NOT MATCHED THEN
        //             INSERT ($keys, CREATE_TIME) VALUES ('$values', TO_DATE('" . date('Ymd h:i:s') . "', 'yyyymmdd HH24:MI:SS'))";
        //         $q = oci_parse($conn, $ins);

        //         if (oci_execute($q)) {
        //             $berhasil++;
        //         } else {
        //             $gagal++;
        //             echo "<br>" . $ins . "<br><br>";
        //         }
        //         $total++;
        //     }

        //     $log1 = 'COMPANY PROSES PADA ' . $va . '000  || ' . $thn . ' || ' . $project . ' => '
        //             . 'Tgl Selesai = ' . date('Y.m.d') . ' | '
        //             . 'Jam Selesai = ' . date("h:i:sa") . ' | '
        //             . 'Total Data = ' . $total . ' | '
        //             . 'Inserts Berhasil = ' . $berhasil . ' | '
        //             . 'Insert Gagal = ' . $gagal;

        //     echo "<br>COMPANY PROSES PADA " . $va . "000  || $project => " . $log1;
        //     $filename = 'M_CAPEX_SAP';
        //     //echo file_put_contents('log/' . $filename . '.txt', $log1 . "\r\n", FILE_APPEND | LOCK_EX);
        // }
    }

}