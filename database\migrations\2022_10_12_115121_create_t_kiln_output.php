<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTKilnOutput extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('t_kiln_output', function (Blueprint $table) {
            $table->bigIncrements('id_kiln_output');
            $table->string('kode_plant', 30);
            $table->timestamp('tanggal')->nullable()->default(NULL);
            $table->unsignedBigInteger('produksi_output')->nullable();
            $table->string('source_system', 30);
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->string('deleted_by', 30)->nullable();
            $table->softDeletes();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('t_kiln_output');
    }
}
