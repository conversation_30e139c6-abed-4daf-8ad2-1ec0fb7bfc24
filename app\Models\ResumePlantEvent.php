<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ResumePlantEvent extends Model
{
    use SoftDeletes;
    
    protected $table = 'ts_resume';
    protected $primaryKey = 'id_resume';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'kode_opco',
        'no_plant',
        'kode_plant',
        'tanggal',
        'alasan',
        'id_kategori',
        'nama_kategori',
        'oph',
        'updt',
        'pdt',
        'stop_idle',
        'total',
        'frek',
        'sort'
    ];
}
