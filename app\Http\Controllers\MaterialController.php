<?php

namespace App\Http\Controllers;

use App\Http\Requests\MaterialRequest;
use App\Models\Company;
use App\Models\Plant;

use DataTables;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\Routes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class MaterialController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        $data['company'] = Company::select('id', 'company', 'description')->get();
        $data['plant'] = Plant::select('id', 'plant')->get();
        
        //return 
        // return $data;
        return view('Material', $data);
    }

    public function datatables(Request $request)
    {
        $query    = Material::get();
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $material = Material::create([
            'kodematerial' => $request->kodematerial,
            'name' => $request->name,
            'description' => $request->description,
            'material_type' => $request->material_type,
            'parenth' => $request->parenth,
            'valuation_class' => $request->valuation_class,
            'company' => $request->company,
            'plant' => $request->plant,
        ]);
        $response = responseSuccess(trans('message.read-success'),$material);
        return response()->json($response,200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($material)
    {
        $query   = Material::find($material);
        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }
    

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $query   = Material::find($id);
        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Material $request, $id)
    {
        $data = $this->findDataWhere(Material::class, ['id' => $id]);

        //   dd($data);exit();
        DB::beginTransaction();
        try {
            $data->update([
                'kodematerial' => $request->kodematerial,
                'name' => $request->name,
                'description' => $request->description,
                'material_type' => $request->material_type,
                'parenth' => $request->parenth,
                'valuation_class' => $request->valuation_class,
                'company' => $request->company,
                'plant' => $request->plant,
            ]);
            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
            } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        Material::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }
}
