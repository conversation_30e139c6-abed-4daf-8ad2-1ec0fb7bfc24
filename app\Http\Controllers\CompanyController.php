<?php

namespace App\Http\Controllers;

use App\Http\Requests\CompanyRequest;
use App\Models\Opco;
use DataTables;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\Routes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class CompanyController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Company',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'/company',
                ],
                [
                    'title'=>'Company',
                    'url'=>'',
                ]
            ],
        ];
        // $query = new Company();
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        // $parenth1 = Opco::whereNull('parenth1')->get();
        // $parenth2 = Opco::whereNotNull('parenth1')->whereNull('parenth2')->get();
        // $data['parenth1'] = $parenth1;
        // $data['parenth2'] = $parenth2;

        return view('company', $data);
    }

    public function datatables(Request $request)
    {
        $query    = Opco::orderBy('no_pbi')->get();

        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $rules = [
            'kode_opco'     => 'required|unique:m_opco',
            'nama_opco'     => 'required|unique:m_opco',
            'holding'       => 'required',
            'reference_sap' => 'required',
            'status'        => 'required',
        ];
        $messages =[
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
        ];
        $this->validate($request, $rules, $messages);
        try {
            $company = Opco::create([
                'kode_opco'     => $request->kode_opco,
                'nama_opco'     => $request->nama_opco,
                'holding'       => $request->holding,
                'reference_sap' => $request->reference_sap,
                'status'        => $request->status,
            ]);
            $response = responseSuccess(trans('messages.create-success'), $company);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($company)
    {

        $query   = Opco::find($company);
        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $query   = Opco::find($id);
        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update($id, Request $request)
    {
        $data = $this->findDataWhere(Opco::class, ['id' => $id]);

        $rules = [
            'kode_opco'     => 'required',
            'nama_opco'     => 'required',
            'holding'       => 'required',
            'reference_sap' => 'required',
            'status'        => 'required',
        ];
        $messages =[
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
        ];
        $this->validate($request, $rules, $messages);

        DB::beginTransaction();
        try {
            $data->update([
                'kode_opco'     => $request->kode_opco,
                'nama_opco'     => $request->nama_opco,
                'holding'       => $request->holding,
                'reference_sap' => $request->reference_sap,
                'status'        => $request->status,
            ]);
            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function destroy($id)
    {
        Opco::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }
}
