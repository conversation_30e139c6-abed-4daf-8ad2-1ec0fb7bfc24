<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTkBdp extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tk_bdp', function (Blueprint $table) {
            $table->bigIncrements('id_koreksi_bdp');
            $table->integer('no', false,false)->length(4)->nullable();
            $table->string('kode_plant', 30);
            $table->string('tahun', 4);
            $table->decimal('bdp_sig',10, 2)->nullable();
            $table->decimal('bdp_ghopo',10, 2)->nullable();
            $table->decimal('bdp_sg',10, 2)->nullable();
            $table->decimal('bdp_sp',10, 2)->nullable();
            $table->decimal('bdp_st',10, 2)->nullable();
            $table->decimal('bdp_sbi',10, 2)->nullable();
            $table->decimal('bdp_tlcc',10, 2)->nullable();
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tk_bdp');
    }
}
