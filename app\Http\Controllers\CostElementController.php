<?php

namespace App\Http\Controllers;

use App\Exports\ExportCostElement;
use App\Imports\ImportCostElement;
use App\Models\CostElement;
use DataTables;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class CostElementController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Cost Element',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'/cost_element',
                ],
                [
                    'title'=>'Cost Element',
                    'url'=>'',
                ]
            ],
        ];
        return view('master.cost_element', $data);
    }

    public function importCostElement()
    {
        $data = [
            'title' => 'Import Cost Element',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'/cost_element',
                ],
                [
                    'title'=>'Cost Element',
                    'url'=>'/cost-element-import',
                ],
                [
                    'title'=>'Import Cost Element',
                    'url'=>'',
                ]
            ],
        ];
        $cost_element = [
            [
                "id"=>'BAHAN_BAKAR',
                "name"=>'BAHAN BAKAR'
            ],
            [
                "id"=>'BAHAN_BAKU',
                "name"=>'BAHAN BAKU'
            ],
            [
                "id"=>'LISTRIK',
                "name"=>'LISTRIK'
            ],
            [
                "id"=>'TENAGA_KERJA',
                "name"=>'TENAGA KERJA'
            ],
            [
                "id"=>'MAINTENANCE',
                "name"=>'MAINTENANCE'
            ],
            [
                "id"=>'PENYUSUTAN',
                "name"=>'PENYUSUTAN'
            ],
            [
                "id"=>'ADMINISTRASI_UMUM',
                "name"=>'ADMINISTRASI UMUM'
            ],
            [
                "id"=>'PAJAK_ASURANSI',
                "name"=>'PAJAK ASURANSI'
            ],
            [
                "id"=>'ELIM_BAHAN_BAKU',
                "name"=>'ELIM BAHAN BAKU'
            ],
            [
                "id"=>'ELIM_PENYUSUTAN',
                "name"=>'ELIM PENYUSUTAN'
            ],
            [
                "id"=>'ELIM_ADMINISTRASI_UMUM',
                "name"=>'ELIM ADMINISTRASI UMUM'
            ]

        ];
        $opt='';
        foreach ($cost_element as $value) {
            $id = $value['id'];
            $nama = $value['name'];
            $opt .= "<option value='$id'>$nama</option>";
        }
        $data['group_biaya'] = $opt;

        $opco_all = [
            [
                "id"=>'SBI',
                "name"=>'SBI'
            ],
            [
                "id"=>'NON SBI',
                "name"=>'NON SBI'
            ]
        ];
        $opt_opco='';
        foreach ($opco_all as $value) {
            $id_opco = $value['id'];
            $nama_opco = $value['name'];
            $opt_opco .= "<option value='$id_opco'>$nama_opco</option>";
        }
        $data['opco'] = $opt_opco;

        return view('costelementImport', $data);
    }

    public function temp()
    {
        $fileName = 'Template Cost Element.xlsx';
        return Excel::download(new ExportCostElement, $fileName);
    }

    public function import(Request $request)
    {
        // validasi
        $this->validate($request, [
            'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author
        ]);

        // menangkap file excel
        $file = $request->file('excel');

        // import data
        $import = new ImportCostElement;
        Excel::import($import, $file);

        $datas = ($import->data);
        $result = [];
        foreach ($datas as $data) {
            $format = $data;
            $message = "";
            $status = "Valid";

            if ($data['nomer_cost_element'] == NULL || $data['nomer_cost_element'] == "") {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom Cost Element tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom Cost Element tidak boleh kosong";
                }
            }

            if ($data['nama_cost_element'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom cost element name tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom cost element name tidak boleh kosong";
                }
            }

            if ($data['group_cost_element'] == NULL || $data['group_cost_element'] == "") {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom cost element group tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom cost element group tidak boleh kosong";
                }
            }

            if ($data['group_biaya'] == NULL || $data['group_biaya'] == "") {
                $status = 'Invalid';
                $message = $message . "Kolom biaya group tidak boleh kosong ";
            } else if ($data['group_biaya'] == "BAHAN_BAKAR" ) {
                $status = "Valid";
            }else if ($data['group_biaya'] == "BAHAN_BAKU") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "LISTRIK") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "TENAGA_KERJA") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "MAINTENANCE") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "PENYUSUTAN") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "ADMINISTRASI_UMUM") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "PAJAK_ASURANSI") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "ELIM_BAHAN_BAKU") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "ELIM_PENYUSUTAN") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "ELIM_ADMINISTRASI_UMUM") {
                $status = "Valid";
            }else{
                $status = "Invalid";
                $message = $message . "Kolom biaya group tidak ditemukan";
            }

            if ($data['opco'] == NULL || $data['opco'] == "") {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom cost element name tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom cost element name tidak boleh kosong";
                }
            }else if ($data['opco'] == "SBI") {
                $status = "Valid";
            }else if ($data['opco'] == "NON SBI") {
                $status = "Valid";
            }else{
                $status = "Invalid";
                $message = $message . "Kolom opco tidak ditemukan";
            }

            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function saveData(Request $request)
    {
        $excel = json_decode($request->excel);
        $opco = null;
        foreach ($excel as $item) {
            if ($excel == "SBI") {
                $opco = '7001';
            }
            CostElement::create([
                'cost_element' => $item[1],
                'cost_element_name' => $item[2],
                'cost_element_group' => $item[3],
                'biaya_group' => $item[4],
                'kode_opco' => $opco,
                'create_by' => Auth::user()->username,
            ]);
        }
        $response = responseSuccess('Data added successfully');
        return response()->json($response,200);
    }

    public function datatables(Request $request)
    {
        $query    = CostElement::get();

        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function insertData(Request $request)
    {
        $excel = json_decode($request->excel);
        $result = [];
        $success = true;
        //list opco
        foreach ($excel as $data) {
            $oldData = $data;
            $data['no'] = $data[0];
            $data['nomer_cost_element'] = $data[1];
            $data['nama_cost_element'] = $data[2];
            $data['group_cost_element'] = $data[3];
            $data['group_biaya'] = $data[4];
            $data['opco'] = $data[5];
            $data['is_valid'] = $data[6];
            $data = array_diff_key($data, $oldData);
            $format = $data;
            $message = "";
            $status = "Valid";

            if ($data['nomer_cost_element'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom nomer Cost Element tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom nomer Cost Element tidak boleh kosong";
                }
            }

            if ($data['nama_cost_element'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom nama cost element name tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom nama cost element name tidak boleh kosong";
                }
            }

            if ($data['group_cost_element'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom cost element group tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom cost element group tidak boleh kosong";
                }
            }

            if ($data['group_biaya'] == NULL) {
                $status = 'Invalid';
                $message = $message . "Kolom biaya group tidak boleh kosong ";
            } else if ($data['group_biaya'] == "BAHAN_BAKAR" ) {
                $status = "Valid";
            }else if ($data['group_biaya'] == "BAHAN_BAKU") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "LISTRIK") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "TENAGA_KERJA") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "MAINTENANCE") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "PENYUSUTAN") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "ADMINISTRASI_UMUM") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "PAJAK_ASURANSI") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "ELIM_BAHAN_BAKU") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "ELIM_PENYUSUTAN") {
                $status = "Valid";
            }else if ($data['group_biaya'] == "ELIM_ADMINISTRASI_UMUM") {
                $status = "Valid";
            }else{
                $status = "Invalid";
                $message = $message . "Kolom biaya group tidak ditemukan";
            }

            if ($data['opco'] == NULL || $data['opco'] == "") {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom cost element name tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom cost element name tidak boleh kosong";
                }
            }else if ($data['opco'] == "SBI") {
                $status = "Valid";
            }else if ($data['opco'] == "NON SBI") {
                $status = "Valid";
            }else{
                $status = "Invalid";
                $message = $message . "Kolom opco tidak ditemukan";
            }

            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function store(Request $request)
    {
        $username = Auth::user()->username;
        $request->validate([
            'cost_element' => 'required|max:30|unique:m_cost_element,cost_element,NULL,cost_element,deleted_at,NULL',
            'cost_element_name' => 'required|max:100',
            'cost_element_group' => 'required|max:30',
            'biaya_group' => 'required|max:30',
        ]);

        $opco = $request->kode_opco;
        if ($opco == '' ) {
            $request->merge([
                'kode_opco' => null,
            ]);
        }

        try {
            $model = CostElement::withTrashed()->find($request->cost_element);
            if ($model) {
                $model->update(array_merge($request->all(), ['update_by' => $username]));
                $model->restore();
            } else {
                $model = CostElement::create(array_merge($request->all(), ['create_by' => $username]));
            }

            $response = responseSuccess(trans('messages.create-success'), $model);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function show($cost)
    {
        $query   = CostElement::findOrFail($cost);
        $response = responseSuccess(trans('messages.read-success'), $query);
        return response()->json($response, 200);
    }

    public function edit($id)
    {
        $query   = CostElement::findOrFail($id);
        $response = responseSuccess(trans('messages.read-success'), $query);
        return response()->json($response, 200);
    }

    public function update($id, Request $request)
    {
        $model = CostElement::findOrFail($id);

        $request->validate([
            'cost_element' => 'required|max:30|unique:m_cost_element,cost_element,' . $id . ',cost_element',
            'cost_element_name' => 'required|max:100',
            'cost_element_group' => 'required|max:30',
            'biaya_group' => 'required|max:30',
        ]);

        $opco = $request->kode_opco;
        if ($opco == '' ) {
            $request->merge([
                'kode_opco' => null,
            ]);
        }

        try {
            $model->update($request->all());

            $response = responseSuccess(trans('messages.update-success'), $model);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.update-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }


    public function destroy($id)
    {
        $model = CostElement::findOrFail($id);

        try {
            $model->delete();
            $response = responseSuccess(trans('messages.delete-success'), $model);
            return response()->json($response, 200);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.delete-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }
}
