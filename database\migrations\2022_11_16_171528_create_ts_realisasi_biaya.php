<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTsRealisasiBiaya extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ts_realisasi_biaya', function (Blueprint $table) {
            $table->bigIncrements('id_realisasi_biaya');
            $table->string('kode_opco', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->float('bahan_bakar',8,5);
            $table->float('bahan_baku',8,5);
            $table->float('listrik',8,5);
            $table->float('tenaga_kerja',8,5);
            $table->float('pemeliharaan',8,5);
            $table->float('penyusutan',8,5);
            $table->float('administrasi_umum',8,5);
            $table->float('pajak_asuransi',8,5);
            $table->float('elim_bb',8,5);
            $table->float('elim_penyusutan',8,5);
            $table->float('elim_administrasi',8,5);
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();  
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ts_realisasi_biaya');
    }
}
