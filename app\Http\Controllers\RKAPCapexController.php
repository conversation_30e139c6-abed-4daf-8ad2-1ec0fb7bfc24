<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\RKAPCapex;
use App\Models\Opco;
use Illuminate\Support\Facades\DB;
use Auth;
use DataTables;

use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportTempRKAPCapex;
use App\Imports\ImportRKAPCapex;

class RKAPCapexController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'RKAP Capex',
            'breadcrumb' => [
                [
                    'title'=>'Input RKAP',
                    'url'=>'/rkap-capex',
                ],
                [
                    'title'=>'RKAP Capex',
                    'url'=>'',
                ]
            ],
        ];
        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow; $i++) { 
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];  
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);
        $optTahun ="";
        foreach ($tahun as $value) {
            $tahun = $value['tahun'];
            $selected = $value['selected'];
            if ($selected == true) {
                $optTahun .= "<option value=$tahun selected>$tahun</option>";
            }
            else{
                $optTahun .= "<option value=$tahun>$tahun</option>";
            }
        }
        $data['tahun'] = $optTahun;

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        // $bulan = DB::table('m_bulan')->select('kode_bulan', 'bln_english')->orderBy('kode_bulan')->get();
        $optBulan = "";
        for ($i=0; $i < count($months); $i++) { 
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $optBulan .= "<option value=$noBulan selected>$months[$i]</option>";
            } 
            else{
                $optBulan .= "<option value=$noBulan>$months[$i]</option>";
            }       
        }
        $data['bulan'] = $optBulan;
        return view('rkapCapex', $data);      
    }

    public function viewImport()
    {
        $data = [
            'title' => 'RKAP Capex',
            'breadcrumb' => [
                [
                    'title'=>'Input RKAP',
                    'url'=>'/rkap-capex',
                ],
                [
                    'title'=>'RKAP Capex',
                    'url'=>'/rkap-capex/view-import',
                ],
                [
                    'title'=>'Import Data',
                    'url'=>'',
                ]
            ],
        ];
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();
        $opt_opco = "";
        foreach ($opco as $value) {
            $kode_opco = $value['kode_opco'];
            $nama_opco = $value['nama_opco'];
            $opt_opco .= "<option value='$kode_opco'>$nama_opco</option>";
        }
        $data['opco'] = $opt_opco;
        return view('rkapCapexImport', $data);      
    }

    public function datatables(Request $request)
    {
        $rkap = RKAPCapex::select([
            'id_rkap_capex','rkap_capex.kode_opco','m_opco.holding','tahun','bln_indo','bln_english','rkap_capex',
            DB::raw("to_char(rkap_capex, '999G999G999G999G999') as new_rkap_capex")])
        ->join('m_opco', 'rkap_capex.kode_opco', '=', 'm_opco.kode_opco')
        ->join('m_bulan', 'bulan', '=', 'kode_bulan');
        if($request->filter_opco){
            $rkap = $rkap -> where('rkap_capex.kode_opco', $request->filter_opco);
        }
        if($request->filter_tahun){
            $rkap = $rkap -> where('tahun', $request->filter_tahun);
        }
        if($request->filter_bulan){
            $rkap = $rkap -> where('bulan', $request->filter_bulan);
        }
        if($request->filter_search){
            $filter = $request->filter_search;
            $rkap = $rkap -> where('rkap_capex.kode_opco', 'ilike','%'.$filter.'%')
            -> orWhere('tahun', 'ilike','%'.$filter.'%')
            -> orWhere('bln_indo', 'ilike','%'.$filter.'%')
            -> orWhere('bln_english', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_capex', 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_capex, '999G999G999G999G999')"), 'ilike','%'.$filter.'%');
        }
        $data     = DataTables::of($rkap)
        ->addIndexColumn()
        ->addColumn('action', function($row){
            $btn = '<button type="button" class="edits btn btn-sm btn-icon btn-outline-primary mr-2" title="Edit" data-toggle="tooltip" data-id="'.$row->id_rkap_capex.'" ><i class="fa fa-edit"></i></button>';
            $btn = $btn.'<button type="button" class="deletes btn btn-sm btn-icon btn-outline-danger" title="Delete" data-toggle="tooltip" data-id="'.$row->id_rkap_capex.'" ><i class="fa fa-trash"></i></button>';
            return $btn;
        })
        ->rawColumns(['action'])        
        ->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);        

    }

    public function store(Request $request)
    {
        $cekRkap = RKAPCapex::where('kode_opco', $request->kode_opco)
        ->where('tahun', $request->tahun)
        ->where('bulan', $request->bulan)->get()->toArray();
        if($cekRkap){
            $response = [
                'success' => false,
                'data' => $cekRkap
            ];
            return response()->json($response,200);    
        }
        $rkap = RKAPCapex::Create([
            'kode_opco' => $request->kode_opco,
            'tahun' => $request->tahun,
            'bulan' => $request->bulan,
            'rkap_capex' => $request->rkap_capex == null ? 0 : str_replace(".","",$request->rkap_capex),
            'create_by' => Auth::user()->username, //masih belum masuk
        ]);
        $response = responseSuccess('Data added successfully',$rkap);
        return response()->json($response,200);    
    }

    public function import(Request $request)
    {
		// validasi
		$this->validate($request, [
			'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author 
		]);

        // menangkap file excel
        $file = $request->file('excel');

        // import data
        $import = new ImportRKAPCapex;
        Excel::import($import, $file);
        //list opco
        $opco = Opco::select('kode_opco')->get()->toArray();        
        $arrOpco = [];
        foreach ($opco as $value) {
            array_push($arrOpco,$value['kode_opco']);
        }
        $datas = ($import->data);
        $result = [];
        foreach ($datas as $data) {
            $format = $data;
            $message = "";
            $status = "Valid";
            if($data['opco']==NULL){
                $status = 'Invalid';
                $message = $message . "Kolom opco tidak boleh kosong ";
            }
            else if(gettype($data['opco']) != 'string'){
                $status = 'Invalid';
                $message = $message . "Kolom opco hanya berisi huruf ";
            }
            else if(!in_array($data['opco'],$arrOpco)){
                $status = "Invalid";
                $message = $message . "Kolom opco tidak ditemukan";
            }
            if($data['tahun']==NULL){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom tahun tidak boleh kosong ";
                }
                else{
                    $message = $message . ", Kolom tahun tidak boleh kosong";
                }
            }
            else if(gettype($data['tahun']) != 'integer' ){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom tahun hanya berisi angka ";
                }
                else{
                    $message = $message . ", Kolom tahun hanya berisi angka";
                }            
            }
            if($data['bulan']==NULL){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom bulan tidak boleh kosong ";
                }
                else{
                    $message = $message . ", Kolom bulan tidak boleh kosong";
                }
            }
            else if(gettype($data['bulan']) != 'integer' ){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom bulan hanya berisi angka ";
                }
                else{
                    $message = $message . ", Kolom bulan hanya berisi angka";
                }            
            }
            if($data['rkap_capex']==NULL ){
                $format['rkap_capex'] = 0;
            }
            else if(gettype($data['rkap_capex']) != 'integer'){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom RKAP Capex hanya berisi angka ";
                }
                else{
                    $message = $message . ", Kolom RKAP Capex hanya berisi angka";
                }            
            }
            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT); 
    }

    public function insertData(Request $request){
        $excel = json_decode($request->excel);
        $result = [];
        $success = true;
        //list opco
        $opco = Opco::select('kode_opco')->get()->toArray();        
        $arrOpco = [];
        foreach ($opco as $value) {
            array_push($arrOpco,$value['kode_opco']);
        }
        foreach ($excel as $data) {
            $oldData = $data;
            $data['opco'] = $data[0];
            $data['tahun'] = $data[1];
            $data['bulan'] = $data[2];
            $data['rkap_capex'] = $data[3];
            $data['is_valid'] = $data[4];
            $data = array_diff_key($data, $oldData);
            $format = $data;
            $message = "";
            $status = "Valid";
            if($data['opco']==NULL){
                $status = 'Invalid';
                $success = false;
                $message = $message . "Kolom opco tidak boleh kosong ";
            }
            else if(gettype($data['opco']) != 'string'){
                $status = 'Invalid';
                $success = false;
                $message = $message . "Kolom opco hanya berisi huruf ";
            }
            else if(!in_array($data['opco'],$arrOpco)){
                $status = "Invalid";
                $success = false;
                $message = $message . "Kolom opco tidak ditemukan";
            }
            if($data['tahun']==NULL){
                $status = 'Invalid';
                $success = false;
                if($message == ""){
                    $message = $message . "Kolom tahun tidak boleh kosong ";
                }
                else{
                    $message = $message . ", Kolom tahun tidak boleh kosong";
                }
            }
            else if(gettype($data['tahun']) != 'integer' ){
                $status = 'Invalid';
                $success = false;
                if($message == ""){
                    $message = $message . "Kolom tahun hanya berisi angka ";
                }
                else{
                    $message = $message . ", Kolom tahun hanya berisi angka";
                }            
            }
            if($data['bulan']==NULL){
                $status = 'Invalid';
                $success = false;
                if($message == ""){
                    $message = $message . "Kolom bulan tidak boleh kosong ";
                }
                else{
                    $message = $message . ", Kolom bulan tidak boleh kosong";
                }
            }
            else if(gettype($data['bulan']) != 'integer' ){
                $status = 'Invalid';
                $success = false;
                if($message == ""){
                    $message = $message . "Kolom bulan hanya berisi angka ";
                }
                else{
                    $message = $message . ", Kolom bulan hanya berisi angka";
                }            
            }
            if($data['rkap_capex']==NULL ){
                $format['rkap_capex'] = 0;
            }
            else if(gettype($data['rkap_capex']) != 'integer'){
                $status = 'Invalid';
                $success = false;
                if($message == ""){
                    $message = $message . "Kolom RKAP Capex hanya berisi angka ";
                }
                else{
                    $message = $message . ", Kolom RKAP Capex hanya berisi angka";
                }            
            }
            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);   
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);    
    }

    public function saveData(Request $request)
    {
        $excel = json_decode($request->excel);
        foreach ($excel as $item) {
            RKAPCapex::updateOrCreate([
                'kode_opco' => $item[0],
                'tahun' => $item[1],
                'bulan' => $item[2],
            ],[
                'kode_opco' => $item[0],
                'tahun' => $item[1],
                'bulan' => $item[2],
                'rkap_capex' => $item[3],
            ]);
        }
        $response = responseSuccess('Data added successfully');
        return response()->json($response,200);
    }

    public function temp(Request $request)
    {
        $request->validate([
            'filter_tahun' => 'nullable|numeric',
        ]);

        $data =  RKAPCapex::select(
            'kode_opco',
            'tahun',
            'bulan',
            'rkap_capex'
        );
        if ($request->filter_tahun) {
            $filter_tahun = " tahun = ? ";
            $data->whereRaw($filter_tahun,[$request->filter_tahun]);
        }
        $data = $data->get();

            // dd($data);

		return Excel::download(new ExportTempRKAPCapex($data), 'Template RKAP Capex.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
    }

    public function create()
    {
        //
    }

    public function filter()
    {
        //filter opco
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();

        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow+1; $i++) {
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];  
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $bulan = [];
        for ($i=0; $i < count($months); $i++) { 
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => true
                ];
            } 
            else{
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => false
                ];
            }       
        }

        $data = [
            'kode_opco' => $opco,
            'tahun'     => $tahun,
            'bulan'     => $bulan
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function show($rkap)
    {
        $query   = RKAPCapex::find($rkap);
        $response = responseSuccess('Data successfully displayed',$query);
        return response()->json($response,200);    
    }
    
    public function edit($id)
    {
        //
    }

    public function update(Request $request, $id)
    {
        $data = $this->findDataWhere(RKAPCapex::class, ['id_rkap_capex' => $id]);

          DB::beginTransaction();
          try {
              $data->update([
                'kode_opco' => $request->kode_opco,
                'tahun' => $request->tahun,
                'bulan' => $request->bulan,
                'rkap_capex' => $request->rkap_capex == null ? 0 : str_replace(".","",$request->rkap_capex),
                'update_by' => Auth::user()->username, //masih belum masuk
              ]);
              DB::commit();
              $response = responseSuccess('Data updated successfully ', $data);
              return response()->json($response, 200, [], JSON_PRETTY_PRINT);
          } catch (Exception $e) {
              DB::rollback();
              $response = responseFail(trans("messages.update-fail"), $e->getMessage());
              return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }    
    }

    public function destroy($id)
    {
        RKAPCapex::destroy($id);
        $response = responseSuccess("Data deleted successfully");
        return response()->json($response,200);    
    }
}
