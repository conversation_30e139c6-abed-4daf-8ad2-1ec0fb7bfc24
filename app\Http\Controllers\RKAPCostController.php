<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Menu;
use App\Models\RKAPCost;
use App\Models\Opco;
use Auth;
use DataTables;

use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportTempRKAPCost;
use App\Imports\ImportRKAPCost;

class RKAPCostController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'RKAP Cost',
            'breadcrumb' => [
                [
                    'title'=>'Input RKAP',
                    'url'=>'/rkap-cost',
                ],
                [
                    'title'=>'RKAP Cost',
                    'url'=>'',
                ]
            ],
        ];
        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow; $i++) { 
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];  
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);
        $optTahun ="";
        foreach ($tahun as $value) {
            $tahun = $value['tahun'];
            $selected = $value['selected'];
            if ($selected == true) {
                $optTahun .= "<option value=$tahun selected>$tahun</option>";
            }
            else{
                $optTahun .= "<option value=$tahun>$tahun</option>";
            }
        }
        $data['tahun'] = $optTahun;

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        // $bulan = DB::table('m_bulan')->select('kode_bulan', 'bln_english')->orderBy('kode_bulan')->get();
        $optBulan = "";
        for ($i=0; $i < count($months); $i++) { 
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $optBulan .= "<option value=$noBulan selected>$months[$i]</option>";
            } 
            else{
                $optBulan .= "<option value=$noBulan>$months[$i]</option>";
            }       
        }
        $data['bulan'] = $optBulan;
        return view('rkapCost', $data); 
    }

    public function viewImport()
    {
        $data = [
            'title' => 'RKAP Cost',
            'breadcrumb' => [
                [
                    'title'=>'Input RKAP',
                    'url'=>'/rkap-cost',
                ],
                [
                    'title'=>'RKAP Cost',
                    'url'=>'/rkap-cost/view-import',
                ],
                [
                    'title'=>'Import Data',
                    'url'=>'',
                ]
            ],
        ];
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();
        $opt_opco = "";
        foreach ($opco as $value) {
            $kode_opco = $value['kode_opco'];
            $nama_opco = $value['nama_opco'];
            $opt_opco .= "<option value='$kode_opco'>$nama_opco</option>";
        }
        $data['opco'] = $opt_opco;
        return view('rkapCostImport', $data);      
    }

    public function filter()
    {
        //filter opco
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();

        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow+1; $i++) {
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];  
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $bulan = [];
        for ($i=0; $i < count($months); $i++) { 
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => true
                ];
            } 
            else{
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => false
                ];
            }       
        }

        $data = [
            'kode_opco' => $opco,
            'tahun'     => $tahun,
            'bulan'     => $bulan
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function datatables(Request $request)
    {
        $rkap = RKAPCost::select(['id_rkap_biaya','m_opco.kode_opco','tahun','bln_indo','bln_english','rkap_bahan_bakar','rkap_bahan_baku','rkap_listrik',
        'rkap_tenaga_kerja','rkap_pemeliharaan','rkap_penyusutan','rkap_administrasi_umum','rkap_pajak_asuransi','rkap_elim_bb','rkap_elim_penyusutan',
        'rkap_elim_administrasi',
        DB::raw("to_char(rkap_bahan_bakar, '999G999G999G999G999') as new_rkap_bahan_bakar"),
        DB::raw("to_char(rkap_bahan_baku, '999G999G999G999G999') as new_rkap_bahan_baku"),
        DB::raw("to_char(rkap_listrik, '999G999G999G999G999') as new_rkap_listrik"),
        DB::raw("to_char(rkap_tenaga_kerja, '999G999G999G999G999') as new_rkap_tenaga_kerja"),
        DB::raw("to_char(rkap_pemeliharaan, '999G999G999G999G999') as new_rkap_pemeliharaan"),
        DB::raw("to_char(rkap_penyusutan, '999G999G999G999G999') as new_rkap_penyusutan"),
        DB::raw("to_char(rkap_administrasi_umum, '999G999G999G999G999') as new_rkap_administrasi_umum"),
        DB::raw("to_char(rkap_pajak_asuransi, '999G999G999G999G999') as new_rkap_pajak_asuransi"),
        DB::raw("to_char(rkap_elim_bb, '999G999G999G999G999') as new_rkap_elim_bb"),
        DB::raw("to_char(rkap_elim_penyusutan, '999G999G999G999G999') as new_rkap_elim_penyusutan"),
        DB::raw("to_char(rkap_elim_administrasi, '999G999G999G999G999') as new_rkap_elim_administrasi")])
        ->join('m_opco', 'rkap_cost.kode_opco', '=', 'm_opco.kode_opco')    
        ->join('m_bulan', 'bulan', '=', 'kode_bulan');
        if($request->filter_opco){
            $rkap = $rkap -> where('m_opco.kode_opco', $request->filter_opco);
        }
        if($request->filter_tahun){
            $rkap = $rkap -> where('rkap_cost.tahun', $request->filter_tahun);
        }
        if($request->filter_bulan){
            $rkap = $rkap -> where('rkap_cost.bulan', $request->filter_bulan);
        }
        if($request->filter_search){
            $filter = $request->filter_search;
            $rkap = $rkap -> where('rkap_cost.kode_opco', 'ilike','%'.$filter.'%')
            -> orWhere('tahun', 'ilike','%'.$filter.'%')
            -> orWhere('bln_indo', 'ilike','%'.$filter.'%')
            -> orWhere('bln_english', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_bahan_bakar', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_bahan_baku', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_listrik', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_tenaga_kerja', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_pemeliharaan', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_penyusutan', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_administrasi_umum', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_pajak_asuransi', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_elim_bb', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_elim_penyusutan', 'ilike','%'.$filter.'%')
            -> orWhere('rkap_elim_administrasi', 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_bahan_bakar, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_bahan_baku, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_listrik, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_tenaga_kerja, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_pemeliharaan, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_penyusutan, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_administrasi_umum, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_pajak_asuransi, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_elim_bb, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_elim_penyusutan, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_elim_administrasi, '999G999G999G999G999')"), 'ilike','%'.$filter.'%');
        }
        $data     = DataTables::of($rkap)
        ->addIndexColumn()
        ->addColumn('action', function($row){
            $btn = '<button type="button" class="edits btn btn-sm btn-icon btn-outline-primary mr-2" title="Edit" data-toggle="tooltip" data-id="'.$row->id_rkap_biaya.'" ><i class="fa fa-edit"></i></button>';
            $btn = $btn.'<button type="button" class="deletes btn btn-sm btn-icon btn-outline-danger" title="Delete" data-toggle="tooltip" data-id="'.$row->id_rkap_biaya.'" ><i class="fa fa-trash"></i></button>';
            return $btn;
        })
        ->rawColumns(['action'])        
        ->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);        
    }
    

    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        $cekRkap = RKAPCost::where('kode_opco', $request->kode_opco)
        ->where('tahun', $request->tahun)
        ->where('bulan', $request->bulan)->get()->toArray();
        if($cekRkap){
            $response = [
                'success' => false,
                'data' => $cekRkap
            ];
            return response()->json($response,200);    
        }
        $rkap = RKAPCost::create([
            'kode_opco' => $request->kode_opco,
            'tahun' => $request->tahun,
            'bulan' => $request->bulan,
            'rkap_bahan_bakar' => $request->rkap_bahan_bakar == null ? 0 : str_replace('.','',$request->rkap_bahan_bakar),
            'rkap_bahan_baku' => $request->rkap_bahan_baku == null ? 0 : str_replace('.','',$request->rkap_bahan_baku),
            'rkap_listrik' => $request->rkap_listrik == null ? 0 : str_replace('.','',$request->rkap_listrik),
            'rkap_tenaga_kerja' => $request->rkap_tenaga_kerja == null ? 0 : str_replace('.','',$request->rkap_tenaga_kerja),
            'rkap_pemeliharaan' => $request->rkap_pemeliharaan == null ? 0 : str_replace('.','',$request->rkap_pemeliharaan),
            'rkap_penyusutan' => $request->rkap_penyusutan == null ? 0 : str_replace('.','',$request->rkap_penyusutan),
            'rkap_administrasi_umum' => $request->rkap_administrasi_umum == null ? 0 : str_replace('.','',$request->rkap_administrasi_umum),
            'rkap_pajak_asuransi' => $request->rkap_pajak_asuransi == null ? 0 : str_replace('.','',$request->rkap_pajak_asuransi),
            'rkap_elim_bb' => $request->rkap_elim_bb == null ? 0 : str_replace('.','',$request->rkap_elim_bb),
            'rkap_elim_penyusutan' => $request->rkap_elim_penyusutan == null ? 0 : str_replace('.','',$request->rkap_elim_penyusutan),
            'rkap_elim_administrasi' => $request->rkap_elim_administrasi == null ? 0 : str_replace('.','',$request->rkap_elim_administrasi),
            'create_by' => Auth::user()->username
        ]);
        $response = responseSuccess('Data added successfully',$rkap);
        return response()->json($response,200);    
    }

    public function show($rkap)
    {
        $query   = RKAPCost::find($rkap);
        $response = responseSuccess('Data successfully displayed',$query);
        return response()->json($response,200);    
    }

    public function edit($id)
    {
        //
    }

    public function update(Request $request, $id)
    {
        $data = $this->findDataWhere(RKAPCost::class, ['id_rkap_biaya' => $id]);

        DB::beginTransaction();
        try {
            $data->update([
                'kode_opco' => $request->kode_opco,
                'tahun' => $request->tahun,
                'bulan' => $request->bulan,
                'rkap_bahan_bakar' => $request->rkap_bahan_bakar == null ? 0 : str_replace('.','',$request->rkap_bahan_bakar),
                'rkap_bahan_baku' => $request->rkap_bahan_baku == null ? 0 : str_replace('.','',$request->rkap_bahan_baku),
                'rkap_listrik' => $request->rkap_listrik == null ? 0 : str_replace('.','',$request->rkap_listrik),
                'rkap_tenaga_kerja' => $request->rkap_tenaga_kerja == null ? 0 : str_replace('.','',$request->rkap_tenaga_kerja),
                'rkap_pemeliharaan' => $request->rkap_pemeliharaan == null ? 0 : str_replace('.','',$request->rkap_pemeliharaan),
                'rkap_penyusutan' => $request->rkap_penyusutan == null ? 0 : str_replace('.','',$request->rkap_penyusutan),
                'rkap_administrasi_umum' => $request->rkap_administrasi_umum == null ? 0 : str_replace('.','',$request->rkap_administrasi_umum),
                'rkap_pajak_asuransi' => $request->rkap_pajak_asuransi == null ? 0 : str_replace('.','',$request->rkap_pajak_asuransi),
                'rkap_elim_bb' => $request->rkap_elim_bb == null ? 0 : str_replace('.','',$request->rkap_elim_bb),
                'rkap_elim_penyusutan' => $request->rkap_elim_penyusutan == null ? 0 : str_replace('.','',$request->rkap_elim_penyusutan),
                'rkap_elim_administrasi' => $request->rkap_elim_administrasi == null ? 0 : str_replace('.','',$request->rkap_elim_administrasi),
                'update_by' => Auth::user()->username
            ]);
        DB::commit();
        $response = responseSuccess('Data updated successfully ', $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }    
    }

    public function destroy($id)
    {
        RKAPCost::destroy($id);
        $response = responseSuccess("Data deleted successfully");
        return response()->json($response,200);    
    }

    public function temp(Request $request)
    {
            $request->validate([
                'filter_tahun' => 'nullable|numeric',
            ]);

            $data =  RKAPCost::select(
                'kode_opco',
                'tahun',
                'bulan',
                'rkap_bahan_bakar',
                'rkap_bahan_baku',
                'rkap_listrik',
                'rkap_tenaga_kerja',
                'rkap_pemeliharaan',
                'rkap_penyusutan',
                'rkap_administrasi_umum',
                'rkap_pajak_asuransi',
                'rkap_elim_bb',
                'rkap_elim_penyusutan',
                'rkap_elim_administrasi'
            );
            if ($request->filter_tahun) {
                $filter_tahun = " tahun = ? ";
                $data->whereRaw($filter_tahun,[$request->filter_tahun]);
            }
            $data = $data->get();

		return Excel::download(new ExportTempRKAPCost($data), 'Template RKAP Cost.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
    }

    public function import(Request $request)
    {
		// validasi
		$this->validate($request, [
			'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author 
		]);

        // menangkap file excel
        $file = $request->file('excel');

        // import data
        $import = new ImportRKAPCost;
        Excel::import($import, $file);
        //list opco
        $opco = Opco::select('kode_opco')->get()->toArray();        
        $arrOpco = [];
        foreach ($opco as $value) {
            array_push($arrOpco,$value['kode_opco']);
        }
        $datas = ($import->data)->toArray();
        $result = [];
        foreach ($datas as $data) {
            $format = $data;
            $message = "";
            $status = "Valid";
            if($data['opco']==NULL){
                $status = 'Invalid';
                $message = $message . "Kolom opco tidak boleh kosong ";
            }
            else if(gettype($data['opco']) != 'string'){
                $status = 'Invalid';
                $message = $message . "Kolom opco hanya berisi huruf ";
            }
            else if(!in_array($data['opco'],$arrOpco)){
                $status = "Invalid";
                $message = $message . "Kolom opco tidak ditemukan";
            }
            if($data['year']==NULL){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom tahun tidak boleh kosong ";
                }
                else{
                    $message = $message . ", Kolom tahun tidak boleh kosong";
                }
            }
            else if(gettype($data['year']) != 'integer' ){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom tahun hanya berisi angka ";
                }
                else{
                    $message = $message . ", Kolom tahun hanya berisi angka";
                }            
            }
            if($data['month']==NULL){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom bulan tidak boleh kosong ";
                }
                else{
                    $message = $message . ", Kolom bulan tidak boleh kosong";
                }
            }
            else if(gettype($data['month']) != 'integer' ){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom bulan hanya berisi angka ";
                }
                else{
                    $message = $message . ", Kolom bulan hanya berisi angka";
                }            
            }
            $listRKAP = ['rkap_bahan_bakar', 'rkap_bahan_baku_penolong', 'rkap_listrik', 'rkap_tenaga_kerja', 'rkap_pemeliharaan','rkap_deplesi_penyusutan_amortisasi',
            'rkap_urusan_umum_adm_kantor', 'rkap_pajak_asuransi', 'rkap_elim_bahan_baku_penolong', 'rkap_elim_deplesi_penyusutan_amortisasi', 'rkap_elim_urusan_umum_adm_kantor'];
            $messageRKAP = ['RKAP bahan bakar', 'RKAP bahan baku penolong', 'RKAP listrik', 'RKAP tenaga kerja', 'RKAP pemeliharaan','RKAP deplesi penyusutan amortisasi',
            'RKAP urusan umum adm kantor', 'RKAP pajak asuransi', 'RKAP elim bahan baku penolong', 'RKAP elim deplesi penyusutan amortisasi', 'RKAP elim urusan umum adm kantor'];
            foreach ($listRKAP as $key => $value) {
                if ($data[$value] == null) {
                    $format[$value]=0;
                }
                else if(gettype($data[$value]) != 'integer'){
                    $status = "Invalid";
                    $success = false; 
                    if($message == ""){
                        $message = $message . "Kolom nilai ".$messageRKAP[$key]." hanya berisi angka";
                    }else{
                        $message = $message . ", Kolom nilai rkap ".$messageRKAP[$key]." hanya berisi angka";
                    } 
                }            
            }
            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT); 
    }

    public function insertData(Request $request){
        $excel = json_decode($request->excel);
        $result = [];
        $listCol = ['opco', 'year', 'month', 'rkap_bahan_bakar', 'rkap_bahan_baku_penolong', 'rkap_listrik', 'rkap_tenaga_kerja', 'rkap_pemeliharaan','rkap_deplesi_penyusutan_amortisasi',
        'rkap_urusan_umum_adm_kantor', 'rkap_pajak_asuransi', 'rkap_elim_bahan_baku_penolong', 'rkap_elim_deplesi_penyusutan_amortisasi', 'rkap_elim_urusan_umum_adm_kantor'];
        //list opco
        $opco = Opco::select('kode_opco')->get()->toArray();        
        $arrOpco = [];
        foreach ($opco as $value) {
            array_push($arrOpco,$value['kode_opco']);
        }
        foreach ($excel as $data) {
            $oldData = $data;
            foreach ($listCol as $index => $col) {
                $data[$col] = $data[$index];
            }
            $data = array_diff_key($data, $oldData);
            $format = $data;
            $message = "";
            $status = "Valid";
            if($data['opco']==NULL){
                $status = 'Invalid';
                $message = $message . "Kolom opco tidak boleh kosong ";
            }
            else if(gettype($data['opco']) != 'string'){
                $status = 'Invalid';
                $message = $message . "Kolom opco hanya berisi huruf ";
            }
            else if(!in_array($data['opco'],$arrOpco)){
                $status = "Invalid";
                $message = $message . "Kolom opco tidak ditemukan";
            }
            if($data['year']==NULL){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom tahun tidak boleh kosong ";
                }
                else{
                    $message = $message . ", Kolom tahun tidak boleh kosong";
                }
            }
            else if(gettype($data['year']) != 'integer' ){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom tahun hanya berisi angka ";
                }
                else{
                    $message = $message . ", Kolom tahun hanya berisi angka";
                }            
            }
            if($data['month']==NULL){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom bulan tidak boleh kosong ";
                }
                else{
                    $message = $message . ", Kolom bulan tidak boleh kosong";
                }
            }
            else if(gettype($data['month']) != 'integer' ){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom bulan hanya berisi angka ";
                }
                else{
                    $message = $message . ", Kolom bulan hanya berisi angka";
                }            
            }
            $listRKAP = ['rkap_bahan_bakar', 'rkap_bahan_baku_penolong', 'rkap_listrik', 'rkap_tenaga_kerja', 'rkap_pemeliharaan','rkap_deplesi_penyusutan_amortisasi',
            'rkap_urusan_umum_adm_kantor', 'rkap_pajak_asuransi', 'rkap_elim_bahan_baku_penolong', 'rkap_elim_deplesi_penyusutan_amortisasi', 'rkap_elim_urusan_umum_adm_kantor'];
            $messageRKAP = ['RKAP bahan bakar', 'RKAP bahan baku penolong', 'RKAP listrik', 'RKAP tenaga kerja', 'RKAP pemeliharaan','RKAP deplesi penyusutan amortisasi',
            'RKAP urusan umum adm kantor', 'RKAP pajak asuransi', 'RKAP elim bahan baku penolong', 'RKAP elim deplesi penyusutan amortisasi', 'RKAP elim urusan umum adm kantor'];
            foreach ($listRKAP as $key => $value) {
                if ($data[$value] == null) {
                    $format[$value]=0;
                }
                else if(gettype($data[$value]) != 'integer'){
                    $status = "Invalid";
                    $success = false; 
                    if($message == ""){
                        $message = $message . "Kolom nilai ".$messageRKAP[$key]." hanya berisi angka";
                    }else{
                        $message = $message . ", Kolom nilai rkap ".$messageRKAP[$key]." hanya berisi angka";
                    } 
                }            
            }
            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);      
    }

    public function saveData(Request $request)
    {
        $excel = json_decode($request->excel);
        foreach ($excel as $item) {
            RKAPCost::updateOrCreate([
                'kode_opco' => $item[0],
                'tahun' => $item[1],
                'bulan' => $item[2],
            ],[
                'kode_opco' => $item[0],
                'tahun' => $item[1],
                'bulan' => $item[2],
                'rkap_bahan_bakar' => $item[3],
                'rkap_bahan_baku' => $item[4],
                'rkap_listrik' => $item[5],
                'rkap_tenaga_kerja' => $item[6],
                'rkap_pemeliharaan' => $item[7],
                'rkap_penyusutan' => $item[8],
                'rkap_administrasi_umum' => $item[9],
                'rkap_pajak_asuransi' => $item[10],
                'rkap_elim_bb' => $item[11],
                'rkap_elim_penyusutan' => $item[12],
                'rkap_elim_administrasi' => $item[13],
                'create_by' => Auth::user()->username,
                'update_by' => Auth::user()->username
            ]);
        }
        $response = responseSuccess('Data added successfully');
        return response()->json($response,200);
    }
}
