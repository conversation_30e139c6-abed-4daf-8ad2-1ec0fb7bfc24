<?php

namespace App\Console\Commands;

use App\Services\MailCauseStopService;
use Illuminate\Console\Command;

class MailCauseStopCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:cause-stop';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send Email Cause of Stop';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        (new MailCauseStopService)->execute();
    }
}
