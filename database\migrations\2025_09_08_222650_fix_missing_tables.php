<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class FixMissingTables extends Migration
{
    public function up()
    {
        // Create m_plant_ics if missing
        if (!Schema::hasTable('m_plant_ics')) {
            Schema::create('m_plant_ics', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('kode_plant', 30)->nullable();
                $table->string('name_plant', 100)->nullable();
                $table->string('created_by', 30)->nullable();
                $table->string('updated_by', 30)->nullable();
                $table->softDeletes();
            });
        }

        // Add other missing tables as needed
        // You can add more tables here if you encounter similar errors
    }

    public function down()
    {
        Schema::dropIfExists('m_plant_ics');
    }
}