<?php

namespace App\Http\Controllers;

use App\Http\Requests\OperasionalCapexRequest;
use App\Models\OperasionalCapex;
use App\Models\Company;
use App\Models\Po;
use App\Models\Gr;
use App\Models\Menu;
use App\Models\Equipment;
use App\Models\InvestGroup;
use App\Models\Investment;
use App\Models\ProjectInisiator;
use App\Models\InvestType;
use App\Models\ProjectProfile;
use App\Models\CostcenterStructure;
use App\Models\Initiator;
use App\Models\CoorInvest;
use App\Models\KadeptInitiator;
use App\Models\FocusStrategy;
use App\Models\AssetCode;

use App\Models\Routes;


use DataTables;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use PDF;

class OperasionalCapexController extends Controller
{
    public function index()
    {
        // $query = new Company();
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();

        $data['costcenter'] = CostcenterStructure::select('id', 'directorat')->get();
        $data['investtype'] = InvestType::select('tipe_investasi', 'description')->get();
        $data['projectprofile'] = ProjectProfile::select('id', 'project_profile')->get();
        $data['company'] = Company::select('id', 'company', 'description')->get();

        $data['initiator'] = Initiator::select('id', 'name')->get();
        $data['coorinvest'] = CoorInvest::select('id', 'coor_invest')->get();
        $data['kadeptinitiator'] = KadeptInitiator::select('id', 'nopeg', 'name')->get();
        $data['focusstrategy'] = FocusStrategy::select('id', 'kode', 'focus_strategy')->get();

        $data['equipment'] = Equipment::select('id', 'name',)->get();
        $data['investment'] = Investment::select('id', 'name',)->get();
        $data['investgroup'] = InvestGroup::select('id', 'name',)->get();
        $data['projectinisiator'] = ProjectInisiator::select('id', 'name',)->get();

        $data['assetcode'] = AssetCode::select('id', 'kode_asset', 'deskripsi',)->get();

        // return $data;

        return view('operationalCapex', $data);
    }

    public function datatables(Request $request)
    {
        $query    = OperasionalCapex::get();
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // return $request;
        if ($request->file('supporting_documents')) {
            $file= $request->file('supporting_documents');
            $supporting_documents= date('YmdHi').$file->getClientOriginalName();
            $file-> move(public_path('assets/img/operationalCapex/supportingDocuments'), $supporting_documents);
        }
        $operasionalcapex = OperasionalCapex::create([
            'company' => $request->company,
            'type_investment' => $request->investtype,
            'profile_projects' => $request->profile_projects,
            'coor_invest' => $request->coor_invest,
            'asset_code' => $request->asset_code,
            'project_inisiator' => $request->project_inisiator,
            'cost_center' => $request->cost_center,
            'investment_name' => $request->investment_name,
            'existing_condition' => $request->existing_condition,
            'capex_success_indicator' => $request->capex_success_indicator,
            'detail_scope' => $request->detail_scope,
            'priority' => $request->priority,
            'year' => $request->year,
            'intiator' => $request->intiator,
            'kadept_initiator' => $request->kadept_initiator,
            'focus_strategy' => $request->focus_strategy,
            'risk_if_not_done' => $request->risk_if_not_done,
            'invest_group' => $request->invest_group,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'investment' => $request->investment,
            'consequences' => $request->consequences,
            'likelihood' => $request->likelihood,
            'level' => $request->level,
            'invesment_status' => $request->invesment_status,
            // 'planning_commitment' => $planning_commitment,
            // 'po' => $request->po,
            'next_year_po_1' => $request->next_year_po_1,
            'next_year_po_2' => $request->next_year_po_2,
            'next_year_po_3' => $request->next_year_po_3,
            // 'planning_good_receipt' => $planning_good_receipt,
            // 'gr' => $request->gr,
            'no_equipment' => $request->no_equipment,
            'depreiasi' => $request->depreiasi,
            'quantity' => $request->quantity,
            'nilai_barang_dan_jasa_import' => $request->nilai_barang_dan_jasa_import,
            'nilai_barang_lokal' => $request->nilai_barang_lokal,
            'nilai_jasa' => $request->nilai_jasa,
            'eng_mngmt_cost' => $request->eng_mngmt_cost,
            'total_investment' => $request->total_investment,
            // 'komponen_tkdn' => $request->komponen_tkdn,
            'benefit_analysis' => $request->benefit_analysis,
            // 'similar_capex_reference' => $similar_capex_reference,
            // 'wbs' => $request->wbs,
            'supporting_documents' => $supporting_documents,
            'price_list_OEM' => $request->price_list_OEM,
            'engineering_judgement' => $request->engineering_judgement,
            'critical_part' => $request->critical_part,
        ]);
        
        // looping untuk isi otomatis 
        $lastinsertedid = $operasionalcapex->id;

        if ($request->po) {
            foreach ($request->po as $key => $value) {
                Po::create([
                    'id_operasional' => $lastinsertedid,
                    'po' => $value,
                    'month' => $request->month[$key],
                ]);
            }   
        }

        if ($request->gr) {
            foreach ($request->gr as $key => $value) {
                Gr::create([
                    'id_operasional' => $lastinsertedid,
                    'gr' => $value,
                    'month' => $request->month1[$key],
                ]);
            }
        }

        $response = responseSuccess(trans('message.read-success'),$operasionalcapex);
        return response()->json($response,200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($operasionalcapex)
    {

        $query['operasional']   = OperasionalCapex::find($operasionalcapex);
        $query['po'] = Po::where('id_operasional', $operasionalcapex)->get();
        $query['gr'] = Gr::where('id_operasional', $operasionalcapex)->get();
        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $query   = OperasionalCapex::find($id);
        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update($id, OperasionalCapexRequest $request)
    {
        $filename   = OperasionalCapex::find($id);
        $data = $this->findDataWhere(OperasionalCapex::class, ['id' => $id]);

        if ($request->file('supporting_documents')) {
            $file= $request->file('supporting_documents');
            $supporting_documents= date('YmdHi').$file->getClientOriginalName();
            $file-> move(public_path('assets/img/operationalCapex/supportingDocuments'), $supporting_documents);
        } else {
            $supporting_documents = $filename->supporting_documents;
        };
        
        //   dd($data);exit();
        DB::beginTransaction();
        try {
            $data->update([
                'company' => $request->company,
                'type_investment' => $request->type_investment,
                'profile_projects' => $request->profile_projects,
                'coor_invest' => $request->coor_invest,
                'asset_code' => $request->asset_code,
                'project_inisiator' => $request->project_inisiator,
                'cost_center' => $request->cost_center,
                'investment_name' => $request->investment_name,
                'existing_condition' => $request->existing_condition,
                'capex_success_indicator' => $request->capex_success_indicator,
                'detail_scope' => $request->detail_scope,
                'priority' => $request->priority,
                'year' => $request->year,
                'intiator' => $request->intiator,
                'kadept_initiator' => $request->kadept_initiator,
                'focus_strategy' => $request->focus_strategy,
                'risk_if_not_done' => $request->risk_if_not_done,
                'invest_group' => $request->invest_group,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
                'investment' => $request->investment,
                'consequences' => $request->consequences,
                'likelihood' => $request->likelihood,
                'level' => $request->level,
                'invesment_status' => $request->invesment_status,
                'next_year_po_1' => $request->next_year_po_1,
                'next_year_po_2' => $request->next_year_po_2,
                'next_year_po_3' => $request->next_year_po_3,
                'no_equipment' => $request->no_equipment,
                'depreiasi' => $request->depreiasi,
                'quantity' => $request->quantity,
                'nilai_barang_dan_jasa_import' => $request->nilai_barang_dan_jasa_import,
                'nilai_barang_lokal' => $request->nilai_barang_lokal,
                'nilai_jasa' => $request->nilai_jasa,
                'total_investment' => $request->total_investment,
                'komponen_tkdn' => $request->komponen_tkdn,
                'benefit_analysis' => $request->benefit_analysis,
                'supporting_documents' => $supporting_documents,
                'price_list_OEM' => $request->price_list_OEM,
                'engineering_judgement' => $request->engineering_judgement,
                'critical_part' => $request->critical_part,                   
            ]);
            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }

    }


    public function destroy($id)
    {

        OperasionalCapex::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }

    public function print($id)
    {
        $data['company'] = Company::all();
        $data['po'] = Po::where('id_operasional', $id)->get();
        $data['gr'] = Gr::where('id_operasional', $id)->get();
        $data['operasionalcapex'] = OperasionalCapex::find($id);

        return view('operasionalcapexprint', $data);
    }
}

