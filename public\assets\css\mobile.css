#kt_wrapper{
    padding-left:0;
    padding-top: 40px !important
}

.style-top-card{
    margin-left: 2px !important;
    margin-right: 2px !important;
    padding: 0px !important;
}
.filter-mobile{
    display: none;
}

.filter-dropdown{
    max-width: 130px;
}

#kt_filter{
    display: none !important;
}

@media screen and (max-width:600px) {
    #kt_wrapper{
        padding-top: 0 !important
    }

    #kt_filter{
        display: inline-flex !important;
    }

    .filter-top{
        display: none !important;
    }

    .mobile-filter.open{
        display: flex !important;
        position: absolute;
        top: 4rem;
        z-index: 10;
        transform: translateX(69%);
    }

    .mobile-filter.open>div:first-child{
        flex-direction: column;
        background-color: white;
        box-shadow: 1px 1px 1px rgb(0 0 0 / 50%);
        padding: 0.5rem 1rem;
        justify-content: center !important;
    }

    .mobile-filter.open>div:first-child>*+*{
        margin-top: 1rem;
    }

    .mobile-filter.open>div:first-child button,
    .mobile-filter.open>div:first-child a{
        width: 100%;
    }

    .style-top-card{
        margin: 0 !important;
    }

    .content-mobile{
        margin-top: -10px !important;
    }

    .filter-mobile{
        display: flex;
    }
    .filter-dropdown{

    }
}
