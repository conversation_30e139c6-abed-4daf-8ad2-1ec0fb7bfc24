<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTkResume extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tk_resume', function (Blueprint $table) {
            $table->bigIncrements('id_koreksi_resume');
            $table->string('kode_opco', 30);
            $table->string('kode_plant', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->text('problem')->nullable();
            $table->decimal('oph',10, 2)->nullable();
            $table->decimal('updt',10, 2)->nullable();
            $table->decimal('pdt',10, 2)->nullable();
            $table->decimal('stop_idle',10, 2)->nullable();
            $table->integer('frek_updt')->nullable();

            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->index(['kode_plant', 'tanggal']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tk_resume');
    }
}
