<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTsRealisasiPerformanceDailyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::dropIfExists('ts_realisasi_performance_daily');
        Schema::create('ts_realisasi_performance_daily', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('kode_plant', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->float('oph', 8, 5);
            $table->float('updt', 8, 5);
            $table->float('pdt', 8, 5);
            $table->float('stop_idle', 8, 5);
            $table->float('fy_stop', 8, 5);
            $table->float('frek_updt', 8, 5);
            $table->string('problem', 200);
            $table->string('source_system', 30)->nullable();
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->float('cal', 52, 0)->nullable();
            $table->float('net_avail', 52, 0)->nullable();
            $table->float('koreksi', 52, 0)->nullable();
            $table->float('act_prod', 52, 0)->nullable();
            $table->float('act_idle_prod', 52, 0)->nullable();
            $table->string('kode_opco', 30)->nullable();
            $table->float('rate_gross', 52, 0)->nullable();
            $table->float('rate_netto', 52, 0)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ts_realisasi_performance_daily');
    }
}
