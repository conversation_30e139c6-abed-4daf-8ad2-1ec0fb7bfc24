<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RKAPCost extends Model
{
    // use SoftDeletes;

    protected $table = 'rkap_cost';
    protected $primaryKey = 'id_rkap_biaya';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [  
        'kode_opco',
        'tahun',
        'bulan',
        'rkap_bahan_bakar',
        'rkap_bahan_baku',
        'rkap_listrik',
        'rkap_tenaga_kerja',
        'rkap_pemeliharaan',
        'rkap_penyusutan',
        'rkap_administrasi_umum',
        'rkap_pajak_asuransi',
        'rkap_elim_bb',
        'rkap_elim_penyusutan',
        'rkap_elim_administrasi',
        'create_by',
        'update_by',
    ];

    protected $dates = ['deleted_at'];

}
