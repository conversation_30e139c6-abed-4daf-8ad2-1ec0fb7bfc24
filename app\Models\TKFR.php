<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TKFR extends Model
{
    protected $table = 'tk_fr';
    protected $primaryKey = 'no';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [     
        'no',
        'holding',
        'kode_opco',
        'tanggal',
        'THN',
        'rkap_bahan_bakar',
        'rkap_bahan_baku',
        'rkap_listrik',
        'rkap_tenaga_kerja',
        'rkap_pemeliharaan',
        'rkap_penyusutan',
        'rkap_administrasi_umum',
        'rkap_pajak_asuransi',
        'rkap_elim_bb',
        'rkap_elim_penyusutan',
        'rkap_elim_administrasi',
        'rkap_prod_klinker',
        'rkap_prod_semen',
        'rkap_clinker_sold',
        'rkap_prod_output',
        'bahan_bakar',
        'bahan_baku',
        'listrik',
        'tenaga_kerja',
        'pemeliharaan',
        'penyusutan',
        'administrasi_umum',
        'pajak_asuransi',
        'elim_bb',
        'elim_penyusutan',
        'elim_administrasi',
        'prod_klinker',
        'prod_semen',
        'clinker_sold',
        'prod_output',
        'ics',
        'create_by',
        'update_by',
    ];

}
