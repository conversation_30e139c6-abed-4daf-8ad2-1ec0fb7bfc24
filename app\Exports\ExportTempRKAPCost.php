<?php

namespace App\Exports;

use App\Models\RKAPCost;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ExportTempRKAPCost implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents, WithTitle, WithColumnWidths, WithStyles
{
    public function  __construct($data)
    {
        $this->data = $data;
    }
     //set header value
     public function headings():array
     {
         return [
             'OPCO',
             'Year',
             'Month',
             'RKAP <PERSON>',
             'RKAP <PERSON>han Baku Penolong',
             'RKAP Listrik',
             'RKAP Tenaga Kerja',
             'RKAP Pemeliharaan',
             'RKAP Deplesi, Penyusutan & Amortisasi',
             'RKAP Urusan Umum & Adm. Kantor',
             'RKAP Pajak & Asuransi',
             'RKAP Elim. Bahan Baku & Penolong',
             'RKAP Elim. Deplesi, Penyusutan & Amortisasi',
             'RKAP Elim. Urusan Umum & Adm. Kantor',
         ];
     }
 
    public function collection()
    {
        return collect($this->data);
    }

     public function registerEvents(): array
     {
         return [
             AfterSheet::class    => function(AfterSheet $event) {
                 //set background color for header (A1:D1)
                 $event->sheet->getDelegate()
                 ->getStyle('A1:N1')
                 ->getFill()
                 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                 ->getStartColor()
                 ->setARGB('C4D79B');
                //  $event->sheet->getDelegate()->getRowDimension('1')->setRowHeight(40);

 
                 //set header (A1:D1) uneditable and set workspace (A2:D1000) editable
                 // $event->sheet->protectCells('A1:D1', 'PASSWORD');
                 // $event->sheet->getStyle('A2:D1000')->getProtection()->setLocked(\PhpOffice\PhpSpreadsheet\Style\Protection::PROTECTION_UNPROTECTED);
                 // $event->sheet->getDelegate()->getProtection()->setSheet(true);
 
             },
         ];
     }
 
     //set name for worksheet
     public function title(): string
     {
         return 'RKAP Cost';
     }
 
     //set width for header
     public function columnWidths(): array
     {
         return [
             'A' => 15,
             'B' => 15,    
             'C' => 15,
             'D' => 15,
             'E' => 15,
             'F' => 15,    
             'G' => 15,
             'H' => 15, 
             'I' => 15,
             'J' => 15,    
             'K' => 15,
             'L' => 15, 
             'M' => 15,
             'N' => 15,    
         ];
     }
 
     //set bold for header
     public function styles(Worksheet $sheet)
     {
         return [
             'A'    => ['font' => ['bold' => true]],
             'B'    => ['font' => ['bold' => true]],
             'C'    => ['font' => ['bold' => true]],
             'D'    => ['font' => ['bold' => true]],
             'E'    => ['font' => ['bold' => true]],
             'F'    => ['font' => ['bold' => true]],    
             'G'    => ['font' => ['bold' => true]],
             'H'    => ['font' => ['bold' => true]], 
             'I'    => ['font' => ['bold' => true]],
             'J'    => ['font' => ['bold' => true]],    
             'K'    => ['font' => ['bold' => true]],
             'L'    => ['font' => ['bold' => true]], 
             'M'    => ['font' => ['bold' => true]],
             'N'    => ['font' => ['bold' => true]],    
         ];
     }
 
}
