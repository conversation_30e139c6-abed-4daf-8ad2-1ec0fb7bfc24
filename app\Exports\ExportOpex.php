<?php

namespace App\Exports;

use App\Models\InputOpex;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class ExportOpex implements FromCollection, WithHeadings, ShouldAutoSize
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return InputOpex::where('id','0')->get();
    }

    public function headings():array
    {
        return [
            '#',
            'fm_area',
            'fiscal_year',
            'funds_center',
            'commitment_itm',
            'amount',
        ];
    }
}
