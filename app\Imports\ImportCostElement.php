<?php

namespace App\Imports;

use App\Models\RKAPCapex;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
class ImportCostElement implements ToCollection, WithHeadingRow
{
    public $data;

    public function collection(Collection $rows)
    {
        $this->data = $rows;
    }

    public function rules(): array
    {
        return[
            'cost_element' => ['required'],
            'cost_element_name' => ['required'],
            'cost_element_group' => ['required'],
            'biaya_group' => ['required'],
            'kode_opco' => ['required']
        ];
    }
}
