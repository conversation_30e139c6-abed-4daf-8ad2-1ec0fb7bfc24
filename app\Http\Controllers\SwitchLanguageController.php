<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class SwitchLanguageController extends Controller
{
    public function switch_language($lang, Request $request)
    {
        if (! in_array($lang, config('app.available_locales'))) {
            abort(400);
        }
        $referer = $request->headers->get('referer');
        $allowedReferer = explode(';', env('ALLOWED_REFERER'));
        foreach ($allowedReferer as $key => $value) {
            if (str_contains($referer, $value)) {
                // Save locale to session.
                request()->session()->put('lang', $lang);
        return redirect()->back();
            }
        }
        header($_SERVER['SERVER_PROTOCOL'] . ' 400 Bad Request');
        exit;
    }
}
