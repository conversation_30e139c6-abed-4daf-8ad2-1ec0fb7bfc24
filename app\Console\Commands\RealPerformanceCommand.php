<?php

namespace App\Console\Commands;

use App\Models\RealPerformanceDaily;
use App\Models\RealPerformanceOld;
use App\Models\ViewMonthRealPerform;
use App\Models\ViewRealPerform;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RealPerformanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'realisasi:performance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Realisasi Performance';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $date = date('Y-m-d');
            $yearMonth = date('Y-m');
            $models = ViewMonthRealPerform::where('tanggal', $yearMonth)->get();
            foreach ($models as $model) {
                RealPerformanceOld::updateOrCreate(
                    [
                        'kode_plant' => $model->kode_plant,
                        'kode_opco' => $model->kode_opco,
                        'tahun' => date('Y'),
                        'bulan' => date('n')
                    ],
                    [
                        'tanggal' => $date,
                        'oph' => $model->oph,
                        'updt' => $model->updt,
                        'pdt' => $model->pdt,
                        'stop_idle' => $model->stop_idle,
                        'fy_stop' => $model->fy_stop,
                        'frek_updt' => $model->frek_updt,
                        'cal' => intval($model->cal),
                        'net_avail' => $model->net_avail,
                        'koreksi' => $model->koreksi,
                        'act_prod' => $model->act_prod,
                        'act_idle_prod' => $model->act_idle_prod,
                        'rate_gross' => $model->rate_gross,
                        'rate_netto' => $model->rate_netto,
                    ]
                );
            }

            $models = ViewRealPerform::where('tanggal', $date)->get();
            foreach ($models as $model) {
                RealPerformanceDaily::updateOrCreate(
                    [
                        'kode_plant' => $model->kode_plant,
                        'kode_opco' => $model->kode_opco,
                        'tanggal' => $model->tanggal
                    ],
                    [
                        'oph' => $model->oph,
                        'updt' => $model->updt,
                        'pdt' => $model->pdt,
                        'stop_idle' => $model->stop_idle,
                        'fy_stop' => $model->fy_stop,
                        'frek_updt' => intval($model->frek_updt),
                        'cal' => intval($model->cal),
                        'net_avail' => $model->net_avali,
                        'koreksi' => $model->koreksi,
                        'act_prod' => $model->act_prod,
                        'act_idle_prod' => $model->act_idle_prod,
                        'rate_gross' => $model->rate_gross,
                        'rate_netto' => $model->rate_netto,
                    ]
                );
            }
            DB::commit();
            Log::info('Realisasi performance was created successfully');
        } catch (Exception $e) {
            DB::rollBack();
            Log::info('Failed create realisasi performance');
            Log::error($e->getMessage());
        }
    }
}
