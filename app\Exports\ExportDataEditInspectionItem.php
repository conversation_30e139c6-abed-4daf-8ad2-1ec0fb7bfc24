<?php

namespace App\Exports;

use App\Models\Area;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\Models\ItemInspection;

class ExportDataEditInspectionItem implements FromCollection, WithHeadings, WithTitle, WithStyles
{

    /**
     * @return \Illuminate\Support\Collection
     */


    public function  __construct($id)
    {
        $this->id = $id;
        $this->fungsi = $fungsi;
    }

    public function headings(): array
    {
        return [
            ['FUNGSI : ' . $this->fungsi . ', 1.GOOD | 2.LOW RISK | 3.MED RISK | 4.HIGH RISK'],
            [
                'kode_equipment',
                'desc_equipment',
                'id_area',
                'nm_area',
                'id_kondisi',
                'remark',
                'create_date',
                'last_condition',
                'last_inspection',
                'last_remark'
            ]
        ];
    }
    /**
     * @return string
     */
    public function title(): string
    {
        return 'Data Export Inspection Item';
    }

    public function collection()
    {
        if ($this->id == "empty" || $this->id == "") {
            $id_area = '1=1';
        } else {
            $id_area = 'm_area.id_area in ( ' . $this->id . ' )';
        }

        $data = Area::select(
            'm_equipment.kode_equipment',
            'm_equipment.nm_equipment',
            'm_equipment.id_area',
            'm_area.nm_area',
            'm_area.create_by',
            'm_area.update_by',
            'm_area.created_at'
        )->join('m_equipment', 'm_equipment.id_area', '=', 'm_area.id_area')->whereraw($id_area)->get();


        $equipment = $data->map(function ($equip, $key) {
            $last_condition = ItemInspection::where('id_equipment', $equip->kode_equipment)
                ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
                ->where('status','Approve')
                ->where('function',$this->fungsi)
                ->orderBy('t_item_inspection.create_date', 'desc')
                ->first();

            if ($last_condition) {
                $kondisi = $last_condition->id_kondisi;
                $create_date = $last_condition->create_date;
                $remark = $last_condition->remark;
            } else {
                $kondisi = '';
                $create_date = '';
                $remark = '';
            }

            return [
                "kode_equipment" => $equip->kode_equipment,
                "nm_equipment" => $equip->nm_equipment,
                "id_area" => $equip->id_area,
                "nm_area" => $equip->nm_area,
                "id_kondisi" => "-",
                "remark" => "-",
                "date_inspection" => Carbon::now(),
                "last_condition" =>  $kondisi,
                "last_inspection" =>  $create_date,
                "last_remark" =>  $remark
            ];
        });


        return $equipment;
    }

    public function styles(Worksheet $sheet)
    {
        return [
            'A1' => ['font' => ['bold' => true]],
            'A2' => ['font' => ['bold' => true]],
        ];
    }
}
