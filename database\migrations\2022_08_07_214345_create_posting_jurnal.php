<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePostingJurnal extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('posting_jurnal', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('company_code')->nullable();
            $table->string('no_sap')->nullable();
            $table->string('doc_header')->nullable();
            $table->string('referance')->nullable();
            $table->string('document_type')->nullable();
            $table->string('currency')->nullable();
            $table->string('periode')->nullable();
            $table->string('fiscal_year')->nullable();
            $table->date('posting_date')->nullable();
            $table->date('documet_date')->nullable();
            $table->longText('messages')->nullable();
            $table->string('status')->nullable();
            $table->integer('flag')->nullable();
            $table->integer('create_by')->nullable();
            $table->integer('update_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('posting_jurnal');
    }
}
