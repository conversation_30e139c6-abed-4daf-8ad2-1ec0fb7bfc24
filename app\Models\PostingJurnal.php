<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

use DB;

class PostingJurnal extends Model
{

    protected $table    = 'posting_jurnal';
    protected $fillable = ['id',
    'company_code',
    'no_sap',
    'doc_header',
    'referance',
    'document_type',
    'currency',
    'periode',
    'fiscal_year',
    'posting_date',
    'documet_date',    
    'messages',
    'status',
    'flag',
    ];
}
