<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRealPerformanceTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('real_performance', function (Blueprint $table) {
            $table->bigIncrements('id_realisasi_performance');
            $table->string('kode_plant', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->integer('oph');
            $table->integer('opdt');
            $table->integer('pdt');
            $table->integer('stop_idle');
            $table->integer('fy_stop');
            $table->integer('frek_updt');
            $table->string('problem', 200);
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('real_performance');
    }
}
