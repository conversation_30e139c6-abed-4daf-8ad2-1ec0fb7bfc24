<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTsRealisasiCapex extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Check if table already exists before creating
        if (!Schema::hasTable('ts_realisasi_capex')) {
            Schema::create('ts_realisasi_capex', function (Blueprint $table) {
                $table->bigIncrements('id_realisasi_capex');
                $table->string('kode_opco', 30);
                $table->date('tanggal')->nullable();
                $table->bigInteger('real_capex');
                $table->string('create_by', 30)->nullable();
                $table->string('update_by', 30)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ts_realisasi_capex');
    }
}
