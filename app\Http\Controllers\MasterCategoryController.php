<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Menu;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use function response;
use function responseFail;
use function responseSuccess;
use function trans;
use function view;

class MasterCategoryController extends Controller
{

    public function index()
    {
        $data = [
            'title' => 'Category',
            'breadcrumb' => [
                [
                    'title' => 'Master Data',
                    'url' => '/category',
                ],
                [
                    'title' => 'Category',
                    'url' => '',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['menu'] = Menu::select('id', 'name')->get();
        $data['parents'] = Category::getTree();

        return view('category', $data);
    }

    public function datatables(Request $request)
    {
        $query = Category::select('m_category.*', 'p.name as parent_name')
                ->leftjoin('m_category as p', 'p.id', 'm_category.parent_id')->get();
        $data = DataTables::of($query)->make(true);
        $response = $data->getData(true);

        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  Request  $request
     * @return Response
     */
    public function store(Request $request)
    {
        $rules = [
            'name' => 'required',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique' => trans('messages.unique'),
        ];
        $this->validate($request, $rules, $messages);
        try {
            if ($request->parent_id) {
                $category = Category::create([
                        'name' => $request->name,
                        'parent_id' => $request->parent_id,
                ]);
            } else {
                $category = Category::create([
                        'name' => $request->name,
                ]);
            }

            $response = responseSuccess(trans('messages.create-success'), $category);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function show($id)
    {
        $query = Category::select('m_category.*', 'p.name as parent_name')
                ->leftjoin('m_category as p', 'p.id', 'm_category.parent_id')->find($id);
        $response = responseSuccess(trans('messages.read-success'), $query);
        return response()->json($response, 200);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  Request  $request
     * @param  int  $id
     * @return Response
     */
    public function update($id, Request $request)
    {
        $data = $this->findDataWhere(Category::class, ['id' => $id]);

        $rules = [
            'name' => 'required',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique' => trans('messages.unique'),
        ];
        $this->validate($request, $rules, $messages);

        DB::beginTransaction();
        try {
            $data->update([
                'name' => $request->name,
                'parent_id' => $request->parent_id,
            ]);
            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function destroy($id)
    {
        Category::destroy($id);
        $response = responseSuccess(trans('messages.delete-success'));
        return response()->json($response, 200);
    }
}
