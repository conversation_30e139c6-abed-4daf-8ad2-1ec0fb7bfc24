<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\CoorInvestRequest;
use DataTables;
use App\Models\Menu;
use App\Models\Routes;
use App\Models\CoorInvest;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class CoorInvestController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();

        return view('CoorInvest', $data);
    }

    public function datatables(Request $request)
    {
        $query    = CoorInvest::get();
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $coorinvest = CoorInvest::create([
            'coor_invest' => $request->coor_invest,
        ]);

        $response = responseSuccess(trans('message.read-success'),$coorinvest);
        // return $response;
        return response()->json($response,200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($coorinvest)
    {
        $query   = CoorInvest::find($coorinvest);
        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $query   = CoorInvest::find($id);
        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $this->findDataWhere(CoorInvest::class, ['id' => $id]);

        //   dd($data);exit();
        DB::beginTransaction();
        try {
            $data->update([
                    'coor_invest' => $request->coor_invest,
                ]);
                DB::commit();
                $response = responseSuccess(trans("messages.update-success"), $data);
                return response()->json($response, 200, [], JSON_PRETTY_PRINT);
            } catch (Exception $e) {
                DB::rollback();
                $response = responseFail(trans("messages.update-fail"), $e->getMessage());
                return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        CoorInvest::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }
}
