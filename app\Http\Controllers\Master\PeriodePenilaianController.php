<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\DmmPeriodePenilaian;
use App\Rules\MasterPeriodePenilainRules;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class PeriodePenilaianController extends Controller
{
    //
    public function index()
    {
        $data = [
            'title' => 'Periode Penilaian',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'#',
                ],
                [
                    'title'=>'Periode Penilaian',
                    'url'=>'periode-penilaian',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        return view('master.periode-penilaian', $data);
    }
    public function datatables(Request $request)
    {
        $query = DmmPeriodePenilaian::list();

        $columns = [
            'dmm_periode_penilaian.id'=>'id',
            'dmm_periode_penilaian.periode'=>'periode',
            'dmm_periode_penilaian.tahun'=>'tahun',
            'dmm_periode_penilaian.start_date'=>'start_date',
            'dmm_periode_penilaian.end_date'=>'end_date',
            'dmm_periode_penilaian.status'=>'status',
            'users.username' => 'created_by_name'
        ];
        
        $data  = DataTables::of($query)
            ->filter(function ($query) use ($request, $columns) {
                $this->filterColumn($columns, $request, $query);
            })
            ->order(function ($query) use ($request, $columns) {
                $this->orderColumn($columns, $request, $query);
            })
            ->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }
    public function show($uuid)
    {
        $attributes['uuid'] = $uuid;

        $roles = [  
            'uuid' => 'required|exists:dmm_periode_penilaian',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);
        $data     = $this->findDataWhere(DmmPeriodePenilaian::class, ['uuid' => $uuid]);
        $response = responseSuccess(trans("messages.read-success"), $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }
    public function store(Request $request)
    {
        $attributes =  $request->only(['periode','tahun','start_date','end_date']);
        $attributes['periode_penilaian'] = $request->only(['periode','tahun','start_date','end_date']);
        $roles = [
            'periode' => 'required',
            'tahun' => 'required',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'date'   => trans('messages.date'),
        ];
        $this->validators($attributes, $roles, $messages);
        $rules = [
            'periode_penilaian' =>[new MasterPeriodePenilainRules()],
        ];
        $messages = [
            'periode_penilaian' => 'Periode Penilaian Sudah Tersedia, Silahkan Pilih Periode Lain',
        ];
        $this->validators($attributes, $rules, $messages);

        $attributes['created_by'] = Auth()->user()->id;

        DB::beginTransaction();
        try {
            //code...
            $data = DmmPeriodePenilaian::create($attributes);
            DB::commit();
            $response = responseSuccess(trans("messages.create-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.create-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
    public function update($uuid,Request $request)
    {
        $attributes =  $request->only(['periode','tahun','start_date','end_date','status']);
        $roles = [
            'periode' => 'required',
            'tahun' => 'required',
            'start_date' => 'required|date',
            'end_date' => 'required|date',
            'status' => 'required|in:y,n',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'date'   => trans('messages.date'),
        ];
        $this->validators($attributes, $roles, $messages);
        
        $attributes['periode_penilaian'] = $request->only(['periode','tahun','start_date','end_date']);
        $attributes['periode_penilaian'] += ['uuid'=>$uuid];
        $rules = [
            'periode_penilaian' =>[new MasterPeriodePenilainRules(true)],
        ];
        $messages = [
            'periode_penilaian' => 'Periode Penilaian Sudah Tersedia, Silahkan Pilih Periode Lain',
        ];
        $this->validators($attributes, $rules, $messages);

        $attributes['updated_by'] = Auth()->user()->id;
        $data = $this->findDataWhere(DmmPeriodePenilaian::class, ['uuid' => $uuid]);
        DB::beginTransaction();
        try {
            //code...
            $data->update($attributes);
            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
    public function destroy($uuid)
    {
        $attributes['uuid'] = $uuid;
        $roles = [  
            'uuid' => 'required|exists:dmm_periode_penilaian',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);
        $data = $this->findDataWhere(DmmPeriodePenilaian::class, ['uuid' => $uuid]);
        if($data->penilaian_opco->count()){
            $response = responseFail(trans("messages.delete-fail"), ['penilaian_opco'=>['Periode Penilaian Sudah Digunakan, Silahkan Hapus Penilaian OPCO Terlebih Dahulu']]);
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
        DB::beginTransaction();
        try {
            //code...
            $data = DmmPeriodePenilaian::where('uuid', $uuid)->delete();
            DB::commit();
            $response = responseSuccess(trans("messages.delete-success"), []);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.delete-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
}
