<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRealProduksiTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('real_produksi', function (Blueprint $table) {
            $table->bigIncrements('id_realisasi_produksi');
            $table->string('kode_plant', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->integer('prod_klinker');
            $table->integer('prod_semen');
            $table->integer('clinker_sold');
            $table->integer('prod_output');
            $table->integer('ics');
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('real_produksi');
    }
}
