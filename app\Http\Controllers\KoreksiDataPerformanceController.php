<?php

namespace App\Http\Controllers;

use Illuminate\Support\Arr;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\KilnPlant;
use App\Models\Opco;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportKoreksiPerformance;
use App\Exports\ExportDataKoreksiPerformance;
use App\Exports\ExportDataKoreksiPerformanceFr;
use App\Helpers\GlobalHelper;
use App\Imports\ImportKoreksiPerformance;
use App\Models\RKAPProduksi;


class KoreksiDataPerformanceController extends Controller
{
  /**
   * Display a listing of the resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function index()
  {
    $data = [
        'title' => 'Koreksi Data Performance',
        'breadcrumb' => [
            [
                'title'=>'Data Koreksi',
                'url'=>'/koreksi-data-performance',
            ],
            [
                'title'=>'Koreksi Data Performance',
                'url'=>'',
            ]
        ],
    ];
    return view('koreksiDataPerformance', $data);
  }

  public function saveDataKoreksi(Request $request)
  {
    $excel = json_decode($request->excel);
    $plant = DB::table('m_kiln_plant')->select(['kode_plant','no_pbi','kode_opco'])->orderBy('no_pbi', 'ASC')->get();
    foreach ($plant as $key => $value) {
      $arrPlant[$value->kode_plant] = [
        'no_pbi' => $value->no_pbi,
        'kode_opco' => $value->kode_opco
      ];
    }
    foreach ($excel as $item) {
      $time = strtotime($item[1]);
      $newformat = date('m', $time);
      $year = date('Y', $time);
      $noPbi = $arrPlant[$item[0]]['no_pbi'];
      $kodeOpco = $arrPlant[$item[0]]['kode_opco'];
      $cek_similiarity = DB::table('tk_kiln_op_detail')
        ->where(DB::raw("EXTRACT(MONTH FROM tanggal)"),$newformat)
        ->where(DB::raw("EXTRACT(YEAR FROM tanggal)"),$year)
        ->where('kode_plant',$item[0])
        ->count();

      if ($cek_similiarity != 0) {
        DB::table('tk_kiln_op_detail')
        ->where(DB::raw("EXTRACT(MONTH FROM tanggal)"),$newformat)
        ->where(DB::raw("EXTRACT(YEAR FROM tanggal)"),$year)
        ->where('kode_plant',$item[0])
        ->delete();
      }

      DB::table('tk_kiln_op_detail')->insert([
        'no' => $noPbi,
        'kode_opco' => $kodeOpco,
        'kode_plant' => $item[0],
        'tanggal' => $item[1],
        'oph' => $item[2],
        'updt' => $item[3],
        'pdt' => $item[4],
        'stop_idle' => $item[5],
        'fy_stop' => $item[6],
        'frek_updt' => (int)GlobalHelper::fnumber_id_en($item[7]),
        'rkap_oph' => $item[8],
        'rkap_updt' => $item[9],
        'rkap_pdt' => $item[10],
        'rkap_stop_idle' => $item[11],
        'rkap_frek_updt' => (int)GlobalHelper::fnumber_id_en($item[12]),
        'cal' => $item[13],
        'bdp_rate' => $item[14],
        'rkap_prod_rate' => $item[15],
        'koreksi' => $item[16],
        'act_prod' => $item[17],
        'rkap_prod' => $item[18]
      ]);
    }
    $response = responseSuccess('Data added successfully');
    return response()->json($response, 200);
  }

  public function insertData(Request $request)
  {

    $excel = json_decode($request->excel);
    $result = [];
    $success = true;
    //list opco
    $plant = KilnPlant::select('kode_plant')->get()->toArray();
    $arrPlant = [];
    foreach ($plant as $value) {
      array_push($arrPlant, $value['kode_plant']);
    }

    foreach ($excel as $data) {
      $oldData = $data;
      $data['kode_plant'] = $data[0];
      $data['tanggal'] = $data[1];
      $data['oph'] = $data[2];
      $data['updt'] = $data[3];
      $data['pdt'] = $data[4];
      $data['stop_idle'] = $data[5];
      $data['fy_stop'] = $data[6];
      $data['frek_updt'] = $data[7];
      $data['rkap_oph'] = $data[8];
      $data['rkap_updt'] = $data[9];
      $data['rkap_pdt'] = $data[10];
      $data['rkap_stop_idle'] = $data[11];
      $data['rkap_frek_updt'] = $data[12];
      $data['cal'] = $data[13];
      $data['bdp_rate'] = $data[14];
      $data['rkap_prod_rate'] = $data[15];
      $data['koreksi'] = $data[16];
      $data['act_prod'] = $data[17];
      $data['rkap_prod'] = $data[18];
      $data['is_valid'] = $data[19];
      $data = array_diff_key($data, $oldData);
      $format = $data;
      $message = "";
      $status = "Valid";

      if ($data['kode_plant'] == NULL) {
        $status = 'Invalid';
        $message = $message . "Kolom Plant tidak boleh kosong ";
      } else if (gettype($data['kode_plant']) != 'string') {
        $status = 'Invalid';
        $message = $message . "Kolom Plant hanya berisi huruf ";
      } else if (!in_array($data['kode_plant'], $arrPlant)) {
        $status = "Invalid";
        $message = $message . "Kolom Plant tidak ditemukan";
      }


      if ($data['tanggal'] == NULL) {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom Tanggal tidak boleh kosong ";
        } else {
          $message = $message . ", Kolom Tanggal tidak boleh kosong ";
        }
      }

      if ($data['oph'] == NULL) {
        $format['oph'] = 0;
      } else if (gettype($data['oph']) != 'integer' and gettype($data['oph']) != 'float'  and gettype($data['oph']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom oph  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom oph  hanya berisi angka";
        }
      }


      if ($data['updt'] == NULL) {
        $format['updt'] = 0;
      } else if (gettype($data['updt']) != 'integer' and gettype($data['updt']) != 'float' and gettype($data['updt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom updt  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom updt  hanya berisi angka";
        }
      }

      if ($data['pdt'] == NULL) {
        $format['pdt'] = 0;
      } else if (gettype($data['pdt']) != 'integer' and gettype($data['pdt']) != 'float' and gettype($data['pdt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom pdt  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom pdt  hanya berisi angka";
        }
      }

      if ($data['stop_idle'] == NULL) {
        $format['stop_idle'] = 0;
      } else if (gettype($data['stop_idle']) != 'integer' and gettype($data['stop_idle']) != 'float' and gettype($data['stop_idle']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom stop idle  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom stop idle  hanya berisi angka";
        }
      }

      if ($data['fy_stop'] == NULL) {
        $format['fy_stop'] = 0;
      } else if (gettype($data['fy_stop']) != 'integer' and gettype($data['fy_stop']) != 'float' and gettype($data['fy_stop']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom Fy Stop hanya berisi angka ";
        } else {
          $message = $message . ", Kolom Fy Stop hanya berisi angka";
        }
      }

      if ($data['frek_updt'] == NULL) {
        $format['frek_updt'] = 0;
      } else if (gettype($data['frek_updt']) != 'integer' and gettype($data['frek_updt']) != 'float' and gettype($data['frek_updt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom frek updt  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom frek updt  hanya berisi angka";
        }
      }

      if ($data['rkap_oph'] == NULL) {
        $format['rkap_oph'] = 0;
      } else if (gettype($data['rkap_oph']) != 'integer' and gettype($data['rkap_oph']) != 'float' and gettype($data['rkap_oph']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap oph  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap oph  hanya berisi angka";
        }
      }

      if ($data['rkap_updt'] == NULL) {
        $format['rkap_updt'] = 0;
      } else if (gettype($data['rkap_updt']) != 'integer' and gettype($data['rkap_updt']) != 'float' and gettype($data['rkap_updt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap updt  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap updt  hanya berisi angka";
        }
      }

      if ($data['rkap_pdt'] == NULL) {
        $format['rkap_pdt'] = 0;
      } else if (gettype($data['rkap_pdt']) != 'integer' and gettype($data['rkap_pdt']) != 'float' and gettype($data['rkap_pdt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap pdt  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap pdt  hanya berisi angka";
        }
      }

      if ($data['rkap_stop_idle'] == NULL) {
        $format['rkap_stop_idle'] = 0;
      } else if (gettype($data['rkap_stop_idle']) != 'integer' and gettype($data['rkap_stop_idle']) != 'float' and gettype($data['rkap_stop_idle']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap Stop Idle  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap Stop Idle  hanya berisi angka";
        }
      }

      if ($data['rkap_frek_updt'] == NULL) {
        $format['rkap_frek_updt'] = 0;
      } else if (gettype($data['rkap_frek_updt']) != 'integer' and gettype($data['rkap_frek_updt']) != 'float' and gettype($data['rkap_frek_updt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap frek updt hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap frek updt hanya berisi angka";
        }
      }

      if ($data['cal'] == NULL) {
        $format['cal'] = 0;
      } else if (gettype($data['cal']) != 'integer' and gettype($data['cal']) != 'float' and gettype($data['cal']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom cal hanya berisi angka ";
        } else {
          $message = $message . ", Kolom cal hanya berisi angka";
        }
      }

      if ($data['bdp_rate'] == NULL) {
        $format['bdp_rate'] = 0;
      } else if (gettype($data['bdp_rate']) != 'integer' and gettype($data['bdp_rate']) != 'float' and gettype($data['bdp_rate']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom bdp rate hanya berisi angka ";
        } else {
          $message = $message . ", Kolom bdp rate hanya berisi angka";
        }
      }

      if ($data['rkap_prod_rate'] == NULL) {
        $format['rkap_prod_rate'] = 0;
      } else if (gettype($data['rkap_prod_rate']) != 'integer' and gettype($data['rkap_prod_rate']) != 'float' and gettype($data['rkap_prod_rate']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap prod rate hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap prod rate hanya berisi angka";
        }
      }

      if ($data['koreksi'] == NULL) {
        $format['koreksi'] = 0;
      } else if (gettype($data['koreksi']) != 'integer' and gettype($data['koreksi']) != 'float' and gettype($data['koreksi']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom koreksi hanya berisi angka ";
        } else {
          $message = $message . ", Kolom koreksi hanya berisi angka";
        }
      }

      if ($data['act_prod'] == NULL) {
        $format['act_prod'] = 0;
      } else if (gettype($data['act_prod']) != 'integer' and gettype($data['act_prod']) != 'float' and gettype($data['act_prod']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom act prod hanya berisi angka ";
        } else {
          $message = $message . ", Kolom act prod hanya berisi angka";
        }
      }

      if ($data['rkap_prod'] == NULL) {
        $format['rkap_prod'] = 0;
      } else if (gettype($data['rkap_prod']) != 'integer' and gettype($data['rkap_prod']) != 'float' and gettype($data['rkap_prod']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap prod hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap prod hanya berisi angka";
        }
      }

      $format['is_valid'] = $status;
      $format['note'] = $message;
      array_push($result, $format);
    }
    $data     = DataTables::of($result)->make(true);
    $response = $data->getData(true);
    return response()->json($response, 200, [], JSON_PRETTY_PRINT);
  }

  public function importKoreksiPerformance()
  {
    $data = [
        'title' => 'Koreksi Data Performance',
        'breadcrumb' => [
            [
                'title'=>'Data Koreksi',
                'url'=>'/koreksi-data-performance',
            ],
            [
                'title'=>'Koreksi Data Performance',
                'url'=>'/import-koreksi-performance',
            ],
            [
                'title'=>'Import Data',
                'url'=>'',
            ]
        ],
    ];
    $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();
    $opt_opco = "";
    foreach ($opco as $value) {
      $kode_opco = $value['kode_opco'];
      $nama_opco = $value['nama_opco'];
      $opt_opco .= "<option value='$kode_opco'>$nama_opco</option>";
    }
    $data['opco'] = $opt_opco;

    $plant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
    $opt_plant = "";
    foreach ($plant as $value) {
      $kode_plant = $value['kode_plant'];
      $nama_plant = $value['name_plant'];
      $opt_plant .= "<option value='$kode_plant'>$nama_plant</option>";
    }
    $data['plant'] = $opt_plant;
    return view('koreksiDataPerformanceImport', $data);
  }

  public function exportTemplate(Request $request)
  {
    $request->validate([
      'filter_tahun' => 'nullable|numeric',
      'filter_bulan' => 'nullable|numeric',
    ]);
    $data_performance =  DB::table('vw_tk_kiln_op_detail_all')
      ->select([
        // 'nm_opco',
        'kode_plant',
        DB::raw("TO_CHAR(tanggal::timestamp, 'DD/MM/YYYY') AS tanggal"),
        // 'tanggal',
        'oph',
        'updt',
        'pdt',
        'stop_idle',
        'fy_stop',
        'frek_updt',
        'rkap_oph',
        'rkap_updt',
        'rkap_pdt',
        'rkap_stop_idle',
        'rkap_frek_updt',
        'cal',
        'bdp_rate',
        'rkap_prod_rate',
        'koreksi',
        'act_prod',
        'rkap_prod_vol'
      ]);
    if($request->filter_tahun){
        $data_performance = $data_performance -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $request->filter_tahun);
    }
    if($request->filter_bulan){
        $data_performance = $data_performance -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $request->filter_bulan);
    }
    $data_performance = $data_performance->get();

    //   dd($data_performance);

    return Excel::download(new ExportKoreksiPerformance($data_performance), 'Template Koreksi Performance.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
  }

  public function import(Request $request)
  {
    // validasi
    $this->validate($request, [
      'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author
    ]);

    // menangkap file excel
    $file = $request->file('excel');

    // import data
    $import = new ImportKoreksiPerformance;
    Excel::import($import, $file);
    //list opco
    $plant = KilnPlant::select('kode_plant')->get()->toArray();
    $arrPlant = [];
    foreach ($plant as $value) {
      array_push($arrPlant, $value['kode_plant']);
    }


    $datas = ($import->data);
    $result = [];
    foreach ($datas as $data) {
      $format = $data;
      $message = "";
      $status = "Valid";


      if ($data['kode_plant'] == NULL) {
        $status = 'Invalid';
        $message = $message . "Kolom Plant tidak boleh kosong ";
      } else if (gettype($data['kode_plant']) != 'string') {
        $status = 'Invalid';
        $message = $message . "Kolom Plant hanya berisi huruf ";
      } else if (!in_array($data['kode_plant'], $arrPlant)) {
        $status = "Invalid";
        $message = $message . "Kolom Plant tidak ditemukan";
      }


      if ($data['tanggal'] == NULL) {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom Tanggal tidak boleh kosong ";
        } else {
          $message = $message . ", Kolom Tanggal tidak boleh kosong";
        }
      }

      if ($data['oph'] == NULL) {
        $format['oph'] = 0;
      } else if (gettype($data['oph']) != 'integer' and gettype($data['oph']) != 'float' and gettype($data['oph']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom oph  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom oph  hanya berisi angka";
        }
      }


      if ($data['updt'] == NULL) {
        $format['updt'] = 0;
      } else if (gettype($data['updt']) != 'integer' and gettype($data['updt']) != 'float' and gettype($data['updt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom updt  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom updt  hanya berisi angka";
        }
      }

      if ($data['pdt'] == NULL) {
        $format['pdt'] = 0;
      } else if (gettype($data['pdt']) != 'integer' and gettype($data['pdt']) != 'float' and gettype($data['pdt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom pdt  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom pdt  hanya berisi angka";
        }
      }

      if ($data['stop_idle'] == NULL) {
        $format['stop_idle'] = 0;
      } else if (gettype($data['stop_idle']) != 'integer' and gettype($data['stop_idle']) != 'float' and gettype($data['stop_idle']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom stop idle  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom stop idle  hanya berisi angka";
        }
      }

      if ($data['fy_stop'] == NULL) {
        $format['fy_stop'] = 0;
      } else if (gettype($data['fy_stop']) != 'integer' and gettype($data['fy_stop']) != 'float' and gettype($data['fy_stop']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom Fy Stop hanya berisi angka ";
        } else {
          $message = $message . ", Kolom Fy Stop hanya berisi angka";
        }
      }

      if ($data['frek_updt'] == NULL) {
        $format['frek_updt'] = 0;
      } else if (gettype($data['frek_updt']) != 'integer' and gettype($data['frek_updt']) != 'float' and gettype($data['frek_updt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom frek updt  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom frek updt  hanya berisi angka";
        }
      }

      if ($data['rkap_oph'] == NULL) {
        $format['rkap_oph'] = 0;
      } else if (gettype($data['rkap_oph']) != 'integer' and gettype($data['rkap_oph']) != 'float' and gettype($data['rkap_oph']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap oph  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap oph  hanya berisi angka";
        }
      }

      if ($data['rkap_updt'] == NULL) {
        $format['rkap_updt'] = 0;
      } else if (gettype($data['rkap_updt']) != 'integer' and gettype($data['rkap_updt']) != 'float' and gettype($data['rkap_updt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap updt  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap updt  hanya berisi angka";
        }
      }

      if ($data['rkap_pdt'] == NULL) {
        $format['rkap_pdt'] = 0;
      } else if (gettype($data['rkap_pdt']) != 'integer' and gettype($data['rkap_pdt']) != 'float' and gettype($data['rkap_pdt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap pdt  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap pdt  hanya berisi angka";
        }
      }

      if ($data['rkap_stop_idle'] == NULL) {
        $format['rkap_stop_idle'] = 0;
      } else if (gettype($data['rkap_stop_idle']) != 'integer' and gettype($data['rkap_stop_idle']) != 'float' and gettype($data['rkap_stop_idle']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap Stop Idle  hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap Stop Idle  hanya berisi angka";
        }
      }

      if ($data['rkap_frek_updt'] == NULL) {
        $format['rkap_frek_updt'] = 0;
      } else if (gettype($data['rkap_frek_updt']) != 'integer' and gettype($data['rkap_frek_updt']) != 'float' and gettype($data['rkap_frek_updt']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap frek updt hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap frek updt hanya berisi angka";
        }
      }

      if ($data['cal'] == NULL) {
        $format['cal'] = 0;
      } else if (gettype($data['cal']) != 'integer' and gettype($data['cal']) != 'float' and gettype($data['cal']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom cal hanya berisi angka ";
        } else {
          $message = $message . ", Kolom cal hanya berisi angka";
        }
      }

      if ($data['bdp_rate'] == NULL) {
        $format['bdp_rate'] = 0;
      } else if (gettype($data['bdp_rate']) != 'integer' and gettype($data['bdp_rate']) != 'float' and gettype($data['bdp_rate']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom bdp rate hanya berisi angka ";
        } else {
          $message = $message . ", Kolom bdp rate hanya berisi angka";
        }
      }

      if ($data['rkap_prod_rate'] == NULL) {
        $format['rkap_prod_rate'] = 0;
      } else if (gettype($data['rkap_prod_rate']) != 'integer' and gettype($data['rkap_prod_rate']) != 'float' and gettype($data['rkap_prod_rate']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap prod rate hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap prod rate hanya berisi angka";
        }
      }

      if ($data['koreksi'] == NULL) {
        $format['koreksi'] = 0;
      } else if (gettype($data['koreksi']) != 'integer' and gettype($data['koreksi']) != 'float' and gettype($data['koreksi']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom koreksi hanya berisi angka ";
        } else {
          $message = $message . ", Kolom koreksi hanya berisi angka";
        }
      }

      if ($data['act_prod'] == NULL) {
        $format['act_prod'] = 0;
      } else if (gettype($data['act_prod']) != 'integer' and gettype($data['act_prod']) != 'float' and gettype($data['act_prod']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom act prod hanya berisi angka ";
        } else {
          $message = $message . ", Kolom act prod hanya berisi angka";
        }
      }

      if ($data['rkap_prod'] == NULL) {
        $format['rkap_prod'] = 0;
      } else if (gettype($data['rkap_prod']) != 'integer' and gettype($data['rkap_prod']) != 'float' and gettype($data['rkap_prod']) != 'double') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom rkap prod hanya berisi angka ";
        } else {
          $message = $message . ", Kolom rkap prod hanya berisi angka";
        }
      }

      $format['is_valid'] = $status;
      $format['note'] = $message;
      array_push($result, $format);
    }
    $data     = DataTables::of($result)->make(true);
    $response = $data->getData(true);
    return response()->json($response, 200, [], JSON_PRETTY_PRINT);
  }

  public function getDataPerformance(Request $request)
  {
    try {

        $request->validate([
            'filter_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
        ]);

        $arr_bind = [];
        if ($request->filter_opco == "empty" || $request->filter_opco == "") {
            $filter_opco = '';
      } else {
            $val_opco = $request->filter_opco;
            $filter_opco = "AND kd_opco = ?";
            $arr_bind[] = $val_opco;
      }

        if ($request->filter_plant == "empty" || $request->filter_plant == "") {
            $filter_plant = '';
      } else {
            $val_plant = $request->filter_plant;
            $filter_plant = "AND kode_plant = ?";
            $arr_bind[] = $val_plant;
      }

        if ($request->filter_bulan == "empty" || $request->filter_bulan == "") {
            $filter_bulan = '';
      } else {
            $val_bulan = $request->filter_bulan;
            $filter_bulan = "AND TO_CHAR(tanggal::timestamp, 'MM') = ?";
            $arr_bind[] = $val_bulan;
      }

        if ($request->filter_tahun == "empty" || $request->filter_tahun == "") {
            $filter_tahun = '';
      } else {
            $val_tahun = $request->filter_tahun;
            $filter_tahun = "AND TO_CHAR(tanggal::timestamp, 'YYYY') = ?";
            $arr_bind[] = $val_tahun;
      }

        $data_performance = DB::select("select
                no_opco,
                holding,
                kd_opco,
                nm_opco,
                no_plant,
                kode_plant,
                tanggal,
                to_char(oph, '999G999G999G999G990D9') as oph,
                to_char(updt, '999G999G999G999G990D9') as updt,
                to_char(pdt, '999G999G999G999G990D9') as pdt,
                to_char(stop_idle, '999G999G999G999G990D9') as stop_idle,
                to_char(fy_stop, '999G999G999G999G990D9') as fy_stop,
                to_char(net_avail, '999G999G999G999G990D9') as net_avail,
                to_char(koreksi, '999G999G999G999G990D9') as koreksi,
                to_char(act_prod, '999G999G999G999G990D9') as act_prod,
                to_char(frek_updt, '999G999G999G999G990D9') as frek_updt,
                to_char(act_idle_prod, '999G999G999G999G990D9') as act_idle_prod,
                to_char(rate_gross, '999G999G999G999G990D9') as rate_gross,
                to_char(rate_netto, '999G999G999G999G990D9') as rate_netto,
                to_char(cal, '999G999G999G999G990D9') as cal,
                to_char(rkap_oph, '999G999G999G999G990D9') as rkap_oph,
                to_char(rkap_updt, '999G999G999G999G990D9') as rkap_updt,
                to_char(rkap_pdt, '999G999G999G999G990D9') as rkap_pdt,
                to_char(rkap_stop_idle, '999G999G999G999G990D9') as rkap_stop_idle,
                to_char(rkap_frek_updt, '999G999G999G999G990D9') as rkap_frek_updt,
                to_char(rkap_prod_vol, '999G999G999G999G990D9') as rkap_prod_vol,
                to_char(bdp_rate, '999G999G999G999G990D9') as bdp_rate,
                to_char(rkap_prod_rate, '999G999G999G999G990D9') as rkap_prod_rate
            from vw_tk_kiln_op_detail_all
            WHERE 1=1 " . $filter_plant . "  " . $filter_opco . " ".$filter_bulan." ".$filter_tahun.""
            , $arr_bind);

      return DataTables::of($data_performance)->make();
      return [
        'message' => 'Successfull Get Data',
        'status' => 'success',
        'data' => $data_performance,
      ];
    } catch (Exception $e) {
      return [
        'status' => 'failed',
        'message' => $e->getMessage()
      ];
    }
  }




  public function exportFr(Request $request)
  {
    list($datas, $SMI) = $this->getDataExportFR($request);
    $tahun = $request->filter_tahun;
    return Excel::download(new ExportDataKoreksiPerformanceFr($datas, $SMI, $tahun), 'Export Data Koreksi Performance FR.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
  }


  public function export(Request $request)
  {
    $request->validate([
      'filter_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
      'filter_opco' => 'nullable|exists:m_opco,kode_opco',
      'filter_bulan' => 'nullable|numeric',
      'filter_tahun' => 'nullable|numeric',
    ]);

    $data_performance =  DB::table('vw_tk_kiln_op_detail_all')
      ->select([
        'nm_opco',
        'kode_plant',
        'tanggal',
        'oph',
        'updt',
        'pdt',
        'stop_idle',
        'fy_stop',
        'net_avail',
        'koreksi',
        'act_prod',
        'frek_updt',
        'act_idle_prod',
        'rate_gross',
        'rate_netto',
        'cal',
        'rkap_oph',
        'rkap_updt',
        'rkap_pdt',
        'rkap_stop_idle',
        'rkap_frek_updt',
        'rkap_prod_vol',
        'bdp_rate',
        'rkap_prod_rate'
      ]);
      if($request->filter_opco){
        $data_performance = $data_performance -> where('kode_opco', $request->filter_opco);
      }
      if($request->filter_plant){
          $data_performance = $data_performance -> where('kode_plant', $request->filter_plant);
      }
      if($request->filter_tahun){
        $data_performance = $data_performance -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $request->filter_tahun);
      }
      if($request->filter_bulan){
          $data_performance = $data_performance -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $request->filter_bulan);
      }
      $data_performance = $data_performance->get();

    return Excel::download(new ExportDataKoreksiPerformance($data_performance), 'Export Data Koreksi Performance.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
  }

  public function getDataExportFR($request)
  {
    $request->validate([
      'filter_bulan' => 'nullable|numeric',
      'filter_tahun' => 'nullable|numeric',
    ]);
    $tahun = $request->filter_tahun;
    $filter_bulan = $request->filter_bulan;

    $now = Carbon::now();
    $nowMonth = $filter_bulan;
    $startMonth = Carbon::now()->startOfYear()->format('m');

    $dataRkap = DB::table('vw_tk_kiln_op_detail_all')->select(
      'kode_plant',
      'kd_opco as kode_opco',
      'no_plant',
      DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY') as tahun"),
      DB::raw("TO_CHAR(tanggal::timestamp, 'MM') as bulan"),
      DB::raw('SUM(rkap_prod_vol) as prod_rkap'),
      DB::raw('SUM(rkap_oph) as oph_rkap'),
      DB::raw('SUM(rkap_stop_idle) as idle_rkap'),
      DB::raw('SUM(cal) as cal_rkap'),
      DB::raw('AVG(rkap_prod_rate) as prod_rate_rkap'),
      DB::raw('AVG(bdp_rate) as bdp_rate_rkap'),
      DB::raw('SUM(rkap_updt) as updt_rkap'),
      DB::raw('SUM(rkap_frek_updt) as frek_updt_rkap'),
    );

    $dataRkapYTD = DB::table('vw_tk_kiln_op_detail_all')->select(
      'kode_plant',
      'kd_opco as kode_opco',
      'no_plant',
      DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY') as tahun"),
      DB::raw('SUM(rkap_prod_vol) as prod_rkap'),
      DB::raw('SUM(rkap_oph) as oph_rkap'),
      DB::raw('SUM(rkap_stop_idle) as idle_rkap'),
      DB::raw('SUM(cal) as cal_rkap'),
      DB::raw('AVG(rkap_prod_rate) as prod_rate_rkap'),
      DB::raw('AVG(bdp_rate) as bdp_rate_rkap'),
      DB::raw('SUM(rkap_updt) as updt_rkap'),
      DB::raw('SUM(rkap_frek_updt) as frek_updt_rkap'),
    );

    $dataReal = DB::table('vw_tk_kiln_op_detail_all')->select(
      'kd_opco as kode_opco',
      'kode_plant',
      'no_plant',
      DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY') as tahun"),
      DB::raw("TO_CHAR(tanggal::timestamp, 'MM') as bulan"),
      DB::raw('SUM(act_prod) as prod_real'),
      DB::raw('SUM(oph) as oph_real'),
      DB::raw('SUM(stop_idle) as idle_real'),
      DB::raw('SUM(cal) as cal_real'),
      DB::raw('SUM(koreksi) as koreksi_real'),
      DB::raw('SUM(act_idle_prod) as idle_prod_real'),
      DB::raw('SUM(updt) as updt_real'),
      DB::raw('AVG(bdp_rate) as bdp_rate_real'),
      DB::raw('SUM(frek_updt) as frek_updt_real'),
    );
    $dataRealYTD = DB::table('vw_tk_kiln_op_detail_all')->select(
      'kd_opco as kode_opco',
      'kode_plant',
      DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY') as tahun"),
      DB::raw('SUM(act_prod) as prod_real'),
      DB::raw('SUM(oph) as oph_real'),
      DB::raw('SUM(stop_idle) as idle_real'),
      DB::raw('SUM(cal) as cal_real'),
      DB::raw('SUM(koreksi) as koreksi_real'),
      DB::raw('SUM(act_idle_prod) as idle_prod_real'),
      DB::raw('SUM(updt) as updt_real'),
      DB::raw('AVG(bdp_rate) as bdp_rate_real'),
      DB::raw('SUM(frek_updt) as frek_updt_real'),
    );

    //Data Perbulan Sekarang
    $arr_tahun = [$request->filter_tahun, ($request->filter_tahun - 1)];
    $dataRealMonth = $dataReal->whereIn(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $arr_tahun);
    $dataRealMonth = $dataRealMonth->where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $nowMonth);
    $dataRealMonth = $dataRealMonth->where('kd_opco', '!=', 'SI2000');
    $dataRealMonth = $dataRealMonth->groupBy('kode_opco')->groupBy('kode_plant')->groupBy('tanggal')->groupBy('no_plant')->orderBy('no_plant')->get()->toArray();
    
    $dataRkapMonth = $dataRkap->where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $request->filter_tahun);
    $dataRkapMonth = $dataRkapMonth->where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $nowMonth);
    $dataRkapMonth = $dataRkapMonth->where('kd_opco', '!=', 'SI2000');
    $dataRkapMonth = $dataRkapMonth->groupBy('kode_opco')->groupBy('kode_plant')->groupBy('tanggal')->groupBy('no_plant')->orderBy('no_plant')->get()->toArray();

    //Data Year To Date
    $dataRealYTD = $dataRealYTD->whereIn(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $arr_tahun);
    $dataRealYTD = $dataRealYTD->whereBetween(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), [ $startMonth, $nowMonth]);
    $dataRealYTD = $dataRealYTD->where('kd_opco', '!=', 'SI2000');
    $dataRealYTD = $dataRealYTD->groupBy('kode_opco')->groupBy('kode_plant')->groupBy(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"))->groupBy('no_plant')->orderBy('no_plant')->get()->toArray();

    $dataRkapYTD = $dataRkapYTD->where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $request->filter_tahun);
    $dataRkapYTD = $dataRkapYTD->whereBetween(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), [ $startMonth, $nowMonth]);
    $dataRkapYTD = $dataRkapYTD->where('kd_opco', '!=', 'SI2000');
    $dataRkapYTD = $dataRkapYTD->groupBy('kode_opco')->groupBy('kode_plant')->groupBy(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"))->groupBy('no_plant')->orderBy('no_plant')->get()->toArray();

    foreach ($dataRealMonth as $key => $value) {
      $realMonth[$value->tahun][$value->kode_opco][]=  array(
        'kode_opco' => $value->kode_opco,
        'kode_plant' => $value->kode_plant,
        'prod_real' => $value->prod_real,
        'oph_real' => $value->oph_real,
        'idle_real' => $value->idle_real,
        'cal_real' => $value->cal_real,
        'koreksi_real' => $value->koreksi_real,
        'idle_prod_real' => $value->idle_prod_real,
        'oph_real' => $value->oph_real,
        'updt_real' => $value->updt_real,
        'frek_updt_real' => $value->frek_updt_real,
        'bulan' => $value->bulan,
        'tahun' => $value->tahun,
        'bdp_rate_real' => $value->bdp_rate_real,
      );
    }

    foreach ($dataRkapMonth as $key => $value) {
      $rkapMonth[$value->tahun][$value->kode_opco][] =  array(
        'kode_opco' => $value->kode_opco,
        'kode_plant' => $value->kode_plant,
        'prod_rkap' => $value->prod_rkap,
        'oph_rkap' => $value->oph_rkap,
        'idle_rkap' => $value->idle_rkap,
        'cal_rkap' => $value->cal_rkap,
        'prod_rate_rkap' => $value->prod_rate_rkap,
        'bdp_rate_rkap' => $value->bdp_rate_rkap,
        'updt_rkap' => $value->updt_rkap,
        'frek_updt_rkap' => $value->frek_updt_rkap,
        'bulan' => $value->bulan,
        'tahun' => $value->tahun
      );
      $opcoSMI[$value->kode_opco]=[];
      $opco[$value->kode_opco][$value->kode_plant]=[];
    }

    foreach ($dataRealYTD as $key => $value) {
      $realYTD[$value->tahun][$value->kode_opco][]=  array(
        'kode_opco' => $value->kode_opco,
        'kode_plant' => $value->kode_plant,
        'prod_real' => $value->prod_real,
        'oph_real' => $value->oph_real,
        'idle_real' => $value->idle_real,
        'cal_real' => $value->cal_real,
        'koreksi_real' => $value->koreksi_real,
        'idle_prod_real' => $value->idle_prod_real,
        'oph_real' => $value->oph_real,
        'updt_real' => $value->updt_real,
        'frek_updt_real' => $value->frek_updt_real,
        'tahun' => $value->tahun,
        'bdp_rate_real' => $value->bdp_rate_real,
      );
    }

    foreach ($dataRkapYTD as $key => $value) {
      $rkapYTD[$value->tahun][$value->kode_opco][] =  array(
        'kode_opco' => $value->kode_opco,
        'kode_plant' => $value->kode_plant,
        'prod_rkap' => $value->prod_rkap,
        'oph_rkap' => $value->oph_rkap,
        'idle_rkap' => $value->idle_rkap,
        'cal_rkap' => $value->cal_rkap,
        'prod_rate_rkap' => $value->prod_rate_rkap,
        'bdp_rate_rkap' => $value->bdp_rate_rkap,
        'updt_rkap' => $value->updt_rkap,
        'frek_updt_rkap' => $value->frek_updt_rkap,
        'tahun' => $value->tahun
      );
    }

    //Rumah data
    $kategori = ['PROD', 'AVAIL', 'YIELD', 'OEE', 'RASIO', 'MTBF', 'MTTR'];
    foreach ($kategori as $key => $kat) {
      $dataSMI[$kat]['SIG']=$opcoSMI;
      $dataAll[$kat]=$opco;
    }

    //Bulan Ini
    foreach ($realMonth as $thn => $tahun) {
      $sumSIGProd = 0;
      $sumSIGOph = 0;
      $sumSIGIdle = 0;
      $sumSIGCal = 0;
      $sumSIGTpd = 0;
      $sumSIGBdp = 0;
      $sumSIGUpdt = 0;
      $sumSIGFrekUpdt = 0;
      foreach ($tahun as $kOpco => $kdOpco) {
        $sumProd = 0;
        $sumOph = 0;
        $sumIdle = 0;
        $sumCal = 0;
        $sumTpd = 0;
        $sumBdp = 0;
        $sumUpdt = 0;
        $sumFrekUpdt = 0;
        foreach ($kdOpco as $plant => $value) {
          $prod_vol = $value['prod_real'];
          $val_prod_vol = $prod_vol;
          $nai = ($value['oph_real'] + $value['idle_real']) > 1 ? (($value['oph_real'] + $value['idle_real']) / $value['cal_real']) * 100 : ($value['oph_real'] + $value['idle_real']);
          $val_nai = round($nai,1);
          $tpd = 0;
          if((($value['oph_real'] + $value['idle_real']) / 24) >=1){
            $tpd += ($value['prod_real'] + $value['idle_prod_real'] + $value['koreksi_real']) / (($value['oph_real'] + $value['idle_real']) / 24);
          }else{
            $tpd += $value['prod_real'] + $value['idle_prod_real'] + $value['koreksi_real'];
          }
          $yield = $tpd > 1 && $value['bdp_rate_real'] > 0 ? ($tpd / $value['bdp_rate_real']) * 100 : $tpd * 100;
          $val_yield = round($yield,1);
          $oee = (($nai/100) * ($yield/100))*100;
          $val_oee = round($oee,1);
          $rasioUpdt = ($value['oph_real'] + $value['idle_real']) > 1 ? ($value['updt_real'] / ($value['oph_real'] + $value['idle_real'])) * 100 : $value['updt_real'] * 100;
          $val_rasioUpdt = round($rasioUpdt,1);
          $mtbf = $value['frek_updt_real'] > 1? $value['oph_real'] / $value['frek_updt_real'] : $value['oph_real'];
          $val_mtbf = round($mtbf,1);
          $mttr = $value['frek_updt_real'] > 1? $value['updt_real'] / $value['frek_updt_real'] : $value['updt_real'];
          $val_mttr = round($mttr,1);

          $sumProd += $value['prod_real'];
          $sumOph += $value['oph_real'];
          $sumIdle += $value['idle_real'];
          $sumCal += $value['cal_real'];
          $sumTpd += $tpd;
          $sumBdp += $value['bdp_rate_real'];
          $sumUpdt += $value['updt_real'];
          $sumFrekUpdt += $value['frek_updt_real'];

          $sumSIGProd += $value['prod_real'];
          $sumSIGOph += $value['oph_real'];
          $sumSIGIdle += $value['idle_real'];
          $sumSIGCal += $value['cal_real'];
          $sumSIGTpd += $tpd;
          $sumSIGBdp += $value['bdp_rate_real'];
          $sumSIGUpdt += $value['updt_real'];
          $sumSIGFrekUpdt += $value['frek_updt_real'];

          $dtReal[$thn][$kOpco][$value['kode_plant']]=[
            'kode_plant' => $value['kode_plant'],
            'kode_opco'  => $value['kode_opco'],
            'PROD'  => $val_prod_vol,
            'AVAIL' => $val_nai,
            'YIELD' => $val_yield,
            'OEE'   => $val_oee,
            'RASIO' => $val_rasioUpdt,
            'MTBF'  => $val_mtbf,
            'MTTR'  => $val_mttr
          ];
          $dtRealExt[$thn][$kOpco][$value['kode_plant']]=[
            'kode_plant' => $value['kode_plant'],
            'kode_opco'  => $value['kode_opco'],
            'PROD'  => round($prod_vol,4),
            'AVAIL' => round($nai,4),
            'YIELD' => round($yield,4),
            'OEE'   => round($oee,4),
            'RASIO' => round($rasioUpdt,4),
            'MTBF'  => round($mtbf,4),
            'MTTR'  => round($mttr,4)
          ];
        }
        $prod_vol = $sumProd;
        $val_prod_vol = $prod_vol;
        $nai = ($sumOph + $sumIdle) > 1 ? (($sumOph + $sumIdle) / $sumCal) * 100 : ($sumOph + $sumIdle);
        $val_nai = round($nai,1);
        $tpd = $sumTpd;
        $yield = $tpd > 1 && $sumBdp > 0 ? ($tpd / $sumBdp) * 100 : $tpd * 100;
        $val_yield = round($yield,1);
        $oee = (($nai/100) * ($yield/100))*100;
        $val_oee = round($oee,1);
        $rasioUpdt = ($sumOph + $sumIdle) > 1 ? ($sumUpdt / ($sumOph + $sumIdle)) * 100 : $sumUpdt * 100;
        $val_rasioUpdt = round($rasioUpdt,1);
        $mtbf = $sumFrekUpdt > 1? $sumOph / $sumFrekUpdt : $sumOph;
        $val_mtbf = round($mtbf,1);
        $mttr = $sumFrekUpdt > 1? $sumUpdt / $sumFrekUpdt : $sumUpdt;
        $val_mttr = round($mttr,1);

        if($kOpco != 'SG' and $kOpco != 'TLCC'){
          $dtReal[$thn][$kOpco][$kOpco]=[
            'kode_plant' => $kOpco,
            'kode_opco'  => $kOpco,
            'PROD'  => $val_prod_vol,
            'AVAIL' => $val_nai,
            'YIELD' => $val_yield,
            'OEE'   => $val_oee,
            'RASIO' => $val_rasioUpdt,
            'MTBF'  => $val_mtbf,
            'MTTR'  => $val_mttr
          ];
          $dtRealExt[$thn][$kOpco][$kOpco]=[
            'kode_plant' => $kOpco,
            'kode_opco'  => $kOpco,
            'PROD'  => round($prod_vol,4),
            'AVAIL' => round($nai,4),
            'YIELD' => round($yield,4),
            'OEE'   => round($oee,4),
            'RASIO' => round($rasioUpdt,4),
            'MTBF'  => round($mtbf,4),
            'MTTR'  => round($mttr,4)
          ];
        }
      }
      $dataSIG[$thn]['real_month'] = [
        'prod'  =>$sumSIGProd ,
        'oph'   =>$sumSIGOph ,
        'idle'  =>$sumSIGIdle ,
        'cal'   =>$sumSIGCal ,
        'tpd'   =>$sumSIGTpd ,
        'bdp'   =>$sumSIGBdp ,
        'updt'  =>$sumSIGUpdt ,
        'frekUpdt'  => $sumSIGFrekUpdt       
      ];
    }
    foreach ($rkapMonth as $thn => $tahun) {
      $sumSIGProd = 0;
      $sumSIGOph = 0;
      $sumSIGIdle = 0;
      $sumSIGCal = 0;
      $sumSIGTpd = 0;
      $sumSIGBdp = 0;
      $sumSIGUpdt = 0;
      $sumSIGFrekUpdt = 0;
      foreach ($tahun as $kOpco => $kdOpco) {
        $sumProd = 0;
        $sumOph = 0;
        $sumIdle = 0;
        $sumCal = 0;
        $sumTpd = 0;
        $sumBdp = 0;
        $sumUpdt = 0;
        $sumFrekUpdt = 0;
        foreach ($kdOpco as $plant => $value) {
          $prod_vol = $value['prod_rkap'];
          $val_prod_vol = $prod_vol;
          $nai = ($value['oph_rkap'] + $value['idle_rkap']) > 1 ? (($value['oph_rkap'] + $value['idle_rkap']) / $value['cal_rkap']) * 100 : ($value['oph_rkap'] + $value['idle_rkap']);
          $val_nai = round($nai,1);
          $tpd = $value['prod_rate_rkap'];
          $yield = $tpd > 1 && $value['bdp_rate_rkap'] > 0 ? ($tpd / $value['bdp_rate_rkap']) * 100 : $tpd * 100;
          $val_yield = round($yield,1);
          $oee = (($nai/100) * ($yield/100))*100;
          $val_oee = round($oee,1);
          $rasioUpdt = ($value['oph_rkap'] + $value['idle_rkap']) > 1 ? ($value['updt_rkap'] / ($value['oph_rkap'] + $value['idle_rkap'])) * 100 : $value['updt_rkap'] * 100;
          $val_rasioUpdt = round($rasioUpdt,1);
          $mtbf = $value['frek_updt_rkap'] > 1? $value['oph_rkap'] / $value['frek_updt_rkap'] : $value['oph_rkap'];
          $val_mtbf = round($mtbf,1);
          $mttr = $value['frek_updt_rkap'] > 1? $value['updt_rkap'] / $value['frek_updt_rkap'] : $value['updt_rkap'];
          $val_mttr = round($mttr,1);

          $sumProd += $value['prod_rkap'];
          $sumOph += $value['oph_rkap'];
          $sumIdle += $value['idle_rkap'];
          $sumCal += $value['cal_rkap'];
          $sumTpd += $tpd;
          $sumBdp += $value['bdp_rate_rkap'];
          $sumUpdt += $value['updt_rkap'];
          $sumFrekUpdt += $value['frek_updt_rkap'];

          $sumSIGProd += $value['prod_rkap'];
          $sumSIGOph += $value['oph_rkap'];
          $sumSIGIdle += $value['idle_rkap'];
          $sumSIGCal += $value['cal_rkap'];
          $sumSIGTpd += $tpd;
          $sumSIGBdp += $value['bdp_rate_rkap'];
          $sumSIGUpdt += $value['updt_rkap'];
          $sumSIGFrekUpdt += $value['frek_updt_rkap'];

          $dtRkap[$thn][$kOpco][$value['kode_plant']]=[
            'kode_plant' => $value['kode_plant'],
            'kode_opco'  => $value['kode_opco'],
            'PROD'  => $val_prod_vol,
            'AVAIL' => $val_nai,
            'YIELD' => $val_yield,
            'OEE'   => $val_oee,
            'RASIO' => $val_rasioUpdt,
            'MTBF'  => $val_mtbf,
            'MTTR'  => $val_mttr
          ];
          $dtRkapExt[$thn][$kOpco][$value['kode_plant']]=[
            'kode_plant' => $value['kode_plant'],
            'kode_opco'  => $value['kode_opco'],
            'PROD'  => round($prod_vol,3),
            'AVAIL' => round($nai,3),
            'YIELD' => round($yield,3),
            'OEE'   => round($oee,3),
            'RASIO' => round($rasioUpdt,3),
            'MTBF'  => round($mtbf,3),
            'MTTR'  => round($mttr,3)
          ];
        }
        $prod_vol = $sumProd;
        $val_prod_vol = $prod_vol;
        $nai = ($sumOph + $sumIdle) > 1 ? (($sumOph + $sumIdle) / $sumCal) * 100 : ($sumOph + $sumIdle);
        $val_nai = round($nai,1);
        $tpd = $sumTpd;
        $yield = $tpd > 1 && $sumBdp > 0 ? ($tpd / $sumBdp) * 100 : $tpd * 100;
        $val_yield = round($yield,1);
        $oee = (($nai/100) * ($yield/100))*100;
        $val_oee = round($oee,1);
        $rasioUpdt = ($sumOph + $sumIdle) > 1 ? ($sumUpdt / ($sumOph + $sumIdle)) * 100 : $sumUpdt * 100;
        $val_rasioUpdt = round($rasioUpdt,1);
        $mtbf = $sumFrekUpdt > 1? $sumOph / $sumFrekUpdt : $sumOph;
        $val_mtbf = round($mtbf,1);
        $mttr = $sumFrekUpdt > 1? $sumUpdt / $sumFrekUpdt : $sumUpdt;
        $val_mttr = round($mttr,1);
        if($kOpco != 'SG' and $kOpco != 'TLCC'){
          $dtRkap[$thn][$kOpco][$kOpco]=[
            'kode_plant' => $kOpco,
            'kode_opco'  => $kOpco,
            'PROD'  => $val_prod_vol,
            'AVAIL' => $val_nai,
            'YIELD' => $val_yield,
            'OEE'   => $val_oee,
            'RASIO' => $val_rasioUpdt,
            'MTBF'  => $val_mtbf,
            'MTTR'  => $val_mttr
          ];
          $dtRkapExt[$thn][$kOpco][$kOpco]=[
            'kode_plant' => $kOpco,
            'kode_opco'  => $kOpco,
            'PROD'  => round($prod_vol,3),
            'AVAIL' => round($nai,3),
            'YIELD' => round($yield,3),
            'OEE'   => round($oee,3),
            'RASIO' => round($rasioUpdt,3),
            'MTBF'  => round($mtbf,3),
            'MTTR'  => round($mttr,3)
          ];
        }
      }
      $dataSIG[$thn]['rkap_month'] = [
        'prod'  =>$sumSIGProd ,
        'oph'   =>$sumSIGOph ,
        'idle'  =>$sumSIGIdle ,
        'cal'   =>$sumSIGCal ,
        'tpd'   =>$sumSIGTpd ,
        'bdp'   =>$sumSIGBdp ,
        'updt'  =>$sumSIGUpdt ,
        'frekUpdt'  => $sumSIGFrekUpdt       
      ];
    }

    //Sd Bulan Ini
    foreach ($realYTD as $thn => $tahun) {
      $sumSIGProd = 0;
      $sumSIGOph = 0;
      $sumSIGIdle = 0;
      $sumSIGCal = 0;
      $sumSIGTpd = 0;
      $sumSIGBdp = 0;
      $sumSIGUpdt = 0;
      $sumSIGFrekUpdt = 0;
      foreach ($tahun as $kOpco => $kdOpco) {
        $sumProd = 0;
        $sumOph = 0;
        $sumIdle = 0;
        $sumCal = 0;
        $sumTpd = 0;
        $sumBdp = 0;  
        $sumUpdt = 0;
        $sumFrekUpdt = 0;
        foreach ($kdOpco as $plant => $value) {
          $prod_vol = $value['prod_real'];
          $val_prod_vol = $prod_vol;
          $nai = ($value['oph_real'] + $value['idle_real']) > 1 ? (($value['oph_real'] + $value['idle_real']) / $value['cal_real']) * 100 : ($value['oph_real'] + $value['idle_real']);
          $val_nai = round($nai,1);
          $tpd = 0;
          if((($value['oph_real'] + $value['idle_real']) / 24) >=1){
            $tpd += ($value['prod_real'] + $value['idle_prod_real'] + $value['koreksi_real']) / (($value['oph_real'] + $value['idle_real']) / 24);
          }else{
            $tpd += $value['prod_real'] + $value['idle_prod_real'] + $value['koreksi_real'];
          }
          $yield = $tpd > 1 && $value['bdp_rate_real'] > 0 ? ($tpd / $value['bdp_rate_real']) * 100 : $tpd * 100;
          $val_yield = round($yield,1);
          $oee = (($nai/100) * ($yield/100))*100;
          $val_oee = round($oee,1);
          $rasioUpdt = ($value['oph_real'] + $value['idle_real']) > 1 ? ($value['updt_real'] / ($value['oph_real'] + $value['idle_real'])) * 100 : $value['updt_real'] * 100;
          $val_rasioUpdt = round($rasioUpdt,1);
          $mtbf = $value['frek_updt_real'] > 1? $value['oph_real'] / $value['frek_updt_real'] : $value['oph_real'];
          $val_mtbf = round($mtbf,1);
          $mttr = $value['frek_updt_real'] > 1? $value['updt_real'] / $value['frek_updt_real'] : $value['updt_real'];
          $val_mttr = round($mttr,1);

          $sumProd += $value['prod_real'];
          $sumOph += $value['oph_real'];
          $sumIdle += $value['idle_real'];
          $sumCal += $value['cal_real'];
          $sumTpd += $tpd;
          $sumBdp += $value['bdp_rate_real'];
          $sumUpdt += $value['updt_real'];
          $sumFrekUpdt += $value['frek_updt_real'];

          $sumSIGProd += $value['prod_real'];
          $sumSIGOph += $value['oph_real'];
          $sumSIGIdle += $value['idle_real'];
          $sumSIGCal += $value['cal_real'];
          $sumSIGTpd += $tpd;
          $sumSIGBdp += $value['bdp_rate_real'];
          $sumSIGUpdt += $value['updt_real'];
          $sumSIGFrekUpdt += $value['frek_updt_real'];

          $dtRealYTD[$thn][$kOpco][$value['kode_plant']]=[
            'kode_plant' => $value['kode_plant'],
            'kode_opco'  => $value['kode_opco'],
            'PROD'  => $val_prod_vol,
            'AVAIL' => $val_nai,
            'YIELD' => $val_yield,
            'OEE'   => $val_oee,
            'RASIO' => $val_rasioUpdt,
            'MTBF'  => $val_mtbf,
            'MTTR'  => $val_mttr
          ];
          $dtRealYTDExt[$thn][$kOpco][$value['kode_plant']]=[
            'kode_plant' => $value['kode_plant'],
            'kode_opco'  => $value['kode_opco'],
            'PROD'  => round($prod_vol,4),
            'AVAIL' => round($nai,4),
            'YIELD' => round($yield,4),
            'OEE'   => round($oee,4),
            'RASIO' => round($rasioUpdt,4),
            'MTBF'  => round($mtbf,4),
            'MTTR'  => round($mttr,4)
          ];
        }
        $prod_vol = $sumProd;
        $val_prod_vol = $prod_vol;
        $nai = ($sumOph + $sumIdle) > 1 ? (($sumOph + $sumIdle) / $sumCal) * 100 : ($sumOph + $sumIdle);
        $val_nai = round($nai,1);
        $tpd = $sumTpd;
        $yield = $tpd > 1 && $sumBdp > 0 ? ($tpd / $sumBdp) * 100 : $tpd * 100;
        $val_yield = round($yield,1);
        $oee = (($nai/100) * ($yield/100))*100;
        $val_oee = round($oee,1);
        $rasioUpdt = ($sumOph + $sumIdle) > 1 ? ($sumUpdt / ($sumOph + $sumIdle)) * 100 : $sumUpdt * 100;
        $val_rasioUpdt = round($rasioUpdt,1);
        $mtbf = $sumFrekUpdt > 1? $sumOph / $sumFrekUpdt : $sumOph;
        $val_mtbf = round($mtbf,1);
        $mttr = $sumFrekUpdt > 1? $sumUpdt / $sumFrekUpdt : $sumUpdt;
        $val_mttr = round($mttr,1);

        if($kOpco != 'SG' and $kOpco != 'TLCC'){
          $dtRealYTD[$thn][$kOpco][$kOpco]=[
            'kode_plant' => $kOpco,
            'kode_opco'  => $kOpco,
            'PROD'  => $val_prod_vol,
            'AVAIL' => $val_nai,
            'YIELD' => $val_yield,
            'OEE'   => $val_oee,
            'RASIO' => $val_rasioUpdt,
            'MTBF'  => $val_mtbf,
            'MTTR'  => $val_mttr
          ];
          $dtRealYTDExt[$thn][$kOpco][$kOpco]=[
            'kode_plant' => $value['kode_plant'],
            'kode_opco'  => $value['kode_opco'],
            'PROD'  => round($prod_vol,4),
            'AVAIL' => round($nai,4),
            'YIELD' => round($yield,4),
            'OEE'   => round($oee,4),
            'RASIO' => round($rasioUpdt,4),
            'MTBF'  => round($mtbf,4),
            'MTTR'  => round($mttr,4)
          ];
        }
      }
      $dataSIG[$thn]['real_ytd_month'] = [
        'prod'  =>$sumSIGProd ,
        'oph'   =>$sumSIGOph ,
        'idle'  =>$sumSIGIdle ,
        'cal'   =>$sumSIGCal ,
        'tpd'   =>$sumSIGTpd ,
        'bdp'   =>$sumSIGBdp ,
        'updt'  =>$sumSIGUpdt ,
        'frekUpdt'  => $sumSIGFrekUpdt       
      ];
    }
    foreach ($rkapYTD as $thn => $tahun) {
      $sumSIGProd = 0;
      $sumSIGOph = 0;
      $sumSIGIdle = 0;
      $sumSIGCal = 0;
      $sumSIGTpd = 0;
      $sumSIGBdp = 0;
      $sumSIGUpdt = 0;
      $sumSIGFrekUpdt = 0;
      foreach ($tahun as $kOpco => $kdOpco) {
        $sumProd = 0;
        $sumOph = 0;
        $sumIdle = 0;
        $sumCal = 0;
        $sumTpd = 0;
        $sumBdp = 0;
        $sumUpdt = 0;
        $sumFrekUpdt = 0;
        foreach ($kdOpco as $plant => $value) {
          $prod_vol = $value['prod_rkap'];
          $val_prod_vol = $prod_vol;
          $nai = ($value['oph_rkap'] + $value['idle_rkap']) > 1 ? (($value['oph_rkap'] + $value['idle_rkap']) / $value['cal_rkap']) * 100 : ($value['oph_rkap'] + $value['idle_rkap']);
          $val_nai = round($nai,1);
          $tpd = $value['prod_rate_rkap'];
          $yield = $tpd > 1 && $value['bdp_rate_rkap'] > 0 ? ($tpd / $value['bdp_rate_rkap']) * 100 : $tpd * 100;
          $val_yield = round($yield,1);
          $oee = (($nai/100) * ($yield/100))*100;
          $val_oee = round($oee,1);
          $rasioUpdt = ($value['oph_rkap'] + $value['idle_rkap']) > 1 ? ($value['updt_rkap'] / ($value['oph_rkap'] + $value['idle_rkap'])) * 100 : $value['updt_rkap'] * 100;
          $val_rasioUpdt = round($rasioUpdt,1);
          $mtbf = $value['frek_updt_rkap'] > 1? $value['oph_rkap'] / $value['frek_updt_rkap'] : $value['oph_rkap'];
          $val_mtbf = round($mtbf,1);
          $mttr = $value['frek_updt_rkap'] > 1? $value['updt_rkap'] / $value['frek_updt_rkap'] : $value['updt_rkap'];
          $val_mttr = round($mttr,1);

          $sumProd += $value['prod_rkap'];
          $sumOph += $value['oph_rkap'];
          $sumIdle += $value['idle_rkap'];
          $sumCal += $value['cal_rkap'];
          $sumTpd += $tpd;
          $sumBdp += $value['bdp_rate_rkap'];
          $sumUpdt += $value['updt_rkap'];
          $sumFrekUpdt += $value['frek_updt_rkap'];

          $sumSIGProd += $value['prod_rkap'];
          $sumSIGOph += $value['oph_rkap'];
          $sumSIGIdle += $value['idle_rkap'];
          $sumSIGCal += $value['cal_rkap'];
          $sumSIGTpd += $tpd;
          $sumSIGBdp += $value['bdp_rate_rkap'];
          $sumSIGUpdt += $value['updt_rkap'];
          $sumSIGFrekUpdt += $value['frek_updt_rkap'];

          $dtRkapYTD[$thn][$kOpco][$value['kode_plant']]=[
            'kode_plant' => $value['kode_plant'],
            'kode_opco'  => $value['kode_opco'],
            'PROD'  => $val_prod_vol,
            'AVAIL' => $val_nai,
            'YIELD' => $val_yield,
            'OEE'   => $val_oee,
            'RASIO' => $val_rasioUpdt,
            'MTBF'  => $val_mtbf,
            'MTTR'  => $val_mttr
          ];
          $dtRkapYTDExt[$thn][$kOpco][$value['kode_plant']]=[
            'kode_plant' => $kOpco,
            'kode_opco'  => $kOpco,
            'PROD'  => round($prod_vol,4),
            'AVAIL' => round($nai,4),
            'YIELD' => round($yield,4),
            'OEE'   => round($oee,4),
            'RASIO' => round($rasioUpdt,4),
            'MTBF'  => round($mtbf,4),
            'MTTR'  => round($mttr,4)
          ];
        }
        $prod_vol = $sumProd;
        $val_prod_vol = $prod_vol;
        $nai = ($sumOph + $sumIdle) > 1 ? (($sumOph + $sumIdle) / $sumCal) * 100 : ($sumOph + $sumIdle);
        $val_nai = round($nai,1);
        $tpd = $sumTpd;
        $yield = $tpd > 1 && $sumBdp > 0 ? ($tpd / $sumBdp) * 100 : $tpd * 100;
        $val_yield = round($yield,1);
        $oee = (($nai/100) * ($yield/100))*100;
        $val_oee = round($oee,1);
        $rasioUpdt = ($sumOph + $sumIdle) > 1 ? ($sumUpdt / ($sumOph + $sumIdle)) * 100 : $sumUpdt * 100;
        $val_rasioUpdt = round($rasioUpdt,1);
        $mtbf = $sumFrekUpdt > 1? $sumOph / $sumFrekUpdt : $sumOph;
        $val_mtbf = round($mtbf,1);
        $mttr = $sumFrekUpdt > 1? $sumUpdt / $sumFrekUpdt : $sumUpdt;
        $val_mttr = round($mttr,1);

        if($kOpco != 'SG' and $kOpco != 'TLCC'){
          $dtRkapYTD[$thn][$kOpco][$kOpco]=[
            'kode_plant' => $kOpco,
            'kode_opco'  => $kOpco,
            'PROD'  => $val_prod_vol,
            'AVAIL' => $val_nai,
            'YIELD' => $val_yield,
            'OEE'   => $val_oee,
            'RASIO' => $val_rasioUpdt,
            'MTBF'  => $val_mtbf,
            'MTTR'  => $val_mttr
          ];
          $dtRkapYTDExt[$thn][$kOpco][$kOpco]=[
            'kode_plant' => $kOpco,
            'kode_opco'  => $kOpco,
            'PROD'  => round($prod_vol,4),
            'AVAIL' => round($nai,4),
            'YIELD' => round($yield,4),
            'OEE'   => round($oee,4),
            'RASIO' => round($rasioUpdt,4),
            'MTBF'  => round($mtbf,4),
            'MTTR'  => round($mttr,4)
          ];
        }
      }
      $dataSIG[$thn]['rkap_ytd_month'] = [
        'prod'  =>$sumSIGProd ,
        'oph'   =>$sumSIGOph ,
        'idle'  =>$sumSIGIdle ,
        'cal'   =>$sumSIGCal ,
        'tpd'   =>$sumSIGTpd ,
        'bdp'   =>$sumSIGBdp ,
        'updt'  =>$sumSIGUpdt ,
        'frekUpdt'  => $sumSIGFrekUpdt       
      ];
    }
    $tahun = $request->filter_tahun;
    foreach ($dataAll as $kKet => $ket) {
      foreach ($ket as $kOpco => $opco) {
        foreach ($opco as $kPlant => $plant) {
          $dataAll[$kKet][$kOpco][$kPlant] = [
            'kode_plant'  => $kPlant,
            'kode_opco'   => $kOpco,
            'rkap'.$tahun.'_this_month'       => $dtRkap[$tahun][$kOpco][$kPlant][$kKet] = $kKet == 'PROD' ? round($dtRkap[$tahun][$kOpco][$kPlant][$kKet],0):  $dtRkap[$tahun][$kOpco][$kPlant][$kKet],
            'real'.$tahun.'_this_month'       => $dtReal[$tahun][$kOpco][$kPlant][$kKet] = $kKet == 'PROD' ? round($dtReal[$tahun][$kOpco][$kPlant][$kKet],0):  $dtReal[$tahun][$kOpco][$kPlant][$kKet],
            'real'.($tahun-1).'_this_month'   => $dtReal[$tahun-1][$kOpco][$kPlant][$kKet] = $kKet == 'PROD' ? round($dtReal[$tahun-1][$kOpco][$kPlant][$kKet] ,0):  $dtReal[$tahun-1][$kOpco][$kPlant][$kKet] ,
            '2_1'  => $dtRkapExt[$tahun][$kOpco][$kPlant][$kKet] > 0 ? round(($dtRealExt[$tahun][$kOpco][$kPlant][$kKet] / $dtRkapExt[$tahun][$kOpco][$kPlant][$kKet]) * 100,1) : round($dtRealExt[$tahun][$kOpco][$kPlant][$kKet],1),
            '2_3'  => $dtRealExt[$tahun-1][$kOpco][$kPlant][$kKet] > 0 ? round(($dtRealExt[$tahun][$kOpco][$kPlant][$kKet] / $dtRealExt[$tahun-1][$kOpco][$kPlant][$kKet])* 100,1) : round($dtRealExt[$tahun][$kOpco][$kPlant][$kKet],1),
            'rkap'.$tahun.'_until_month'      =>$dtRkapYTD[$tahun][$kOpco][$kPlant][$kKet]= $kKet == 'PROD' ? round($dtRkapYTD[$tahun][$kOpco][$kPlant][$kKet],0):  $dtRkapYTD[$tahun][$kOpco][$kPlant][$kKet],
            'real'.$tahun.'_until_month'      =>$dtRealYTD[$tahun][$kOpco][$kPlant][$kKet]= $kKet == 'PROD' ? round($dtRealYTD[$tahun][$kOpco][$kPlant][$kKet],0):  $dtRealYTD[$tahun][$kOpco][$kPlant][$kKet],
            'real'.($tahun-1).'_until_month'  =>$dtRealYTD[$tahun-1][$kOpco][$kPlant][$kKet]= $kKet == 'PROD' ? round($dtRealYTD[$tahun-1][$kOpco][$kPlant][$kKet],0):  $dtRealYTD[$tahun-1][$kOpco][$kPlant][$kKet],
            '5_4'  => $dtRkapYTDExt[$tahun][$kOpco][$kPlant][$kKet] > 0 ? round(($dtRealYTDExt[$tahun][$kOpco][$kPlant][$kKet] / $dtRkapYTDExt[$tahun][$kOpco][$kPlant][$kKet]) * 100,1) : round($dtRealYTDExt[$tahun][$kOpco][$kPlant][$kKet],1),
            '5_6'  => $dtRealYTDExt[$tahun-1][$kOpco][$kPlant][$kKet] > 0 ? round(($dtRealYTDExt[$tahun][$kOpco][$kPlant][$kKet] / $dtRealYTDExt[$tahun-1][$kOpco][$kPlant][$kKet]) * 100,1) : round($dtRealYTDExt[$tahun][$kOpco][$kPlant][$kKet],1)
          ];
        }
        $dataAll[$kKet][$kOpco]['kode_opco'] = $kOpco;
        $dataAll[$kKet][$kOpco][$kOpco] = [
          'kode_plant'  => $kOpco,
          'kode_opco'   => $kOpco,
          'rkap'.$tahun.'_this_month'       => $dtRkap[$tahun][$kOpco][$kOpco][$kKet] = $kKet == 'PROD' ? round($dtRkap[$tahun][$kOpco][$kOpco][$kKet],0):  $dtRkap[$tahun][$kOpco][$kOpco][$kKet],
          'real'.$tahun.'_this_month'       => $dtReal[$tahun][$kOpco][$kOpco][$kKet]= $kKet == 'PROD' ? round($dtReal[$tahun][$kOpco][$kOpco][$kKet],0):  $dtReal[$tahun][$kOpco][$kOpco][$kKet],
          'real'.($tahun-1).'_this_month'   => $dtReal[$tahun-1][$kOpco][$kOpco][$kKet]= $kKet == 'PROD' ? round($dtReal[$tahun-1][$kOpco][$kOpco][$kKet],0):  $dtReal[$tahun-1][$kOpco][$kOpco][$kKet],
          '2_1'  => $dtRkapExt[$tahun][$kOpco][$kOpco][$kKet] > 0 ? round(($dtRealExt[$tahun][$kOpco][$kOpco][$kKet] / $dtRkapExt[$tahun][$kOpco][$kOpco][$kKet]) * 100,1) : round($dtRealExt[$tahun][$kOpco][$kOpco][$kKet],1),
          '2_3'  => $dtRealExt[$tahun-1][$kOpco][$kOpco][$kKet] > 0 ? round(($dtRealExt[$tahun][$kOpco][$kOpco][$kKet] / $dtRealExt[$tahun-1][$kOpco][$kOpco][$kKet])* 100,1) : round($dtRealExt[$tahun][$kOpco][$kOpco][$kKet],1),
          'rkap'.$tahun.'_until_month'      =>$dtRkapYTD[$tahun][$kOpco][$kOpco][$kKet] = $kKet == 'PROD' ? round($dtRkapYTD[$tahun][$kOpco][$kOpco][$kKet],0):  $dtRkapYTD[$tahun][$kOpco][$kOpco][$kKet],
          'real'.$tahun.'_until_month'      =>$dtRealYTD[$tahun][$kOpco][$kOpco][$kKet]= $kKet == 'PROD' ? round($dtRealYTD[$tahun][$kOpco][$kOpco][$kKet],0):  $dtRealYTD[$tahun][$kOpco][$kOpco][$kKet],
          'real'.($tahun-1).'_until_month'  =>$dtRealYTD[$tahun-1][$kOpco][$kOpco][$kKet]= $kKet == 'PROD' ? round($dtRealYTD[$tahun-1][$kOpco][$kOpco][$kKet],0):  $dtRealYTD[$tahun-1][$kOpco][$kOpco][$kKet],
          '5_4'  => $dtRkapYTDExt[$tahun][$kOpco][$kOpco][$kKet] > 0 ? round(($dtRealYTDExt[$tahun][$kOpco][$kOpco][$kKet] / $dtRkapYTDExt[$tahun][$kOpco][$kOpco][$kKet]) * 100,1) : round($dtRealYTDExt[$tahun][$kOpco][$kOpco][$kKet],1),
          '5_6'  => $dtRealYTDExt[$tahun-1][$kOpco][$kOpco][$kKet] > 0 ? round(($dtRealYTDExt[$tahun][$kOpco][$kOpco][$kKet] / $dtRealYTDExt[$tahun-1][$kOpco][$kOpco][$kKet]) * 100,1) : round($dtRealYTDExt[$tahun][$kOpco][$kOpco][$kKet],1)
        ];
      }
    }

    
    //Data SIG
    foreach ($dataSIG as $key => $value) {
      foreach ($value as $index => $val) {
        $prod_vol = $val['prod'];
        $val_prod_vol = $prod_vol;
        $nai = ($val['oph'] + $val['idle']) > 1 ? (($val['oph'] + $val['idle']) / $val['cal']) * 100 : ($val['oph'] + $val['idle']);
        $val_nai = round($nai,1);
        $tpd = $val['tpd'];
        $yield = $val['tpd'] > 1 && $val['bdp'] > 0 ? ($val['tpd'] / $val['bdp']) * 100 : $val['tpd'] * 100;
        $val_yield = round($yield,1);
        $oee = (($nai/100) * ($yield/100))*100;
        $val_oee = round($oee,1);
        $rasioUpdt = ($val['oph'] + $val['idle']) > 1 ? ($val['updt'] / ($val['oph'] + $val['idle'])) * 100 : $val['updt'] * 100;
        $val_rasioUpdt = round($rasioUpdt,1);
        $mtbf = $val['frekUpdt'] > 1? $val['oph'] / $val['frekUpdt'] : $val['oph'];
        $val_mtbf = round($mtbf,1);
        $mttr = $val['frekUpdt'] > 1? $val['updt'] / $val['frekUpdt'] : $val['updt'];
        $val_mttr = round($mttr,1);
        $dtSIG[$index.$key]=[
          'PROD'  => $val_prod_vol,
          'AVAIL' => $val_nai,
          'YIELD' => $val_yield,
          'OEE'   => $val_oee,
          'RASIO' => $val_rasioUpdt,
          'MTBF'  => $val_mtbf,
          'MTTR'  => $val_mttr
        ];
        $dtSIGExt[$index.$key]=[
          'PROD'  => round($prod_vol,4),
          'AVAIL' => round($nai,4),
          'YIELD' => round($yield,4),
          'OEE'   => round($oee,4),
          'RASIO' => round($rasioUpdt,4),
          'MTBF'  => round($mtbf,4),
          'MTTR'  => round($mttr,4)
        ];
      }
    }
    //===========================================================
    foreach ($dataSMI as $kKet => $ket) {
      foreach ($ket as $kSig => $sig) {
        foreach ($sig as $kOpco => $opco) {
          $dataSMI[$kKet][$kSig][$kOpco] = [
            'kode_opco'   => $kOpco,
            'rkap'.$tahun.'_this_month'       => $dtRkap[$tahun][$kOpco][$kOpco][$kKet] = $kKet == 'PROD' ? round($dtRkap[$tahun][$kOpco][$kOpco][$kKet],0):  $dtRkap[$tahun][$kOpco][$kOpco][$kKet],
            'real'.$tahun.'_this_month'       => $dtReal[$tahun][$kOpco][$kOpco][$kKet] = $kKet == 'PROD' ? round($dtReal[$tahun][$kOpco][$kOpco][$kKet],0):  $dtReal[$tahun][$kOpco][$kOpco][$kKet],
            'real'.($tahun-1).'_this_month'   => $dtReal[$tahun-1][$kOpco][$kOpco][$kKet] = $kKet == 'PROD' ? round($dtReal[$tahun-1][$kOpco][$kOpco][$kKet],0):  $dtReal[$tahun-1][$kOpco][$kOpco][$kKet],
            '2_1'  => $dtRkapExt[$tahun][$kOpco][$kOpco][$kKet] > 0 ? round(($dtRealExt[$tahun][$kOpco][$kOpco][$kKet] / $dtRkapExt[$tahun][$kOpco][$kOpco][$kKet]) * 100,1) : round($dtRealExt[$tahun][$kOpco][$kOpco][$kKet],1),
            '2_3'  => $dtRealExt[$tahun-1][$kOpco][$kOpco][$kKet] > 0 ? round(($dtRealExt[$tahun][$kOpco][$kOpco][$kKet] / $dtRealExt[$tahun-1][$kOpco][$kOpco][$kKet])* 100,1) : round($dtRealExt[$tahun][$kOpco][$kOpco][$kKet],1),
            'rkap'.$tahun.'_until_month'      =>$dtRkapYTD[$tahun][$kOpco][$kOpco][$kKet] = $kKet == 'PROD' ? round($dtRkapYTD[$tahun][$kOpco][$kOpco][$kKet],0):  $dtRkapYTD[$tahun][$kOpco][$kOpco][$kKet],
            'real'.$tahun.'_until_month'      =>$dtRealYTD[$tahun][$kOpco][$kOpco][$kKet] = $kKet == 'PROD' ? round($dtRealYTD[$tahun][$kOpco][$kOpco][$kKet],0):  $dtRealYTD[$tahun][$kOpco][$kOpco][$kKet],
            'real'.($tahun-1).'_until_month'  =>$dtRealYTD[$tahun-1][$kOpco][$kOpco][$kKet] = $kKet == 'PROD' ? round($dtRealYTD[$tahun-1][$kOpco][$kOpco][$kKet],0):  $dtRealYTD[$tahun-1][$kOpco][$kOpco][$kKet],
            '5_4'  => $dtRkapYTDExt[$tahun][$kOpco][$kOpco][$kKet] > 0 ? round(($dtRealYTD[$tahun][$kOpco][$kOpco][$kKet] / $dtRkapYTDExt[$tahun][$kOpco][$kOpco][$kKet]) * 100,1) : round($dtRealYTD[$tahun][$kOpco][$kOpco][$kKet],1),
            '5_6'  => $dtRealYTDExt[$tahun-1][$kOpco][$kOpco][$kKet] > 0 ? round(($dtRealYTDExt[$tahun][$kOpco][$kOpco][$kKet] / $dtRealYTDExt[$tahun-1][$kOpco][$kOpco][$kKet]) * 100,1) : round($dtRealYTDExt[$tahun][$kOpco][$kOpco][$kKet],1)
          ];
        }
      }
      $dataSMI[$kKet][$kSig]['kode'] = 'SIG';
      
      $dataSMI[$kKet][$kSig]['SIG'] = [
        'rkap'.$tahun.'_this_month'       => $dtSIG['rkap_month'.$tahun][$kKet] = $kKet == 'PROD' ? round($dtSIG['rkap_month'.$tahun][$kKet],0):  $dtSIG['rkap_month'.$tahun][$kKet],
        'real'.$tahun.'_this_month'       => $dtSIG['real_month'.$tahun][$kKet] = $kKet == 'PROD' ? round($dtSIG['real_month'.$tahun][$kKet],0):  $dtSIG['real_month'.$tahun][$kKet],
        'real'.($tahun-1).'_this_month'   => $dtSIG['real_month'.($tahun-1)][$kKet] = $kKet == 'PROD' ? round($dtSIG['real_month'.($tahun-1)][$kKet],0):  $dtSIG['real_month'.($tahun-1)][$kKet],
        '2_1'  => $dtSIGExt['rkap_month'.$tahun][$kKet] > 0 ? round(($dtSIGExt['real_month'.$tahun][$kKet] / $dtSIGExt['rkap_month'.$tahun][$kKet]) * 100,1) : round($dtSIGExt['real_month'.$tahun][$kKet],1),
        '2_3'  => $dtSIGExt['real_month'.($tahun-1)][$kKet] > 0 ? round(($dtSIGExt['real_month'.$tahun][$kKet] / $dtSIGExt['real_month'.($tahun-1)][$kKet])* 100,1) : round($dtSIGExt['real_month'.$tahun][$kKet],1),
        'rkap'.$tahun.'_until_month'      =>$dtSIG['rkap_ytd_month'.$tahun][$kKet] = $kKet == 'PROD' ? round($dtSIG['rkap_ytd_month'.$tahun][$kKet],0):  $dtSIG['rkap_ytd_month'.$tahun][$kKet],
        'real'.$tahun.'_until_month'      =>$dtSIG['real_ytd_month'.$tahun][$kKet] = $kKet == 'PROD' ? round($dtSIG['real_ytd_month'.$tahun][$kKet],0):  $dtSIG['real_ytd_month'.$tahun][$kKet],
        'real'.($tahun-1).'_until_month'  =>$dtSIG['real_ytd_month'.($tahun-1)][$kKet] = $kKet == 'PROD' ? round($dtSIG['real_ytd_month'.($tahun-1)][$kKet],0):  $dtSIG['real_ytd_month'.($tahun-1)][$kKet],
        '5_4'  => $dtSIGExt['rkap_ytd_month'.$tahun][$kKet] > 0 ? round(($dtSIGExt['real_ytd_month'.$tahun][$kKet] / $dtSIGExt['rkap_ytd_month'.$tahun][$kKet]) * 100,1) : round($dtSIGExt['real_ytd_month'.$tahun][$kKet],1),
        '5_6'  => $dtSIGExt['real_ytd_month'.($tahun-1)][$kKet] > 0 ? round(($dtSIGExt['real_ytd_month'.$tahun][$kKet] / $dtSIGExt['real_ytd_month'.($tahun-1)][$kKet])* 100,1) : round($dtSIGExt['real_ytd_month'.$tahun][$kKet],1),
      ];
    }
    return [$dataAll, $dataSMI];
  }
}
