<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Kiln_Stop;
use App\Models\ReasonStop;
use App\Models\User;
use App\Mail\CauseStopMail;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class CauseStopController extends Controller
{
    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $data['id']   = $id;
        $data['data']   = Kiln_Stop::find($id);
        $data['reason'] = ReasonStop::select('id_kategori', 'nama_kategori')->get();
        $data['group_reason'] = ReasonStop::select(DB::raw('DISTINCT m_reason_stop.group'))->get();
        return view('causeStop', $data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'jenis_downtime' => 'required',
            'id_kategori' => 'required',
            'alasan' => 'required'
        ]);
        $data = Kiln_Stop::find($request->id);

        $time_start = '00:00:00';
        $time_end = '00:00:00';

        $data_kiln_stop = Kiln_Stop::select(
                                    'id_kiln_stop',
                                    'is_sent',
                                    DB::raw("to_char(tanggal_mulai::timestamp, 'hh24:mi:ss'::text) as tanggal_mulai"),
                                    DB::raw("to_char(tanggal_selesai::timestamp, 'hh24:mi:ss'::text) as tanggal_selesai")
                                )->where('kode_plant', '=', $data->kode_plant)
                                ->where('id_kiln_stop', '>=', $data->id_kiln_stop)
                                ->orderby('id_kiln_stop', 'ASC')
                                ->get();

        foreach ($data_kiln_stop as $key => $value) {
        DB::beginTransaction();
        try {
            if (($value->is_sent == 1 || $value->is_sent == 2 || $value->is_sent == 0) && $key > 0) {
                break;
            }else{
                // jika tanggal mulai atau selesai tidak sama dengan 00:00
                Kiln_Stop::where('id_kiln_stop', $value->id_kiln_stop)->update([
                'jenis_downtime' => $request->jenis_downtime,
                'id_kategori' => $request->id_kategori,
                'alasan' => $request->alasan,
            ]);

            DB::commit();
            }

            //     return redirect('cause-stop/'.$request->id)->with('message_success', 'Data updated!');
            $response = responseSuccess(trans('messages.create-success'), ['']);
                $new_response = response()->json($response, 201);
        } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
                $new_response = response()->json($response, 500);
        }

        }
        return $new_response;

    }

    public function sendMail()
    {
        $data = Kiln_Stop::select('t_kiln_stop.*', 'm_kiln_plant.kode_opco')
            ->leftJoin('m_kiln_plant', 't_kiln_stop.kode_plant', '=', 'm_kiln_plant.kode_plant')
            ->whereNull('t_kiln_stop.jenis_downtime')
            ->where('t_kiln_stop.is_sent', 0)
            ->get();
        foreach ($data as $val) {
            $user = User::where('plant_relation', 'ilike', '%' . $val->kode_plant . '%')->first();
            $details = [
                'opco' => $val->kode_opco ?? '-',
                'plant' => $val->kode_plant,
                'url' => 'https://dev-dmm.ptsisi.id/'.'cause-stop/'.$val->id_kiln_stop
            ];
            if(!empty($user->email)){
                $row_kiln = Kiln_Stop::find($val->id_kiln_stop);
                try {
                    Mail::to($user->email)->send(new CauseStopMail($details));
                    $row_kiln->update([
                          'is_sent' => 1,
                          'keterangan_sent' => 'Berhasil Terkirim!',
                    ]);
                } catch (Exception $e) {
                    $row_kiln->update([
                          'is_sent' => 2,
                          'keterangan_sent' => 'Gagal Terkirim: '.$e,
                    ]);
                }
            }
        }
        dd("Email is Sent.");
    }

    public function sendMailTest()
    {
        $data = Kiln_Stop::select('t_kiln_stop.*', 'm_kiln_plant.kode_opco')
            ->leftJoin('m_kiln_plant', 't_kiln_stop.kode_plant', '=', 'm_kiln_plant.kode_plant')
            ->find(14);
        $email_sendto = '<EMAIL>';
        $details = [
            'opco' => $data->kode_opco ?? '-',
            'plant' => $data->kode_plant,
            'url' => 'https://dev-dmm.ptsisi.id/'.'cause-stop/'.'14'
        ];
        if(!empty($email_sendto)){
            Mail::to($email_sendto)->send(new CauseStopMail($details));
        }
        dd("Email is Sent.");
    }

    public function getCauseStop(Request $request){
        $typestop = $request->typestop;
        if ($typestop) {
            $reasonstop = ReasonStop::select(['id_kategori', 'nama_kategori'])->where('group', $typestop)->orderBy('id_kategori')->get()->toArray();
            $data = ['reasonstop' => $reasonstop];
            return response()->json($data, 200, [], JSON_PRETTY_PRINT);
        }
        $data = [
            'reasonstop' => [],
            'noData'=> true,
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }
}
