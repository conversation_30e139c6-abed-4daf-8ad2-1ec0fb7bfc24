<?php
namespace App\Services;
use phpsap\classes\Config\ConfigTypeA;
use phpsap\DateTime\SapDateTime;
use phpsap\saprfc\SapRfc;

/**
 * https://php-sap.github.io/
 * https://github.com/php-sap/saprfc-kralik
 */
class SAPRFCClass
{
    /**
     * Call SPP ZFM_GET_SURATTUGAS SPPD
     */
    public function getData_SPPD($request){
        //CallFunction
        /**
         * The imaginary SAP remote function requires a
         * date as input and will return a date as output.
         */
        $function = new SapRfc('ZFM_GET_SURATTUGAS');
        // /**
        //  * The instance in `$function` has no way to establish a connection
        //  * because it lacks the necessary configuration. Let's add that
        //  * missing configuration now.
        //  */
        $configuration = new ConfigTypeA([
            ConfigTypeA::JSON_ASHOST => '*************',
            ConfigTypeA::JSON_SYSNR  => '00',
            ConfigTypeA::JSON_CLIENT => '110',
            ConfigTypeA::JSON_USER   => 'sapsupport',
            ConfigTypeA::JSON_PASSWD => '2022Juli!'
        ]);

        $function->setConfiguration($configuration);

        // /**
        //  * SAP has a different idea than ISO about formatting dates and times. However
        //  * PHP/SAP has your back with \phpsap\DateTime\SapDateTime extending DateTime. 
        //  */
        $pernr = trim($request->pernr);
        $pernrPar = [];
        if($pernr!="")
        $pernrPar = [['PERNR'=>$pernr]];

        $sppdNo = trim($request->nosppd);
        $sppdPar = [];
        if($sppdNo!="")
        $sppdPar = [['SPPDNO'=>$sppdNo]];

        $stno = trim($request->stno);
        $stno_par = [];
        if($stno!="")
        $stno_par = [['STNO'=>$stno]];

        $skno = trim($request->skno);
        $skno_par = [];
        if($skno!="")
        $skno_par = [['SKNO'=>$skno]];

        $statusapp = trim($request->statusapp);
        $statusapp_par = [];
        if($statusapp!="")
        $statusapp_par = [['STATUS_APPROVAL'=>$statusapp]];

        $date = explode("-",$request->start_date);
        $sd = $date[2].".".$date[1].".".$date[0];
        $date = explode("-",$request->end_date);
        $ed = $date[2].".".$date[1].".".$date[0];
        $function->setParams([
            'INPUT_PERNR'  => $pernrPar,//[], //['PERNR'=>'']
            'INPUT_NOSPPD'  => $sppdPar,//[['SPPDNO'=>'20170005']],
            'INPUT_NOST'  => $stno_par,//[],//['STNO'=>'']
            'INPUT_NOSK'  => $skno_par,//[],//['SKNO'=>'']
            'STATUS_APPROVAL' => $statusapp_par,//'',//['S'=>'']
            'IM_BEGDA' => $sd,//(new \DateTime('2017-01-01'))->format(SapDateTime::SAP_DATE),
            'IM_ENDDA' => $ed//(new \DateTime('2017-12-31'))->format(SapDateTime::SAP_DATE)
        ]);
        /**
         * Now let's encode this remote function call.
         */
        // echo json_encode($function, JSON_PRETTY_PRINT) . PHP_EOL;die;
        /**
         * To actually call the SAP remote function, use invoke()
         */
        $result = $function->invoke();
        foreach($result['T_HEADER'] as $k=>$v){
            $result['T_HEADER'][$k]['INDEX'] = $k;
        }
        $arrdetail = [];
        foreach($result['T_OUTPUT'] as $k=>$v){
            $arrdetail[$v['SPPDNO']][] = $v;
        }
        $result['T_OUTPUT'] = $arrdetail;

        $arrdetail = [];
        foreach($result['T_OUTPUT_UM'] as $k=>$v){
            $arrdetail[$v['SPPDNO']][] = $v;
        }
        $result['T_OUTPUT_UM'] = $arrdetail;

        $arrdetail = [];
        foreach($result['T_OUTPUT_TMB'] as $k=>$v){
            $arrdetail[$v['SPPDNO']][] = $v;
        }
        $result['T_OUTPUT_TMB'] = $arrdetail;

        $arrdetail = [];
        foreach($result['T_OUTPUT_ACC'] as $k=>$v){
            $arrdetail[$v['SPPDNO']][] = $v;
        }
        $result['T_OUTPUT_ACC'] = $arrdetail;
        return $result;
        // echo json_encode($result, JSON_PRETTY_PRINT) . PHP_EOL;die;
    }
    public function getData_Anggaran(){
        //CallFunction
        /**
         * The imaginary SAP remote function requires a
         * date as input and will return a date as output.
         */
        $result = (new SapRfc(
            'ZFM_FR50',
            [
                'FIKRS' => 'SGG2',
                'VERSN' => '0',
                'BLDAT' => '20220404',
                'GJAHR' => '2022',
                'WGES' => 'X',
                'T_LINE'  => [[
                    'BLPOS' => "001",
                    "FICTR" => "2101432000",
                    "VAL0" => "1500",
                    "FIPEX" => "61120002"
                ]]
            ],
            new ConfigTypeA([
                ConfigTypeA::JSON_ASHOST => '**********',
                ConfigTypeA::JSON_SYSNR  => '00',
                ConfigTypeA::JSON_CLIENT => '030',
                ConfigTypeA::JSON_USER   => 'ANDRE',
                ConfigTypeA::JSON_PASSWD => 'sisi2015'
            ])
          ))->invoke();
        //   print_r($result['T_LINE']);exit;
        return $result;
        // echo json_encode($result, JSON_PRETTY_PRINT) . PHP_EOL;die;
    }
    public function store_Anggaran($query,$generate){
        $records = [];
        for ($i = 0; $i < count($query); $i++) {
            $child['BLPOS'] = "00".($i+1);
            $child['FICTR'] = $query[$i]->funds_center;
            $child['VAL0'] = strval($query[$i]->amount);
            $child['FIPEX'] = $query[$i]->commitment_itm;
            $records[$query[$i]->fm_area."#".$query[$i]->fiscal_year][] = $child;
            }
            $result = [];
            foreach ($records as $key => $value) {
                $result[$key] = (new SapRfc(
                    // print_r($records);exit;   
                    'ZFM_FR50',
                    [
                        'FIKRS' => explode('#',$key)[0],
                        'VERSN' => '0',
                        'BLDAT' => '20220404',
                        'GJAHR' => explode('#',$key)[1],
                        'WGES' => 'X',
                        'IDPROG' => $generate,
                        'T_LINE'  => $value
                    ],
                    new ConfigTypeA([
                        ConfigTypeA::JSON_ASHOST => '**********',
                        ConfigTypeA::JSON_SYSNR  => '00',
                        ConfigTypeA::JSON_CLIENT => '030',
                        ConfigTypeA::JSON_USER   => 'ANDRE',
                        ConfigTypeA::JSON_PASSWD => 'sisi2015'
                    ])
                  ))->invoke();
                
            }
           
        // print_r($result);exit;
        return $result;
    }
}