<?php

namespace App\Http\Controllers;

use App\Models\Kondisi;
use App\Models\EquipmentInspection;
use App\Models\PlantInspection;
use App\Models\Area;
use App\Models\ItemInspection;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ImportEditInspectionItem;
use App\Exports\ExportDataEditInspectionItem;
use App\Imports\CheckDetailPlantInspection;
use App\Models\PlantCatatan;

class DetailInspectionTransactionController extends Controller
{
    public function getEditInspectionItem(Request $request)
    {
        try {
            $item_inspection_transaction = ItemInspection::select('t_item_inspection.no_item_inspection', 't_item_inspection.nm_area', 't_item_inspection.id_equipment', 't_item_inspection.desc_equipment', 't_item_inspection.create_date', 'm_kondisi.nm_kondisi', 't_item_inspection.remark', 't_item_inspection.no_inspection')
                ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
                ->join('m_kondisi', 't_item_inspection.id_kondisi', '=', 'm_kondisi.id_kondisi')
                ->where('t_plant_inspection.id_inspection', $request->id_inspection)
                ->get();

            return DataTables::of($item_inspection_transaction)->make();
            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data' => $item_inspection_transaction,
            ];
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }

    public function getHeaderPlantInspection(Request $request)
    {
        $data_plant_inspection = PlantInspection::where('id_inspection', $request->id_inspection)->first();

        return [
            'message' => 'Succes Get Data',
            'data_plant' => $data_plant_inspection
        ];
    }

    public function getEditDetailCatatanDatatable(Request $request)
    {
        try {
            $comment_transaction = PlantCatatan::where('t_plant_catatan.id_inspection', $request->id_inspection)
                ->orderBy('id', 'asc')
                ->get();

            return DataTables::of($comment_transaction)->make();
            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data' => $comment_transaction,
            ];
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }

    public function editUpdateInspectionPlant(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = array(
                "status" => $request->status,
                "update_by" => Auth::user()->username,
                "update_date" => Carbon::now(),
            );
            PlantInspection::where('id_inspection', $request->id_inspection)->update($data);
            DB::commit();
            if ($request->txtCatatan != "") {
                $data_catatan = array(
                    "catatan" => $request->txtCatatan,
                    "create_date" => Carbon::now(),
                    "create_by" => Auth::user()->username,
                    "status" => $request->status,
                    "id_inspection" =>  $request->id_inspection,
                );
                PlantCatatan::insert($data_catatan);
                DB::commit();
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Success Input Plant Inspection'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $th->getMessage()
            ]);
        }
    }

    public function getEditAreaRelation(Request $request)
    {
        if ($request->id_area == "empty" || $request->id_area == "") {
            $id_area = '1=1';
        } else {
            $id_area = 'id_area in ( ' . $request->id_area . ' )';
        }
        $data_area = Area::whereraw($id_area)->get();

        return [
            'message' => 'Succes Get Data Area',
            'data' => $data_area
        ];
    }

    public function getExportItemInspectionData($id)
    {
        return Excel::download(new ExportDataEditInspectionItem($id), 'ExportDataInspectionItem.xlsx');
    }


    public function importDetailInspectionItem(Request $request)
    {
        ItemInspection::where('no_inspection', $request->no_inspection)->delete();
        Excel::import(new ImportEditInspectionItem($request->no_inspection), $request->file('excel_file'));

        return [
            'message' => 'Success Import Item Inspection',
            'status' => 'success'
        ];
    }

    public function checkDetailInspectionOnReview(Request $request)
    {
        if ($request->id_area != "") {
            $plant_inspection_transaction = PlantInspection::join('t_item_inspection', 't_item_inspection.no_inspection', '=', 't_plant_inspection.no_inspection')
                ->select('t_plant_inspection.no_inspection')
                ->where('status', 'On Review')
                ->whereRaw('id_area in (' . $request->id_area . ')')
                ->where('function', $request->fungsi)
                ->groupBy('t_plant_inspection.no_inspection')
                ->get();

            $area = PlantInspection::join('t_item_inspection', 't_item_inspection.no_inspection', '=', 't_plant_inspection.no_inspection')
                ->select('t_item_inspection.nm_area')
                ->where('status', 'On Review')
                ->whereRaw('id_area in (' . $request->id_area . ')')
                ->where('function', $request->fungsi)
                ->groupBy('t_item_inspection.nm_area')
                ->get();
        } else {
            $plant_inspection_transaction = "empty";
            $area = "empty";
        }

        return [
            'message' => 'Succes Get Data',
            'data' => $plant_inspection_transaction,
            'area' =>  $area
        ];
    }

    public function checkDetailImportExcel(Request $request)
    {
        $rows = Excel::toArray(new CheckDetailPlantInspection, $request->file('excel_file'));

        foreach ($rows[0] as $row => $value) {
            if ($value[2] != "id_area" and $value[2] != null) {
                $id_area[] = $value[2];
            }
        }

        $distinct_area = array_unique($id_area);
        foreach ($distinct_area as $idArea) {
            $uniq_area[] =  $idArea;
        }

        $newLangsComma = implode(",", $uniq_area);

        $plant_inspection_transaction = PlantInspection::join('t_item_inspection', 't_item_inspection.no_inspection', '=', 't_plant_inspection.no_inspection')
            ->select('t_plant_inspection.no_inspection')
            ->where('status', 'On Review')
            ->whereRaw('id_area in (' . $newLangsComma . ')')
            ->where('function', $request->fungsi)
            ->groupBy('t_plant_inspection.no_inspection')
            ->get();

        $area = PlantInspection::join('t_item_inspection', 't_item_inspection.no_inspection', '=', 't_plant_inspection.no_inspection')
            ->select('t_item_inspection.nm_area')
            ->where('status', 'On Review')
            ->whereRaw('id_area in (' . $newLangsComma . ')')
            ->where('function', $request->fungsi)
            ->groupBy('t_item_inspection.nm_area')
            ->get();

        return [
            'message' => 'Success Get Data',
            'data' => $plant_inspection_transaction,
            'area' => $area
        ];
    }

    public function getEditSummaryKondisi(Request $request)
    {
        $data_plant_inspection = PlantInspection::where('id_inspection', $request->id_inspection)->first();
        $data_count = ItemInspection::where('no_inspection', $data_plant_inspection->no_inspection)->count();

        $data_risk = DB::select("select nm_area,
        COUNT(CASE WHEN m_kondisi.nm_kondisi = 'GOOD' THEN 1 END) AS GOOD,
        COUNT(CASE WHEN m_kondisi.nm_kondisi = 'LOW RISK' THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN m_kondisi.nm_kondisi = 'MED RISK' THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN m_kondisi.nm_kondisi = 'HIGH RISK' THEN 1 END) AS HIGH_RISK
        FROM t_item_inspection
        join m_kondisi on t_item_inspection.id_kondisi = m_kondisi.id_kondisi
        where no_inspection ='" . $data_plant_inspection->no_inspection . "'
        GROUP BY t_item_inspection.nm_area");

        return [
            'message' => 'Succes Get Data',
            'data_risk' => $data_risk,
            'data_count' => $data_count
        ];
    }

    public function editDetailInspectionItem(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = array(
                "id_equipment" => $request->id_equipment,
                "desc_equipment" => $request->desc_equipment,
                "id_area" => $request->id_area,
                "nm_area" => $request->nm_area,
                "id_kondisi" => $request->id_kondisi,
                "no_inspection" => $request->no_inspection,
                "remark" => $request->remark,
                "create_date" => $request->date_inspection,
                "create_time" => Carbon::now(),
                "delete_mark" => 0,
                "update_by" => Auth::user()->username
            );
            ItemInspection::insert($data);
            DB::commit();
            return response()->json([
                'status' => 'success',
                'message' => 'Success Input Item Inspection'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $th->getMessage()
            ]);
        }
    }
    public function getDataUpdateItemInspection(Request $request)
    {
        if ($request->id_area == "empty" || $request->id_area == "") {
            $id_area = '1=1';
        } else {
            $id_area = 'id_area in ( ' . $request->id_area . ' )';
        }
        $area = Area::whereraw($id_area)->get();
        $condition = DB::table('m_kondisi')->get();
        $equipment = DB::table('m_equipment')->get();
        return [
            'message' => 'Succes Get Data',
            'data_condition' => $condition,
            'data_area' => $area,
            'data_equipment' => $equipment
        ];
    }

    public function deleteUpdateItemInspection(Request $request)
    {
        DB::beginTransaction();
        try { //Mencoba apakah ada koneksi internet / dan koneksi database
            ItemInspection::where('no_item_inspection', $request->no_item_inspection)->delete();
            DB::commit();
            return [
                'message' => 'Successfull Delete Data',
                'status' => 'success'
            ];
        } catch (Exception $e) { // Jika ada gangguan internet / dan koneksi daya base akan memberikan notigikasi error
            DB::rollback();
            return [
                'status' => 'failed',
                'message' => $e->getMessage() // kembalikan ke API pesan error
            ];
        }
    }
    public function updateItemInspection(Request $request)
    {
        DB::beginTransaction();
        try { //Mencoba apakah ada koneksi internet / dan koneksi database

            ItemInspection::where('no_item_inspection', $request->no_item_inspection)->update([

                "id_kondisi" => $request->edit_detail_id_kondisi,
                "remark" => $request->edit_detail_remark,
                "update_date" => Carbon::now(),
                "update_by" => Auth::user()->username
            ]);
            DB::commit();

            return [
                'message' => 'Successfull Update Data',
                'status' => 'success'
            ];
        } catch (Exception $e) { // Jika ada gangguan internet / dan koneksi daya base akan memberikan notigikasi error
            DB::rollback();
            return [
                'status' => 'failed',
                'message' => $e->getMessage() // kembalikan ke API pesan error
            ];
        }
    }
    public function getDataInspectionItem(Request $request)
    {
        try { //Mencoba apakah ada koneksi internet / dan koneksi database
            $inspection = ItemInspection::where('no_item_inspection', $request->no_item_inspection)->first();
            $condition = DB::table('m_kondisi')->get();
            $equipment = DB::table('m_equipment')->get();
            if ($request->id_area == "empty" || $request->id_area == "") {
                $id_area = '1=1';
            } else {
                $id_area = 'id_area in ( ' . $request->id_area . ' )';
            }
            $area = Area::whereraw($id_area)->get();

            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data_inspection' => $inspection,
                'data_condition' => $condition,
                'data_area' => $area,
                'data_equipment' => $equipment,
            ];
        } catch (Exception $e) { // Jika ada gangguan internet / dan koneksi daya base akan memberikan notigikasi error

            return [
                'status' => 'failed',
                'message' => $e->getMessage() // kembalikan ke API pesan error
            ];
        }
    }
}
