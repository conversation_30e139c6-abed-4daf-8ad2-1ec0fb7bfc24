<?php

use App\Models\Menu;
use App\Models\RoleHasMenu;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Role;

function getMenu()
{
    $user = Auth::user();
    $datas['role'] = Role::wherein('name', $user->getRoleNames())->where('guard_name', 'web')->get()->pluck('id');
    $datas['menu'] = RoleHasMenu::select('menu_id')->wherein('role_id', $datas['role'])->get()->pluck('menu_id')->unique();
    $arr = Menu::where('type', 'dashboard')->wherein('id', $datas['menu'])->where('status', 'y')->orderBy('order_no', 'ASC')->get()->toArray();
    return buildTree($arr, 0);
}

function buildTree(array $elements, $parentId = 0)
{
    $branch = array();

    foreach ($elements as $element) {
        if ($element['parent_id'] == $parentId) {
            $children = buildTree($elements, $element['id']);
            if ($children) {
                $element['menu_childs'] = $children;
            }
            $branch[] = $element += ['menu_childs' => []];
        }
    }

    return $branch;
}
