<?php

namespace App\Console\Commands;

use App\Models\ResumePlantEvent;
use App\Models\ViewResume;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ResumeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'resume';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate Resume Data';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        try {
            DB::beginTransaction();
            $date = Carbon::today()->format("Y-m-d");
            $resumes = ViewResume::where(DB::raw("to_char(tgl, 'YYYY-MM-DD')"), $date)->get();
            foreach ($resumes as $item) {
                ResumePlantEvent::updateOrCreate([
                    'kode_plant' => $item->kode_plant,
                    'tanggal' => $item->tgl
                ], [
                    'kode_opco' => $item->kode_opco,
                    'no_plant' => $item->no_plant,
                    'alasan' => data_get($item, 'alasan', ''),
                    'id_kategori' => $item->id_kategori,
                    'nama_kategori' => $item->nama_kategori,
                    'oph' => data_get($item, 'oph', 0),
                    'updt' => data_get($item, 'updt', 0),
                    'pdt' => data_get($item, 'pdt', 0),
                    'stop_idle' => data_get($item, 'stop_idle', 0),
                    'total' => data_get($item, 'total', 0),
                    'frek' => data_get($item, 'frek', 0),
                    'sort' => data_get($item, 'sort', 0),
                ]);
            }
            DB::commit();
            Log::info('Resume was created successfully');
        } catch (Exception $e) {
            DB::rollBack();
            Log::info('Failed create resume');
            Log::error($e->getMessage());
        }
    }
}
