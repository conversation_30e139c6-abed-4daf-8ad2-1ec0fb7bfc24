<?php

namespace App\Http\Controllers;

use App\Http\Requests\PlantRequest;
use App\Models\KilnPlant;
use App\Models\Plant;
use DataTables;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\Opco;
use App\Models\Routes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class PlantController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Plant',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'/plant',
                ],
                [
                    'title'=>'Plant',
                    'url'=>'',
                ]
            ],
        ];
        // $query = new Company();
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        $data['opcos'] = Opco::all();
        return view('plant', $data);
    }

    public function datatables(Request $request)
    {
        $query    = KilnPlant::select('id_kiln_plant','kode_opco','kode_plant','name_plant','reference_sap','reference_tis',
        'reference_opc_status','reference_opc_rate','reference_opc_output','source_system',
        DB::raw("to_char(factorial, '90D9999') as factorial"),)
        ->get()->toArray();

        foreach ($query as $key => $value) {
            if($value['reference_tis'] == NULL){
                $query[$key]['reference_tis'] = '-';
            }
            if($value['reference_opc_status'] == NULL){
                $query[$key]['reference_opc_status'] = '-';
            }
            if($value['reference_opc_output'] == NULL){
                $query[$key]['reference_opc_output'] = '-';
            }
            if($value['reference_opc_rate'] == NULL){
                $query[$key]['reference_opc_rate'] = '-';
            }
            if($value['source_system'] == NULL){
                $query[$key]['source_system'] = '-';
            }
        }

        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        // dd($data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function store(Request $request)
    {
        $request->validate([
            'kode_plant'            => 'required|unique:m_kiln_plant|max:30',
            'name_plant'            => 'required|unique:m_kiln_plant|max:30',
            'kode_opco'             => 'required',
            'reference_sap'         => 'required|max:30',
            'source_system'         => 'max:30',
            'reference_tis'         => 'max:30',
            'factorial'             => 'between:0,99.99',
            'reference_opc_status'  => 'max:100',
            'reference_opc_rate'    => 'max:100',
            'reference_opc_output'  => 'max:100',
        ]);

        $attributes = $request->only('kode_plant', 'name_plant', 'kode_opco','source_system','reference_sap', 'reference_tis', 'reference_opc_status', 'reference_opc_rate', 'reference_opc_output','factorial');

        $roles = [
            'kode_plant'            => 'required|unique:m_kiln_plant|max:30',
            'name_plant'            => 'required|unique:m_kiln_plant|max:30',
            'kode_opco'             => 'required',
            'reference_sap'         => 'required|max:30',
            'source_system'         => 'max:30',
            'reference_tis'         => 'max:30',
            'factorial'             => 'between:0,99.99',
            'reference_opc_status'  => 'max:100',
            'reference_opc_rate'    => 'max:100',
            'reference_opc_output'  => 'max:100',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'max'    => trans('messages.max'),
        ];
        $this->validators($attributes, $roles, $messages);

        if($request->source_system == "-"){
            $request->source_system = NULL;
        }
        if($request->factorial == NULL){
            $request->factorial = '0';
        }

        $plant = KilnPlant::create([
            'kode_plant' => $request->kode_plant,
            'name_plant' => $request->name_plant,
            'kode_opco' => $request->kode_opco,
            'source_system' => $request->source_system,
            'reference_sap' => $request->reference_sap,
            'reference_tis' => $request->reference_tis,
            'reference_opc_status' => $request->reference_opc_status,
            'reference_opc_rate' => $request->reference_opc_rate,
            'reference_opc_output' => $request->reference_opc_output,
            'factorial' => str_replace(',','.',$request->factorial),
        ]);

        $response = responseSuccess(trans('message.read-success'),$plant);
        // return $response;
        return response()->json($response,200);
    }

    public function show($plant)
    {

        $query   = KilnPlant::find($plant);

        if($query->source_system == NULL){
            $query->source_system = '-';
        }

        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }

    public function edit($id)
    {
        $query   = Plant::find($id);
        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        //
    }

    public function update($id, Request $request)
    {

        $request->validate([
            'kode_plantEdit'            => 'required|max:30',
            'name_plantEdit'            => 'required|max:30',
            'kode_opcoEdit'             => 'required',
            'reference_sapEdit'         => 'required|max:30',
            'source_systemEdit'         => 'max:30',
            'reference_tisEdit'         => 'max:30',
            'factorialEdit'             => 'between:0,99.99',
            'reference_opc_statusEdit'  => 'max:100',
            'reference_opc_rateEdit'    => 'max:100',
            'reference_opc_outputEdit'  => 'max:100',
        ]);

        $attributes = $request->only('kode_plantEdit', 'name_plantEdit', 'kode_opcoEdit','source_systemEdit','reference_sapEdit', 'reference_tisEdit', 'reference_opc_statusEdit', 'reference_opc_rateEdit', 'reference_opc_outputEdit','EditEdit');

        $roles = [
            'kode_plantEdit'            => 'required|max:30',
            'name_plantEdit'            => 'required|max:30',
            'kode_opcoEdit'             => 'required',
            'reference_sapEdit'         => 'required|max:30',
            'source_systemEdit'         => 'max:30',
            'reference_tisEdit'         => 'max:30',
            'factorialEdit'             => 'between:0,99.99',
            'reference_opc_statusEdit'  => 'max:100',
            'reference_opc_rateEdit'    => 'max:100',
            'reference_opc_outputEdit'  => 'max:100',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'max'    => trans('messages.max'),
        ];
        $this->validators($attributes, $roles, $messages);

        if($request->source_systemEdit == ""){
            $request->source_systemEdit = NULL;
        }
        if($request->source_rateEdit == ""){
            $request->source_rateEdit = NULL;
        }
        if($request->source_rateEdit == ""){
            $request->source_rateEdit = NULL;
        }
        if($request->source_systemEdit == "-"){
            $request->source_systemEdit = NULL;
        }
        if($request->factorialEdit == NULL){
            $request->factorialEdit = '0';
        }

          $data = $this->findDataWhere(KilnPlant::class, ['id_kiln_plant' => $id]);

        //   dd($data);exit();
          DB::beginTransaction();
          try {
              $data->update([
                    'kode_plant' => $request->kode_plantEdit,
                    'name_plant' => $request->name_plantEdit,
                    'kode_opco' => $request->kode_opcoEdit,
                    'reference_sap' => $request->reference_sapEdit,
                    'source_system' => $request->source_systemEdit,
                    'reference_tis' => $request->reference_tisEdit,
                    'reference_opc_status' => $request->reference_opc_statusEdit,
                    'reference_opc_rate' => $request->reference_opc_rateEdit,
                    'reference_opc_output' => $request->reference_opc_outputEdit,
                    'factorial' => str_replace(',','.',$request->factorialEdit),
                ]);
              DB::commit();
              $response = responseSuccess(trans("messages.update-success"), $data);
              return response()->json($response, 200, [], JSON_PRETTY_PRINT);
          } catch (Exception $e) {
              DB::rollback();
              $response = responseFail(trans("messages.update-fail"), $e->getMessage());
              return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }

    }

    public function destroy($id)
    {

        KilnPlant::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }

    public function byOpco($opco)
    {
        $model = KilnPlant::where('kode_opco', $opco)->get();
        $response = responseSuccess(trans("messages.read-success"), $model);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }
}
