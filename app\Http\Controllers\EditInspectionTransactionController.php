<?php

namespace App\Http\Controllers;

use App\Models\Kondisi;
use App\Models\EquipmentInspection;
use App\Models\PlantInspection;
use App\Models\Area;
use App\Models\UserFunctional;
use App\Models\ItemInspection;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ImportDetailInspectionItem;
use App\Exports\ExportDataDetailInspectionItem;
use App\Models\PlantCatatan;

class EditInspectionTransactionController extends Controller
{
    public function getInspectionItem(Request $request)
    {
        try {
            $item_inspection_transaction = ItemInspection::select('t_item_inspection.no_item_inspection', 't_item_inspection.nm_area', 't_item_inspection.id_equipment', 't_item_inspection.desc_equipment', 't_item_inspection.create_date', 'm_kondisi.nm_kondisi', 't_item_inspection.remark', 't_item_inspection.no_inspection')
                ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
                ->join('m_kondisi', 't_item_inspection.id_kondisi', '=', 'm_kondisi.id_kondisi')
                ->where('t_plant_inspection.id_inspection', $request->id_inspection)
                ->orderBy('no_item_inspection','asc')
                ->get();

            return DataTables::of($item_inspection_transaction)->make();
            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data' => $item_inspection_transaction,
            ];
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }

    public function getEditDetailPlantInspection(Request $request)
    {
        $data_plant_inspection = PlantInspection::where('id_inspection', $request->id_inspection)->first();
        $data_item_inspection = ItemInspection::where('no_inspection', $data_plant_inspection->no_inspection)->get();
        $area_id = [];
        foreach ($data_item_inspection as $val) {
            $area_id[] = $val->id_area;
        }
        $data_area = Area::whereIn('id_area', $area_id)->get();
        $areas = '';
        foreach ($data_area as $key => $val) {
            if($key == 0){
                $areas .= $val->nm_area;
            }else{
                $areas .= ', '.$val->nm_area;
            }
        }

        return [
            'message' => 'Succes Get Data',
            'data_plant' => $data_plant_inspection,
            'data_area' => $areas
        ];
    }

    public function getDetailCatatanDatatable(Request $request)
    {
        try {
            $comment_transaction = PlantCatatan::where('t_plant_catatan.id_inspection', $request->id_inspection)
            ->orderBy('id','asc')
                ->get();

            return DataTables::of($comment_transaction)->make();
            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data' => $comment_transaction,
            ];
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }

    public function getDetailCatatan(Request $request)
    {
        try {
            $comment_transaction = PlantCatatan::where('t_plant_catatan.id_inspection', $request->id_inspection)->orderBY('id', 'DESC')
                ->first();

            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data' => $comment_transaction,
            ];
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }

    public function updateInspectionPlant(Request $request)
    {
        DB::beginTransaction();  
        try {
            $data = array(
                "desc_inspection" => $request->decInspection,
                "kode_opco" => $request->opco,
                "kode_plant" => $request->plant,
                "status" => $request->status,
                "no_inspection" => $request->noInspection,
                "update_by" => Auth::user()->username,
                "update_date" => Carbon::now(),
            );
            PlantInspection::where('id_inspection', $request->id_inspection)->update($data);

            if ($request->txtCatatan != "") {
                $data_catatan = array(
                    "catatan" => $request->txtCatatan,
                    "create_date" => Carbon::now(),
                    "create_by" => Auth::user()->username,
                    "status" => $request->status,
                    "id_inspection" =>  $request->id_inspection,
                );
                PlantCatatan::insert($data_catatan);
                DB::commit();
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Success Input Plant Inspection'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $th->getMessage()
            ]);
        }
    }

    public function getAreaRelation(Request $request)
    {
        if ($request->id_area == "empty" || $request->id_area == "") {
            $id_area = '1=1';
        } else {
            $id_area = 'id_area in ( ' . $request->id_area . ' )';
        }
        $data_area = Area::whereraw($id_area)->get();

        return [
            'message' => 'Succes Get Data Area',
            'data' => $data_area
        ];
    }

    public function getExportInspectionData($id,$fungsi)
    {
        return Excel::download(new ExportDataDetailInspectionItem($id,$fungsi), 'ExportDataInspectionItem-'.Carbon::now().'.xlsx');
    }
 

    public function importEditInspectionItem(Request $request)
    {
        Excel::import(new ImportDetailInspectionItem($request->no_inspection), $request->file('excel_file'));

        return [
            'message' => 'Success Import Item Inspection',
            'status' => 'success'
        ];
    }

    public function getDetailSummaryKondisi(Request $request)
    {
        $data_plant_inspection = PlantInspection::where('id_inspection', $request->id_inspection)->first();
        $periode = date('Y-m');
        $func = UserFunctional::get();
        $area = Area::get();
        $data_risk = [];
        foreach($area as $val){
            foreach ($func as $value) {
                $data_risk[$val->nm_area][$value->name] = DB::select("select 
                COUNT(CASE WHEN m_kondisi.nm_kondisi = 'GOOD' THEN 1 END) AS GOOD,
                COUNT(CASE WHEN m_kondisi.nm_kondisi = 'LOW RISK' THEN 1 END) AS LOW_RISK,
                COUNT(CASE WHEN m_kondisi.nm_kondisi = 'MED RISK' THEN 1 END) AS MED_RISK,
                COUNT(CASE WHEN m_kondisi.nm_kondisi = 'HIGH RISK' THEN 1 END) AS HIGH_RISK
                FROM t_item_inspection
                join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection
                join m_kondisi on t_item_inspection.id_kondisi = m_kondisi.id_kondisi
                WHERE (t_plant_inspection.status in('Approve') or t_plant_inspection.no_inspection = '".$data_plant_inspection->no_inspection."')
                AND nm_area = '".$val->nm_area."'
                AND function = '".$value->name."'
                AND t_plant_inspection.kode_plant = '".$data_plant_inspection->kode_plant."'
                ");
            }
        }

        foreach($data_risk as $function_nm => $data){
            $ls_func = array_keys($data);
        }

        $html = '
            <table style="border:1px solid black; border-collapse: collapse;">
                <tr>
                <th rowspan="2" style="border:1px solid black; border-collapse: collapse; text-align:center;">Area</th>';
        $i = 0;
        foreach($ls_func as $val){
            $html .= '<th colspan="4" style="border:1px solid black; border-collapse: collapse; text-align:center;">'.$val.'</th>';
            $i++;
        }
        $html .= '</tr>
                <tr>';
        for($j=0;$j<$i;$j++){
            $html .= '<th style="border:1px solid black; border-collapse: collapse;">Good</th>
                    <th style="border:1px solid black; border-collapse: collapse;">Low Risk</th>
                    <th style="border:1px solid black; border-collapse: collapse;">Med Risk</th>
                    <th style="border:1px solid black; border-collapse: collapse;">High Risk</th>';
        }
        $html .= '</tr>';

        foreach($data_risk as $area_nm => $data){
            $html .= '<tr>';
            $html .= '<td style="border:1px solid black; border-collapse: collapse;">'.$area_nm.'</td>';
            foreach($data as $func_nm => $val){
                $html .= '<td style="border:1px solid black; border-collapse: collapse;" class="text-center" bgcolor="lime"><b>'.$val[0]->good.'</b></td>';
                $html .= '<td style="border:1px solid black; border-collapse: collapse;" class="text-center" bgcolor="yellow"><b>'.$val[0]->low_risk.'</b></td>';
                $html .= '<td style="border:1px solid black; border-collapse: collapse;" class="text-center" bgcolor="orange"><b>'.$val[0]->med_risk.'</b></td>';
                $html .= '<td style="border:1px solid black; border-collapse: collapse;" class="text-center" bgcolor="red"><b>'.$val[0]->high_risk.'</b></td>';
            }
            $html .= '</tr>';
        }

        $html .= '
            </table>
        ';
        

        $data_risk = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE (t_plant_inspection.status in('Approve') or t_plant_inspection.no_inspection = '".$data_plant_inspection->no_inspection."')
                        AND t_plant_inspection.kode_plant = '".$data_plant_inspection->kode_plant."'
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area");

        return [
            'message' => 'Succes Get Data',
            'data_risk' => $data_risk,
            'data_table' => $html
        ];
    }

    public function editAddInspectionItem(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = array(
                "id_equipment" => $request->id_equipment,
                "desc_equipment" => $request->desc_equipment,
                "id_area" => $request->id_area,
                "nm_area" => $request->nm_area,
                "id_kondisi" => $request->id_kondisi,
                "no_inspection" => $request->no_inspection,
                "remark" => $request->remark,
                "create_date" => $request->date_inspection,
                "create_time" => Carbon::now(),
                "delete_mark" => 0,
                "update_by" => Auth::user()->username
            );
            ItemInspection::insert($data);
            DB::commit();
            return response()->json([
                'status' => 'success',
                'message' => 'Success Input Item Inspection'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $th->getMessage()
            ]);
        }
    }
    public function getDataEditItemInspection(Request $request)
    {
        if ($request->id_area == "empty" || $request->id_area == "") {
            $id_area = '1=1';
        } else {
            $id_area = 'id_area in ( ' . $request->id_area . ' )';
        }
        $area = Area::whereraw($id_area)->get();
        $condition = DB::table('m_kondisi')->get();
        $equipment = DB::table('m_equipment')->get();
        return [
            'message' => 'Succes Get Data',
            'data_condition' => $condition,
            'data_area' => $area,
            'data_equipment' => $equipment
        ];
    }

    public function deleteItemInspection(Request $request)
    {
        DB::beginTransaction();
        try { //Mencoba apakah ada koneksi internet / dan koneksi database
            ItemInspection::where('no_item_inspection', $request->no_item_inspection)->delete();
            DB::commit();
            return [
                'message' => 'Successfull Delete Data',
                'status' => 'success'
            ];
        } catch (Exception $e) { // Jika ada gangguan internet / dan koneksi daya base akan memberikan notigikasi error
            DB::rollback();
            return [
                'status' => 'failed',
                'message' => $e->getMessage() // kembalikan ke API pesan error
            ];
        }
    }
    public function updateItemInspection(Request $request)
    { 
        DB::beginTransaction();
        try { //Mencoba apakah ada koneksi internet / dan koneksi database

            ItemInspection::where('no_item_inspection', $request->no_item_inspection)->update([

                "id_kondisi" => $request->edit_detail_id_kondisi,
                "remark" => $request->edit_detail_remark,
                "update_date" => Carbon::now(),
                "update_by" => Auth::user()->username
            ]);
            DB::commit();

            return [
                'message' => 'Successfull Update Data',
                'status' => 'success'
            ];
        } catch (Exception $e) { // Jika ada gangguan internet / dan koneksi daya base akan memberikan notigikasi error
            DB::rollback();
            return [
                'status' => 'failed',
                'message' => $e->getMessage() // kembalikan ke API pesan error
            ];
        }
    }
    public function getDataUpdateInspectionItem(Request $request)
    {
        try { //Mencoba apakah ada koneksi internet / dan koneksi database
            $inspection = ItemInspection::where('no_item_inspection', $request->no_item_inspection)->first();
            $condition = DB::table('m_kondisi')->get();
            $equipment = DB::table('m_equipment')->get();
            $area = Area::query();
            if ($request->id_area != "empty" && $request->id_area != "") {
                $area->whereIn('id_area', explode(',', $request->id_area));
            }
            $area = $area->get();

            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data_inspection' => $inspection,
                'data_condition' => $condition,
                'data_area' => $area,
                'data_equipment' => $equipment,
            ];
        } catch (Exception $e) { // Jika ada gangguan internet / dan koneksi daya base akan memberikan notigikasi error

            return [
                'status' => 'failed',
                'message' => $e->getMessage() // kembalikan ke API pesan error
            ];
        }
    }
}
