<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\InitiatorRequest;
use DataTables;
use App\Models\Menu;
use App\Models\Routes;
use App\Models\Initiator;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class InitiatorController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();

        return view('Initiator', $data);
    }

    public function datatables(Request $request)
    {
        $query    = Initiator::get();
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $initiator = Initiator::create([
            'name' => $request->name,
        ]);

        $response = responseSuccess(trans('message.read-success'),$initiator);
        // return $response;
        return response()->json($response,200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $query   = Initiator::find($initiator);
        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $query   = Initiator::find($id);
        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $this->findDataWhere(Initiator::class, ['id' => $id]);

        //   dd($data);exit();
        DB::beginTransaction();
        try {
            $data->update([
                    'name' => $request->name,
                ]);
                DB::commit();
                $response = responseSuccess(trans("messages.update-success"), $data);
                return response()->json($response, 200, [], JSON_PRETTY_PRINT);
            } catch (Exception $e) {
                DB::rollback();
                $response = responseFail(trans("messages.update-fail"), $e->getMessage());
                return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        Initiator::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }
}
