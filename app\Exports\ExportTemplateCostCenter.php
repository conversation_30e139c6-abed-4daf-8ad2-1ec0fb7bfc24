<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ExportTemplateCostCenter implements WithHeadings, ShouldAutoSize, WithEvents, WithTitle, WithColumnWidths, WithStyles
{
    //set header value
    public function headings():array
    {
        return [
            'Cost Center',
            'Cost Center Name',
            'Kode OPCO',
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                //set background color for header (A1:D1)
                $event->sheet->getDelegate()
                ->getStyle('A1:C1')
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('C4D79B');
            },
        ];
    }

    //set name for worksheet
    public function title(): string
    {
        return 'Cost Center';
    }

    //set width for header
    public function columnWidths(): array
    {
        return [
            'A' => 15,
            'B' => 15,    
            'C' => 22,    
        ];
    }

    //set bold for header
    public function styles(Worksheet $sheet)
    {
        return [
            'A'    => ['font' => ['bold' => true]],
            'B'    => ['font' => ['bold' => true]],
            'C'    => ['font' => ['bold' => true]],
        ];
    }

}
