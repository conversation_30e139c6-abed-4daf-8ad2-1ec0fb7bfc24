<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMCostElement extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_cost_element', function (Blueprint $table) {
            $table->string('cost_element',30)->primary();
            $table->string('cost_element_name',100)->nullable();
            $table->string('cost_element_group',60)->nullable();
            $table->string('biaya_group',30)->nullable();
            $table->string('created_by')->nullable();
            $table->date('create_date')->nullable()->default(NULL);
            $table->string('updated_by')->nullable();
            $table->date('update_date')->nullable()->default(NULL);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('m_cost_element');
    }
}
