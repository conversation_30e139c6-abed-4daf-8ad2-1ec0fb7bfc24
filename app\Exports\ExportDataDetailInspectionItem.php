<?php

namespace App\Exports;

use App\Models\Area;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\Models\ItemInspection;

class ExportDataDetailInspectionItem implements FromCollection, WithHeadings, WithTitle, WithStyles
{

    /**
     * @return \Illuminate\Support\Collection
     */


    public function  __construct($id)
    {
        $this->id = $id;
    }

   
    /**
     * @return string
     */
    public function title(): string
    {
        return 'Data Export Inspection Item';
    }

    public function collection()
    {
        if ($this->id == "empty" || $this->id == "") {
            $id_area = '1=1';
        } else {
            $id_area = 'm_area.id_area in ( ' . $this->id . ' )';
        }
        return Area::select(
            'm_equipment.kode_equipment',
            'm_equipment.nm_equipment',
            'm_equipment.id_area',
            'm_area.nm_area',
            'm_area.create_by',
            'm_area.update_by',
            'm_area.created_at'
        )->join('m_equipment','m_equipment.id_area','=','m_area.id_area')->whereraw($id_area)->get();
    }

    public function styles(Worksheet $sheet)
    {
        return [
           
        ];
    }
}
