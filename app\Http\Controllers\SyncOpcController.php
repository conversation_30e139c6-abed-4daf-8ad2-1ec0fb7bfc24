<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use App\Models\RoleHasMenu;
use App\Models\SampleOpc;
use App\Models\Opc_TIS;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\Kiln_Stop;
use App\Models\Opco;
use App\Models\SyncOpcConfig;
use App\Models\SyncOpcLog;
use Illuminate\Support\Facades\Auth;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;

class SyncOpcController extends Controller
{
    public function index(Request $request)
    {
        $data = [
            'title' => 'DATA OPC',
            'breadcrumb' => [
                [
                    'title'=>'Sync Data',
                    'url'=>'/sync-opc',
                ],
                [
                    'title'=>'Data OPC',
                    'url'=>'',
                ]
            ],
        ];
        $data['api_types'] = SyncOpcConfig::apiTypes();
        $data['schedules'] = SyncOpcConfig::schedules();
        $data['statuses'] = SyncOpcConfig::statuses();
        $data['configs'] = SyncOpcConfig::get();
        $data['opco'] = Opco::get();
        return view('sync.opc', $data);
    }

    public function getDataSyncOpc(Request $request)
    {
        $model = SyncOpcLog::select(['sync_opc_logs.id', 'sync_opc_config_id', 'config_name', 'year', 'month', 'sync_opc_logs.created_at', 'sync_opc_logs.status', 'note']);;

        if ($request->filter_data_opc_bulan) {
            $model->where('month', $request->filter_data_opc_bulan);
        }
        if ($request->filter_data_opc_tahun) {
            $model->where('year', $request->filter_data_opc_tahun);
        }
        if ($request->filter_data_opc) {
            $model->where('sync_opc_config_id', $request->filter_data_opc);
        }
        return DataTables::of($model)->make();
    }

    public function getFilterRfcOpc(Request $request)
    {
        $filter_opc = SampleOpc::select('id_rfc', 'rfc')->distinct('rfc')->get();
        return [
            'message' => 'Succes Get Data Filter Rfc',
            'data' => $filter_opc
        ];
    }

    public function getDataOPC(Request $request)
    {
        //get data from OPC Non SP 
        // 
        // [Tuban 1,2,3,4 ]
        // Semen Tonasa
        // [CCR Tonasa 2/3 ]
        // [CCR Tonasa 4 ]
        // [CCR Tonasa 5 ]
        // [BTG Tonasa ]
        // Semen Rembang
        // [CCR Rembang]
        // TLCC
        // [Plant Quang Ninh]
        // [Plant Ho Chi Minh]
        $arrTags = [
            'Tuban1.KL1_Motor' => 'GHO1',
            'Tuban2.KL1_Motor' => 'GHO2',
            'KL3_Tuban_Motor' => 'GHO3',
            'KL4_Tuban_Motor' => 'GHO4',
        ];
        $client = new client();
        $getTag = '{
            "tags": [
              {
                "name": "KL4_Tuban_Motor",
                "props": [
                  {
                    "name": "Value"
                  }
                ]
              }
            ],
            "status": "OK",
            "message": "",
            "token": "7e61b230-481d-4551-b24b-ba9046e3d8f2"
          }&_=1469589103720';
        try {
            $debug = fopen("path_and_filename.txt", "a+");
            $response = $client->request('GET', 'http://10.15.3.146:58725/OPCREST/getdata?message=' . $getTag, ['debug' => $debug]);
            $response2 = json_decode(str_replace(");", "", str_replace("(", "", $response->getBody()->getContents())));
            $res = $response2->tags;
            if (count($res) > 0)
                foreach ($res as $k => $v) {
                    $status = ($request->status != null) ? $request->status : 1; ////$v->props[0]->val=="True"?1:0;
                    $last_dt = Opc_TIS::where('kode_plant', $arrTags[$v->name])->where(DB::raw("to_char(tanggal, 'YYYY-MM-DD')"), date("Y-m-d"))
                        // ->where('status', $status)
                        ->where('source_system', 'OPC')
                        ->orderBy('tanggal', 'desc')
                        ->first();

                    if ($last_dt) {
                        //save if Different from last Status in same day
                        $date = date("Y-m-d H:i:s");
                        // dd($last_dt->status."-".$status);
                        if ($last_dt->status != $status) {
                            Opc_TIS::create([
                                'kode_plant' => $arrTags[$v->name], 'tanggal' => $date, 'status' => $status,
                                'source_system' => 'OPC'
                            ]);
                        }

                        //send data to KILN_STOP for EMAIL
                        //JIka OPC Down
                        if ($status == 0) {
                            $last_dt_kiln_stop = Kiln_Stop::where('kode_plant', $arrTags[$v->name])->where(DB::raw("to_char(tanggal_mulai, 'YYYY-MM')"), date("Y-m"))
                                // ->where('status', $status)
                                ->whereNull('tanggal_selesai')
                                ->where('source_system', 'OPC')
                                ->orderBy('tanggal_mulai', 'desc')
                                ->first();
                            if (!$last_dt_kiln_stop) {
                                Kiln_Stop::create([
                                    'kode_plant' => $arrTags[$v->name], 'tanggal_mulai' => $date,
                                    'source_system' => 'OPC', 'create_by' => "OPC-" . $arrTags[$v->name]
                                ]);
                                dd('Kiln OFF');
                            }
                            dd('Kiln OFF Before');
                        }

                        //JIka OPC Hidup Update tanggal selsai yang sebelumnya mati
                        if ($status == 1) {
                            $last_dt_kiln_stop = Kiln_Stop::where('kode_plant', $arrTags[$v->name])->where(DB::raw("to_char(tanggal_mulai, 'YYYY-MM-DD')"), date("Y-m-d"))
                                // ->where('status', $status)
                                ->whereNull('tanggal_selesai')
                                ->where('source_system', 'OPC')
                                ->orderBy('tanggal_mulai', 'desc')
                                ->first();
                            if ($last_dt_kiln_stop) {
                                $update_data['tanggal_selesai'] = $date;
                                $last_dt_kiln_stop->update($update_data);
                                dd('Kiln ON');
                            }
                            dd('Kiln ON Before');
                        }
                    } else {
                        Opc_TIS::create([
                            'kode_plant' => $arrTags[$v->name], 'tanggal' => date("Y-m-d h:i:s"), 'status' => $status,
                            'source_system' => 'OPC'
                        ]);
                        if ($status == 0)
                            dd('Kiln OFF');
                        else
                            dd('Kiln ON');
                    }
                    // $OPC = Opc_TIS::firstOrCreate([
                    //   'kode_plantx' => $arrTags[$v->name],
                    //    DB::raw('to_char(tanggal)') => date("Y-m-d h:i:s"),
                    //   'status' => $status,
                    //   'source_system' => 'OPC',
                    // ]);
                }
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            var_dump('error');
            $guzzleResult = $e->getResponse();
            var_dump($guzzleResult);
        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            var_dump('error');
            $guzzleResult = $e->getHandlerContext();
            var_dump($guzzleResult);
        }
    }
}
