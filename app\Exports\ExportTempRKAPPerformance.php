<?php

namespace App\Exports;

use App\Models\KilnPlant;
use App\Models\RKAPPerformance;
use App\Models\Plant;
use App\Models\Parameter;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\FromCollection;


class ExportTempRKAPPerformance implements WithHeadings, ShouldAutoSize, WithEvents, WithTitle, WithColumnWidths, WithStyles, FromCollection
{
    protected $tahun;

    function __construct($tahun) {
        $this->tahun = $tahun;
    }

    public function collection()
    {
        $plants = KilnPlant::select('kode_plant as plant')->orderBy('no_pbi')->get()->toArray();
        $parameters = Parameter::select('id_parameter')->orderBy('id')->get()->toArray();
        $listParams = [
            'PROD. VOL' => 'prod_vol',
            'OPH' => 'oph',
            'PDT' => 'pdt',
            'STOP IDLE' => 'stop_idle',
            'UPDT' => 'updt',
            'BDP RATE' => 'bdp_rate',
            'PROD. RATE' => 'prod_rate',
            'FREK UPDT' => 'frek_updt',
            'FY STOP' => 'fy_stop'];

        $tahun = $this->tahun;
        for ($i=1; $i <= 12 ; $i++) {
            $query = DB::table('ts_rkap_perfomance')->leftJoin('m_kiln_plant', 'ts_rkap_perfomance.kode_plant', '=', 'm_kiln_plant.kode_plant')
                        ->select(
                            'ts_rkap_perfomance.kode_plant',
                            'ts_rkap_perfomance.prod_vol',
                            'ts_rkap_perfomance.oph',
                            'ts_rkap_perfomance.pdt',
                            'ts_rkap_perfomance.stop_idle',
                            'ts_rkap_perfomance.updt',
                            'ts_rkap_perfomance.bdp_rate',
                            'ts_rkap_perfomance.prod_rate',
                            'ts_rkap_perfomance.frek_updt',
                            'ts_rkap_perfomance.fy_stop')
                        ->where('ts_rkap_perfomance.bulan', '=', $i)
                        ->where('ts_rkap_perfomance.tahun', '=', $tahun)
                        ->orderBy('m_kiln_plant.no_pbi')
                        ->get();

                        foreach ($query as $key => $value) {
                            $bulan[$i][$value->kode_plant] = (object) [
                                'prod_vol' => $value->prod_vol,
                                'oph' => (int)$value->oph/24,
                                'pdt' => $value->pdt,
                                'stop_idle' => $value->stop_idle,
                                'updt' => $value->updt,
                                'bdp_rate' => $value->bdp_rate,
                                'prod_rate' => $value->prod_rate,
                                'frek_updt' => $value->frek_updt,
                                'fy_stop' => $value->fy_stop
                            ];
                        }
                        for ($p=count($query); $p < count($plants); $p++) {
                            $bulan[$i][$plants[$p]['plant']] = (object) [
                                        'prod_vol' => "0",
                                        'oph' => "0",
                                        'pdt' => "0",
                                        'stop_idle' => "0",
                                        'updt' => "0",
                                        'bdp_rate' => "0",
                                        'prod_rate' => "0",
                                        'frek_updt' => "0",
                                        'fy_stop' => "0"
                                    ];
        }
        }
        $export = [];
        foreach ($listParams as $params => $param) {
            foreach ($plants as $key => $plant) {
                $temp = [
                    'kode_plant'    => $plant['plant'],
                    'parameter'     => $params,
                    'tahun'         => $tahun,
                    'januari'       => $bulan[1][$plant['plant']]->$param,
                    'february'      => $bulan[2][$plant['plant']]->$param,
                    'march'         => $bulan[3][$plant['plant']]->$param,
                    'april'         => $bulan[4][$plant['plant']]->$param,
                    'mei'           => $bulan[5][$plant['plant']]->$param,
                    'june'          => $bulan[6][$plant['plant']]->$param,
                    'july'          => $bulan[7][$plant['plant']]->$param,
                    'august'        => $bulan[8][$plant['plant']]->$param,
                    'september'     => $bulan[9][$plant['plant']]->$param,
                    'october'       => $bulan[10][$plant['plant']]->$param,
                    'november'      => $bulan[11][$plant['plant']]->$param,
                    'december'      => $bulan[12][$plant['plant']]->$param,
                ];
                array_push($export, $temp);
            }
        }
        return collect($export);
    }

     //set header value
     public function headings():array
     {
         return [
             'PLANT',
             'Parameter',
             'Year',
             'January',
             'February',
             'March',
             'April',
             'Mei',
             'June',
             'July',
             'August',
             'September',
             'October',
             'November',
             'December',
         ];
     }
 
     public function registerEvents(): array
     {
         return [
             AfterSheet::class    => function(AfterSheet $event) {
                 //set background color for header (A1:D1)
                 $event->sheet->getDelegate()
                 ->getStyle('A1:O1')
                 ->getFill()
                 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                 ->getStartColor()
                 ->setARGB('C4D79B');
 
                 //set header (A1:D1) uneditable and set workspace (A2:D1000) editable
                 // $event->sheet->protectCells('A1:D1', 'PASSWORD');
                 // $event->sheet->getStyle('A2:D1000')->getProtection()->setLocked(\PhpOffice\PhpSpreadsheet\Style\Protection::PROTECTION_UNPROTECTED);
                 // $event->sheet->getDelegate()->getProtection()->setSheet(true);
 
             },
         ];
     }
 
     //set name for worksheet
     public function title(): string
     {
         return 'RKAP Performance';
     }
 
     //set width for header
     public function columnWidths(): array
     {
         return [
             'A' => 15,
             'B' => 15,    
             'C' => 15,
             'D' => 15,
             'E' => 15,
             'F' => 15,    
             'G' => 15,
             'H' => 15, 
             'I' => 15,
             'J' => 15,    
             'K' => 15,
             'L' => 15, 
             'M' => 15,
             'N' => 15,    
             'O' => 15,
         ];
     }
 
     //set bold for header
     public function styles(Worksheet $sheet)
     {
         return [
             'A'    => ['font' => ['bold' => true]],
             'B'    => ['font' => ['bold' => true]],
             'C'    => ['font' => ['bold' => true]],
             'D'    => ['font' => ['bold' => true]],
             'E'    => ['font' => ['bold' => true]],
             'F'    => ['font' => ['bold' => true]],    
             'G'    => ['font' => ['bold' => true]],
             'H'    => ['font' => ['bold' => true]], 
             'I'    => ['font' => ['bold' => true]],
             'J'    => ['font' => ['bold' => true]],    
             'K'    => ['font' => ['bold' => true]],
             'L'    => ['font' => ['bold' => true]], 
             'M'    => ['font' => ['bold' => true]],
             'N'    => ['font' => ['bold' => true]],    
             'O'    => ['font' => ['bold' => true]],
         ];
     }
 
}
