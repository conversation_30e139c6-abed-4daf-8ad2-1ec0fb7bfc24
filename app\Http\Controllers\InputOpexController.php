<?php

namespace App\Http\Controllers;

use App\Models\InputOpex;
use DataTables;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\Routes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Services\SAPRFCClass;

use Illuminate\Support\Facades\File;
use App\Imports\ImportOpex;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportOpex;

class InputOpexController extends Controller
{
    public function index()
    {
        // $query = new Company();
        // $response = (new SAPRFCClass)->getData_Anggaran();
        // print_r($response);
        // exit;
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        return view('InputOpex', $data);
    }

    public function datatables(Request $request)
    {
        // $response = (new SAPRFCClass)->getData_Anggaran();
        // $result = $response['T_RETURN'];
        // print_r($response);exit;
        $query    = InputOpex::get();
        $data     = DataTables::of($query)
                        ->addColumn('status', function($row){
                            if($row->status == '1')
                                $status = '<b>Uploaded</b>';
                            else
                                $status = '<b>Not uploaded yet</b>';
                            return $status;
                        })
                        ->addColumn('generate', function($row){
                            if($row->status == '1')
                                $generate = $row->generate_id;
                            else
                                $generate = '';
                            return $generate;
                        })
                        ->addColumn('dokumen_no', function($row){
                            if($row->status == '1')
                                $generate = $row->dokumen_no;
                            else
                                $generate = '';
                            return $generate;
                        })
                        ->rawColumns(['status','generate','dokumen_no'])
                        ->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $InputOpex = InputOpex::create([
            'fm_area' => $request->fm_area,
            'fiscal_year' => $request->fiscal_year,
            'funds_center' => $request->funds_center,
            'commitment_itm' => $request->commitment_itm,
            'status' => '0',
            'amount' => $request->amount,
        ]);
        $response = responseSuccess(trans('message.read-success'),$InputOpex);
        return response()->json($response,200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($InputOpex)
    {

        $query   = InputOpex::find($InputOpex);
        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $query   = InputOpex::find($id);
        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update($id, Request $request)
    {
          $data = $this->findDataWhere(InputOpex::class, ['id' => $id]);

        //   dd($data);exit();
          DB::beginTransaction();
          try {
              $data->update([
                'fm_area' => $request->fm_area,
                'fiscal_year' => $request->fiscal_year,
                'funds_center' => $request->funds_center,
                'commitment_itm' => $request->commitment_itm,
                'amount' => $request->amount,
                'status' => '0',
                'generate_id' => '',
                'fb_1' => '',
                'fb_2' => '',
                'dokumen_no' => '',
              ]);
              DB::commit();
              $response = responseSuccess(trans("messages.update-success"), $data);
              return response()->json($response, 200, [], JSON_PRETTY_PRINT);
          } catch (Exception $e) {
              DB::rollback();
              $response = responseFail(trans("messages.update-fail"), $e->getMessage());
              return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }

    }


    public function destroy($id)
    {

        InputOpex::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }

    public function import(Request $request) 
	{
		// validasi
		$this->validate($request, [
			'file' => 'required|mimes:csv,xls,xlsx'
		]);

		// menangkap file excel
		$file = $request->file('file');

		// membuat nama file unik
		$nama_file = rand().$file->getClientOriginalName();

		// upload ke folder file_opex di dalam folder public
		$file->move('file_opex',$nama_file);

		// import data
		Excel::import(new ImportOpex, public_path('/file_opex/'.$nama_file));

		// alihkan halaman kembali
		$data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        return view('InputOpex', $data);
	}

    public function temp()
	{
		return Excel::download(new ExportOpex, 'Template Opex.xlsx');
	}

    public function generate()
    {
        $query = DB::table('opex')
                ->where('status', '!=' , 1)
                ->select('fm_area','fiscal_year','funds_center','commitment_itm',DB::raw("SUM(amount) as amount"))
                ->groupBy('fm_area','fiscal_year','funds_center','commitment_itm')
                ->get();
        // dd($query);exit;
        if($query){
            $i = 0;
            $j = 1;
            
            foreach ($query  as $key => $value) {
                
                DB::beginTransaction();
                try {
                    
                    $id = (DB::table('opex')->max('id')) + 1;
                    $generate_id = $value->funds_center.$value->fiscal_year.$id;
                    // print_r($generate_id);exit;
                    $amount_result = strval($value->amount);
                    $to_sap = (new SAPRFCClass)->store_Anggaran($query,$generate_id);

                    foreach ($to_sap as $keys => $value3) {
                        if(count($value3['T_RETURN']) == 1){                            
                            $data = $value3['T_RETURN'][0];                            
                            $dok_no = $data['BELNR'];
                            foreach ($value3['T_LINE'] as $key => $rv) {
                                InputOpex::where('fm_area',explode('#',$keys)[0])
                                    ->where('fiscal_year',explode('#',$keys)[1])
                                    ->where('funds_center',$rv['FICTR'])
                                    ->update([
                                        'status' => '1',
                                        'generate_id' => $generate_id,
                                        'fb_1' => $data['STATUS'],
                                        'fb_2' =>'Document '.$dok_no.' posted',
                                        'dokumen_no' => $dok_no,
                                    ]);
                                    DB::commit();
                            }
                        }
                    }                  
                    
                } catch (Exception $e) {
                    DB::rollback();
                    $response = responseFail(trans("messages.update-fail"), $e->getMessage());
                    return response()->json($response, 500, [], JSON_PRETTY_PRINT);
                }
                $i++;
            }
        }
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
        // $response = (new SAPRFCClass)->store_Anggaran($request->fm_area,$request->fiscal_year,$request->funds_center,$request->commitment_itm,$request->amount);
        // print_r($response);exit;
        // exit;
    }

}
