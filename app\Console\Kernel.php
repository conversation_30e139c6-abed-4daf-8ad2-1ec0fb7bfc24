<?php

namespace App\Console;

use App\Console\Commands\IntercoCommand;
use App\Models\SyncOpcConfig;
use App\Models\SyncSapConfig;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        IntercoCommand::class
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('interco:insertdb')->everyFiveMinutes();

        $configs = SyncOpcConfig::where(['status' => 'active'])->get();
        foreach ($configs as $config) {
            $command = "synchronize:opc -v --type=$config->type --id=$config->id";
            if ($config->schedule == 'minutely') {
                $schedule->command($command)->everyMinute()->withoutOverlapping();
            } elseif ($config->schedule == 'everyfiveminute') {
                $schedule->command($command)->everyFiveMinutes()->withoutOverlapping();
            } elseif ($config->schedule == 'hourly') {
                $schedule->command($command)->hourly()->withoutOverlapping();
            } elseif ($config->schedule == 'daily') {
                $schedule->command($command)->dailyAt($config->at_time)->withoutOverlapping();
            } elseif ($config->schedule == 'monthly') {
                $schedule->command($command)->monthlyOn($config->at_date, $config->at_time)->withoutOverlapping();
            }
        }

        $configs = SyncSapConfig::where(['status' => 'active'])->get();
        foreach ($configs as $config) {
            $command = "synchronize:sap -v --type=$config->type --id=$config->id";
            if ($config->schedule == 'minutely') {
                $schedule->command($command)->everyMinute()->withoutOverlapping();
            } elseif ($config->schedule == 'everyfiveminute') {
                $schedule->command($command)->everyFiveMinutes()->withoutOverlapping();
            } elseif ($config->schedule == 'hourly') {
                $schedule->command($command)->hourly()->withoutOverlapping();
            } elseif ($config->schedule == 'daily') {
                $schedule->command($command)->dailyAt($config->at_time)->withoutOverlapping();
            } elseif ($config->schedule == 'monthly') {
                $schedule->command($command)->monthlyOn($config->at_date, $config->at_time)->withoutOverlapping();
            }
        }

        $schedule->command('realisasi:performance')->everyMinute()->withoutOverlapping();
        $schedule->command('mail:cause-stop')->everyMinute()->withoutOverlapping();
       	$schedule->command('synchronize:tis')->everyMinute()->withoutOverlapping();
        $schedule->command('resume')->everyMinute()->withoutOverlapping();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
