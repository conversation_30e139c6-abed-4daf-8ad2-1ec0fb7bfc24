<?php

namespace App\Http\Controllers;

use App\Exports\ExportDataKoreksiFRMTC;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Menu;
use App\Models\RKAPCost;
use App\Models\TKFR;
use App\Models\Opco;
use Auth;
// use DataTables;

use Yajra\DataTables\DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportTempKoreksiFR;
use App\Exports\ExportKoreksiFR;
use App\Imports\ImportKoreksiFR;
use App\Models\ExchangeRate;
use Carbon\Carbon;

class KoreksiFRController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Koreksi FR',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/koreksi-fr',
                ],
                [
                    'title'=>'Koreksi FR',
                    'url'=>'',
                ]
            ],
        ];
        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow; $i++) {
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);
        $optTahun ="";
        foreach ($tahun as $value) {
            $tahun = $value['tahun'];
            $selected = $value['selected'];
            if ($selected == true) {
                $optTahun .= "<option value=$tahun selected>$tahun</option>";
            }
            else{
                $optTahun .= "<option value=$tahun>$tahun</option>";
            }
        }
        $data['tahun'] = $optTahun;

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        // $bulan = DB::table('m_bulan')->select('kode_bulan', 'bln_english')->orderBy('kode_bulan')->get();
        $optBulan = "";
        for ($i=0; $i < count($months); $i++) {
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $optBulan .= "<option value=$noBulan selected>$months[$i]</option>";
            }
            else{
                $optBulan .= "<option value=$noBulan>$months[$i]</option>";
            }
        }
        $data['bulan'] = $optBulan;
        return view('koreksiFR', $data);
    }

    public function viewImport()
    {
        $data = [
            'title' => 'Import Koreksi FR',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/koreksi-fr',
                ],
                [
                    'title'=>'Koreksi Data FR',
                    'url'=>'/koreksi-fr/view-import',
                ],
                [
                    'title'=>'Import Koreksi FR',
                    'url'=>'',
                ]
            ],
        ];
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();
        $opt_opco = "";
        foreach ($opco as $value) {
            $kode_opco = $value['kode_opco'];
            $nama_opco = $value['nama_opco'];
            $opt_opco .= "<option value='$kode_opco'>$nama_opco</option>";
        }
        $data['opco'] = $opt_opco;
        return view('koreksiFRImport', $data);
    }

    public function filter()
    {
        //filter opco
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();

        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow; $i++) {
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $bulan = [];
        for ($i=0; $i < count($months); $i++) {
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => true
                ];
            }
            else{
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => false
                ];
            }
        }

        $data = [
            'kode_opco' => $opco,
            'tahun'     => $tahun,
            'bulan'     => $bulan
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function datatables(Request $request)
    {

        try {

            $request->validate([
                'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
                'filter_bulan' => 'nullable|numeric',
                'filter_tahun' => 'nullable|numeric',
            ]);

            $data_koreksi =  DB::table('vw_tk_fr')->select(
                'kd_opco',
                'tanggal',
                'THN as thn',
                DB::raw("to_char(rkap_bahan_bakar, '999G999G999G999G990D9') as rkap_bahan_bakar"),
                DB::raw("to_char(rkap_bahan_baku, '999G999G999G999G990D9') as rkap_bahan_baku"),
                DB::raw("to_char(rkap_listrik, '999G999G999G999G990D9') as rkap_listrik"),
                DB::raw("to_char(rkap_tenaga_kerja, '999G999G999G999G990D9') as rkap_tenaga_kerja"),
                DB::raw("to_char(rkap_pemeliharaan, '999G999G999G999G990D9') as rkap_pemeliharaan"),
                DB::raw("to_char(rkap_penyusutan, '999G999G999G999G990D9') as rkap_penyusutan"),
                DB::raw("to_char(rkap_administrasi_umum, '999G999G999G999G990D9') as rkap_administrasi_umum"),
                DB::raw("to_char(rkap_pajak_asuransi, '999G999G999G999G990D9') as rkap_pajak_asuransi"),
                DB::raw("to_char(rkap_elim_bb, '999G999G999G999G990D9') as rkap_elim_bb"),
                DB::raw("to_char(rkap_elim_penyusutan, '999G999G999G999G990D9') as rkap_elim_penyusutan"),
                DB::raw("to_char(rkap_elim_administrasi, '999G999G999G999G990D9') as rkap_elim_administrasi"),
                DB::raw("to_char(rkap_prod_klinker, '999G999G999G999G990D9') as rkap_prod_klinker"),
                DB::raw("to_char(rkap_prod_semen, '999G999G999G999G990D9') as rkap_prod_semen"),
                DB::raw("to_char(rkap_clinker_sold, '999G999G999G999G990D9') as rkap_clinker_sold"),
                DB::raw("to_char(rkap_prod_output, '999G999G999G999G990D9') as rkap_prod_output"),
                DB::raw("to_char(bahan_bakar, '999G999G999G999G990D9') as bahan_bakar"),
                DB::raw("to_char(bahan_baku, '999G999G999G999G990D9') as bahan_baku"),
                DB::raw("to_char(listrik, '999G999G999G999G990D9') as listrik"),
                DB::raw("to_char(tenaga_kerja, '999G999G999G999G990D9') as tenaga_kerja"),
                DB::raw("to_char(pemeliharaan, '999G999G999G999G990D9') as pemeliharaan"),
                DB::raw("to_char(penyusutan, '999G999G999G999G990D9') as penyusutan"),
                DB::raw("to_char(administrasi_umum, '999G999G999G999G990D9') as administrasi_umum"),
                DB::raw("to_char(pajak_asuransi, '999G999G999G999G990D9') as pajak_asuransi"),
                DB::raw("to_char(elim_bb, '999G999G999G999G990D9') as elim_bb"),
                DB::raw("to_char(elim_penyusutan, '999G999G999G999G990D9') as elim_penyusutan"),
                DB::raw("to_char(elim_administrasi, '999G999G999G999G990D9') as elim_administrasi"),
                DB::raw("to_char(prod_klinker, '999G999G999G999G990D9') as prod_klinker"),
                DB::raw("to_char(prod_semen, '999G999G999G999G990D9') as prod_semen"),
                DB::raw("to_char(clinker_sold, '999G999G999G999G990D9') as clinker_sold"),
                DB::raw("to_char(prod_output, '999G999G999G999G990D9') as prod_output"),
                DB::raw("to_char(ics, '999G999G999G999G990D9') as ics"),
            );
            if($request->filter_opco){
                $data_koreksi = $data_koreksi->where('kd_opco', $request->filter_opco);
            }
            if($request->filter_tahun){
                $data_koreksi = $data_koreksi->where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $request->filter_tahun);
            }
            if($request->filter_bulan){
                $data_koreksi = $data_koreksi->where(DB::raw("TO_CHAR(tanggal::timestamp, 'FMMM')"), $request->filter_bulan);
            }
            $data_koreksi = $data_koreksi->get();

            return DataTables::of($data_koreksi)->make();
            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data' => $data_koreksi,
            ];
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }


    public function create()
    {
        //
    }

    public function store(Request $request)
    {
        $cekRkap = RKAPCost::where('kode_opco', $request->kode_opco)
        ->where('tahun', $request->tahun)
        ->where('bulan', $request->bulan)->get()->toArray();
        if($cekRkap){
            $response = [
                'success' => false,
                'data' => $cekRkap
            ];
            return response()->json($response,200);
        }
        $rkap = RKAPCost::create([
            'kode_opco' => $request->kode_opco,
            'tahun' => $request->tahun,
            'bulan' => $request->bulan,
            'rkap_bahan_bakar' => $request->rkap_bahan_bakar == null ? 0 : str_replace('.','',$request->rkap_bahan_bakar),
            'rkap_bahan_baku' => $request->rkap_bahan_baku == null ? 0 : str_replace('.','',$request->rkap_bahan_baku),
            'rkap_listrik' => $request->rkap_listrik == null ? 0 : str_replace('.','',$request->rkap_listrik),
            'rkap_tenaga_kerja' => $request->rkap_tenaga_kerja == null ? 0 : str_replace('.','',$request->rkap_tenaga_kerja),
            'rkap_pemeliharaan' => $request->rkap_pemeliharaan == null ? 0 : str_replace('.','',$request->rkap_pemeliharaan),
            'rkap_penyusutan' => $request->rkap_penyusutan == null ? 0 : str_replace('.','',$request->rkap_penyusutan),
            'rkap_administrasi_umum' => $request->rkap_administrasi_umum == null ? 0 : str_replace('.','',$request->rkap_administrasi_umum),
            'rkap_pajak_asuransi' => $request->rkap_pajak_asuransi == null ? 0 : str_replace('.','',$request->rkap_pajak_asuransi),
            'rkap_elim_bb' => $request->rkap_elim_bb == null ? 0 : str_replace('.','',$request->rkap_elim_bb),
            'rkap_elim_penyusutan' => $request->rkap_elim_penyusutan == null ? 0 : str_replace('.','',$request->rkap_elim_penyusutan),
            'rkap_elim_administrasi' => $request->rkap_elim_administrasi == null ? 0 : str_replace('.','',$request->rkap_elim_administrasi),
            'create_by' => Auth::user()->username
        ]);
        $response = responseSuccess('Data added successfully',$rkap);
        return response()->json($response,200);
    }

    public function show($rkap)
    {
        $query   = RKAPCost::find($rkap);
        $response = responseSuccess('Data successfully displayed',$query);
        return response()->json($response,200);
    }

    public function update(Request $request, $id)
    {
        $data = $this->findDataWhere(RKAPCost::class, ['id_rkap_biaya' => $id]);

        DB::beginTransaction();
        try {
            $data->update([
                'kode_opco' => $request->kode_opco,
                'tahun' => $request->tahun,
                'bulan' => $request->bulan,
                'rkap_bahan_bakar' => $request->rkap_bahan_bakar == null ? 0 : str_replace('.','',$request->rkap_bahan_bakar),
                'rkap_bahan_baku' => $request->rkap_bahan_baku == null ? 0 : str_replace('.','',$request->rkap_bahan_baku),
                'rkap_listrik' => $request->rkap_listrik == null ? 0 : str_replace('.','',$request->rkap_listrik),
                'rkap_tenaga_kerja' => $request->rkap_tenaga_kerja == null ? 0 : str_replace('.','',$request->rkap_tenaga_kerja),
                'rkap_pemeliharaan' => $request->rkap_pemeliharaan == null ? 0 : str_replace('.','',$request->rkap_pemeliharaan),
                'rkap_penyusutan' => $request->rkap_penyusutan == null ? 0 : str_replace('.','',$request->rkap_penyusutan),
                'rkap_administrasi_umum' => $request->rkap_administrasi_umum == null ? 0 : str_replace('.','',$request->rkap_administrasi_umum),
                'rkap_pajak_asuransi' => $request->rkap_pajak_asuransi == null ? 0 : str_replace('.','',$request->rkap_pajak_asuransi),
                'rkap_elim_bb' => $request->rkap_elim_bb == null ? 0 : str_replace('.','',$request->rkap_elim_bb),
                'rkap_elim_penyusutan' => $request->rkap_elim_penyusutan == null ? 0 : str_replace('.','',$request->rkap_elim_penyusutan),
                'rkap_elim_administrasi' => $request->rkap_elim_administrasi == null ? 0 : str_replace('.','',$request->rkap_elim_administrasi),
                'update_by' => Auth::user()->username
            ]);
        DB::commit();
        $response = responseSuccess('Data updated successfully ', $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function destroy($id)
    {
        RKAPCost::destroy($id);
        $response = responseSuccess("Data deleted successfully");
        return response()->json($response,200);
    }

    public function temp(Request $request)
    {
        $request->validate([
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
        ]);

            $data_koreksi =  DB::table('vw_tk_fr')->select(
                                'kd_opco',
                                DB::raw("TO_CHAR(tanggal::timestamp, 'DD/MM/YYYY') AS tanggal"),
                                'THN',
                                'rkap_bahan_bakar',
                                'rkap_bahan_baku',
                                'rkap_listrik',
                                'rkap_tenaga_kerja',
                                'rkap_pemeliharaan',
                                'rkap_penyusutan',
                                'rkap_administrasi_umum',
                                'rkap_pajak_asuransi',
                                'rkap_elim_bb',
                                'rkap_elim_penyusutan',
                                'rkap_elim_administrasi',
                                'rkap_prod_klinker',
                                'rkap_prod_semen',
                                'rkap_clinker_sold',
                                'rkap_prod_output',
                                'bahan_bakar',
                                'bahan_baku',
                                'listrik',
                                'tenaga_kerja',
                                'pemeliharaan',
                                'penyusutan',
                                'administrasi_umum',
                                'pajak_asuransi',
                                'elim_bb',
                                'elim_penyusutan',
                                'elim_administrasi',
                                'prod_klinker',
                                'prod_semen',
                                'clinker_sold',
                                'prod_output',
                                'ics');
                if($request->filter_tahun){
                    $data_koreksi = $data_koreksi->where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $request->filter_tahun);
                }
                if($request->filter_bulan){
                    $data_koreksi = $data_koreksi->where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $request->filter_bulan);
                }
                $data_koreksi = $data_koreksi->get();

		return Excel::download(new ExportTempKoreksiFR($data_koreksi), 'Template Koreksi FR.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
    }

    public function export(Request $request)
    {
        $request->validate([
            'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
        ]);

        $opco = $request->filter_opco;
        $tahun = $request->filter_tahun;
        $bulan = $request->filter_bulan;

		return Excel::download(new ExportKoreksiFR($opco, $tahun, $bulan), 'Data Koreksi FR.xlsx');
    }

    public function import(Request $request)
    {
		// validasi
		$this->validate($request, [
			'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author
		]);

        // menangkap file excel
        $file = $request->file('excel');

        // import data
        $import = new ImportKoreksiFR;
        Excel::import($import, $file);
        //list opco
        $opco = Opco::select('kode_opco')->get()->toArray();
        $arrOpco = [];
        foreach ($opco as $value) {
            array_push($arrOpco,$value['kode_opco']);
        }
        $datas = ($import->data)->toArray();
        $result = [];
        foreach ($datas as $data) {
            $format = $data;
            $message = "";
            $status = "Valid";
            if($data['opco']==NULL){
                $status = 'Invalid';
                $message = $message . "Kolom opco tidak boleh kosong ";
            }
            else if(gettype($data['opco']) != 'string'){
                $status = 'Invalid';
                $message = $message . "Kolom opco hanya berisi huruf ";
            }
            else if(!in_array($data['opco'],$arrOpco)){
                $status = "Invalid";
                $message = $message . "Kolom opco tidak ditemukan";
            }
            if($data['date']==NULL){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom tahun tidak boleh kosong ";
                }
                else{
                    $message = $message . ", Kolom tahun tidak boleh kosong";
                }
            }
            $listRKAP = ['rkap_bahan_bakar','rkap_bahan_baku_penolong','rkap_listrik','rkap_tenaga_kerja','rkap_pemeliharaan','rkap_deplesi_penyusutan_amortisasi','rkap_urusan_umum_adm_kantor','rkap_pajak_asuransi','rkap_elim_bahan_baku_penolong','rkap_elim_deplesi_penyusutan_amortisasi','rkap_elim_urusan_umum_adm_kantor','rkap_prod_clinker_ton','rkap_prod_cement_ton','rkap_clinker_sold_ton','rkap_prod_output_ton','bahan_bakar','bahan_baku_penolong','listrik','tenaga_kerja','pemeliharaan','deplesi_penyusutan_amortisasi','urusan_umum_adm_kantor','pajak_dan_asuransi','elim_bahan_baku_penolong','elim_deplesi_penyusutan_amortisasi','elim_urusan_umum_adm_kantor','real_prod_clinker_ton','real_prod_cement_ton','real_clinker_sold_ton','real_prod_output_ton','ics_ton'];
            $messageRKAP = ['RKAP Bahan Bakar', 'RKAP Bahan Baku Penolong', 'RKAP Listrik', 'RKAP Tenaga Kerja', 'RKAP Pemeliharaan','RKAP Deplesi, Penyusutan & Amortisasi',
            'RKAP Urusan Umum & Adm. Kantor', 'RKAP Pajak & Asuransi', 'RKAP Elim. Bahan Baku & Penolong', 'RKAP Elim. Deplesi, Penyusutan & Amortisasi', 'RKAP Elim. Urusan Umum & Adm. Kantor', 'Rkap Prod. Clinker (ton)',  'Rkap Prod. Cement (ton)', 'Rkap Clinker sold (ton)', 'Rkap prod. Output (ton)', 'Bahan Bakar', 'Bahan Baku & Penolong', 'Listrik', 'Tenaga Kerja', 'Pemeliharaan', 'Deplesi, Penyusutan & Amortisasi', 'Urusan Umum & Adm. Kantor', 'Pajak dan Asuransi', 'Elim. Bahan Baku & Penolong', 'Elim. Deplesi, Penyusutan & Amortisasi', 'Elim. Urusan Umum & Adm. Kantor', 'Real. Prod. Clinker (ton)', 'Real. Prod. Cement (ton)', 'Real. Clinker sold (ton)', 'Real prod. Output (ton)', 'ICS (ton)'];
            foreach ($listRKAP as $key => $value) {
                if ($data[$value] == null) {
                    $format[$value]=0;
                }
                else if(gettype($data[$value]) != 'integer' and gettype($data[$value]) != 'float'  and gettype($data[$value]) != 'double'){
                    $status = "Invalid";
                    $success = false;
                    if($message == ""){
                        $message = $message . "Kolom nilai ".$messageRKAP[$key]." hanya berisi angka";
                    }else{
                        $message = $message . ", Kolom nilai rkap ".$messageRKAP[$key]." hanya berisi angka";
                    }
                }
            }
            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function insertData(Request $request){
        $excel = json_decode($request->excel);
        $result = [];
        $listCol = ['opco', 'date', 'thn','rkap_bahan_bakar','rkap_bahan_baku_penolong','rkap_listrik','rkap_tenaga_kerja','rkap_pemeliharaan','rkap_deplesi_penyusutan_amortisasi','rkap_urusan_umum_adm_kantor','rkap_pajak_asuransi','rkap_elim_bahan_baku_penolong','rkap_elim_deplesi_penyusutan_amortisasi','rkap_elim_urusan_umum_adm_kantor','rkap_prod_clinker_ton','rkap_prod_cement_ton','rkap_clinker_sold_ton','rkap_prod_output_ton','bahan_bakar','bahan_baku_penolong','listrik','tenaga_kerja','pemeliharaan','deplesi_penyusutan_amortisasi','urusan_umum_adm_kantor','pajak_dan_asuransi','elim_bahan_baku_penolong','elim_deplesi_penyusutan_amortisasi','elim_urusan_umum_adm_kantor','real_prod_clinker_ton','real_prod_cement_ton','real_clinker_sold_ton','real_prod_output_ton','ics_ton'];
        //list opco
        $opco = Opco::select('kode_opco')->get()->toArray();
        $arrOpco = [];
        foreach ($opco as $value) {
            array_push($arrOpco,$value['kode_opco']);
        }
        foreach ($excel as $data) {
            $oldData = $data;
            foreach ($listCol as $index => $col) {
                $data[$col] = $data[$index];
            }//echo '<pre>';print_r($data);exit;
            $data = array_diff_key($data, $oldData);
            $format = $data;
            $message = "";
            $status = "Valid";
            if($data['opco']==NULL){
                $status = 'Invalid';
                $message = $message . "Kolom opco tidak boleh kosong ";
            }
            else if(gettype($data['opco']) != 'string'){
                $status = 'Invalid';
                $message = $message . "Kolom opco hanya berisi huruf ";
            }
            else if(!in_array($data['opco'],$arrOpco)){
                $status = "Invalid";
                $message = $message . "Kolom opco tidak ditemukan";
            }
            if($data['date']==NULL){
                $status = 'Invalid';
                if($message == ""){
                    $message = $message . "Kolom tanggal tidak boleh kosong ";
                }
                else{
                    $message = $message . ", Kolom tanggal tidak boleh kosong";
                }
            }
            $listRKAP = ['rkap_bahan_bakar','rkap_bahan_baku_penolong','rkap_listrik','rkap_tenaga_kerja','rkap_pemeliharaan','rkap_deplesi_penyusutan_amortisasi','rkap_urusan_umum_adm_kantor','rkap_pajak_asuransi','rkap_elim_bahan_baku_penolong','rkap_elim_deplesi_penyusutan_amortisasi','rkap_elim_urusan_umum_adm_kantor','rkap_prod_clinker_ton','rkap_prod_cement_ton','rkap_clinker_sold_ton','rkap_prod_output_ton','bahan_bakar','bahan_baku_penolong','listrik','tenaga_kerja','pemeliharaan','deplesi_penyusutan_amortisasi','urusan_umum_adm_kantor','pajak_dan_asuransi','elim_bahan_baku_penolong','elim_deplesi_penyusutan_amortisasi','elim_urusan_umum_adm_kantor','real_prod_clinker_ton','real_prod_cement_ton','real_clinker_sold_ton','real_prod_output_ton','ics_ton'];
            $messageRKAP = ['RKAP Bahan Bakar', 'RKAP Bahan Baku Penolong', 'RKAP Listrik', 'RKAP Tenaga Kerja', 'RKAP Pemeliharaan','RKAP Deplesi, Penyusutan & Amortisasi',
            'RKAP Urusan Umum & Adm. Kantor', 'RKAP Pajak & Asuransi', 'RKAP Elim. Bahan Baku & Penolong', 'RKAP Elim. Deplesi, Penyusutan & Amortisasi', 'RKAP Elim. Urusan Umum & Adm. Kantor', 'Rkap Prod. Clinker (ton)',  'Rkap Prod. Cement (ton)', 'Rkap Clinker sold (ton)', 'Rkap prod. Output (ton)', 'Bahan Bakar', 'Bahan Baku & Penolong', 'Listrik', 'Tenaga Kerja', 'Pemeliharaan', 'Deplesi, Penyusutan & Amortisasi', 'Urusan Umum & Adm. Kantor', 'Pajak dan Asuransi', 'Elim. Bahan Baku & Penolong', 'Elim. Deplesi, Penyusutan & Amortisasi', 'Elim. Urusan Umum & Adm. Kantor', 'Real. Prod. Clinker (ton)', 'Real. Prod. Cement (ton)', 'Real. Clinker sold (ton)', 'Real prod. Output (ton)', 'ICS (ton)'];
            foreach ($listRKAP as $key => $value) {
                if ($data[$value] == null) {
                    $format[$value]=0;
                }
                else if(gettype($data[$value]) != 'integer' and gettype($data[$value]) != 'float'  and gettype($data[$value]) != 'double'){
                    $status = "Invalid";
                    $success = false;
                    if($message == ""){
                        $message = $message . "Kolom nilai ".$messageRKAP[$key]." hanya berisi angka";
                    }else{
                        $message = $message . ", Kolom nilai ".$messageRKAP[$key]." hanya berisi angka";
                    }
                }
            }
            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function saveData(Request $request)
    {
        //list opco
        $opco = Opco::get()->toArray();
        $arrHolding = [];
        $arrNoPBI = [];
        foreach ($opco as $value) {
            $arrHolding[$value['kode_opco']] = $value['holding'];
            $arrNoPBI[$value['kode_opco']] = $value['no_pbi'];
        }
        $excel = json_decode($request->excel);
        DB::beginTransaction();
        try {
            $thnBln = [];
            foreach ($excel as $item) {
                $tgl = explode('-', $item[1]);
                $thnBln[] = $tgl[0].'-'.$tgl[1];
            }
            $arrThnBln = array_unique($thnBln);
            $placeholder = implode(', ', array_fill(0, count($arrThnBln), '?'));
            TKFR::whereRaw("to_char(tanggal, 'YYYY-MM') in ($placeholder)", $arrThnBln)->delete();
            foreach ($excel as $item) {
                $holding = $arrHolding[$item[0]];
                $no_pbi = $arrNoPBI[$item[0]];
                $dataInsert[] = [
                    'no' => $no_pbi,
                    'holding' => $holding,
                    'kode_opco' => $item[0],
                    'tanggal' => $item[1],
                    'THN' => $item[2],
                    'rkap_bahan_bakar' => $item[3],
                    'rkap_bahan_baku' => $item[4],
                    'rkap_listrik' => $item[5],
                    'rkap_tenaga_kerja' => $item[6],
                    'rkap_pemeliharaan' => $item[7],
                    'rkap_penyusutan' => $item[8],
                    'rkap_administrasi_umum' => $item[9],
                    'rkap_pajak_asuransi' => $item[10],
                    'rkap_elim_bb' => $item[11],
                    'rkap_elim_penyusutan' => $item[12],
                    'rkap_elim_administrasi' => $item[13],
                    'rkap_prod_klinker' => $item[14],
                    'rkap_prod_semen' => $item[15],
                    'rkap_clinker_sold' => $item[16],
                    'rkap_prod_output' => $item[17],
                    'bahan_bakar' => $item[18],
                    'bahan_baku' => $item[19],
                    'listrik' => $item[20],
                    'tenaga_kerja' => $item[21],
                    'pemeliharaan' => $item[22],
                    'penyusutan' => $item[23],
                    'administrasi_umum' => $item[24],
                    'pajak_asuransi' => $item[25],
                    'elim_bb' => $item[26],
                    'elim_penyusutan' => $item[27],
                    'elim_administrasi' => $item[28],
                    'prod_klinker' => $item[29],
                    'prod_semen' => $item[30],
                    'clinker_sold' => $item[31],
                    'prod_output' => $item[32],
                    'ics' => $item[33],
                    'create_by' => Auth::user()->username,
                    'update_by' => Auth::user()->username,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (isset($dataInsert)) {
                $createdDocs = TKFR::insert($dataInsert);
            }
            DB::commit();
            $response = responseSuccess('Data added successfully');
            return response()->json($response,200);
        } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function exportMTCCost(Request $request)
    {
        $data = $this->getDataExportFR($request);
        $tahun = $request->filter_tahun;
        return Excel::download(new ExportDataKoreksiFRMTC($data, $tahun), 'Export Data Koreksi MTC Cost.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
    }

    public function getDataExportFR($request)
    {
        $request->validate([
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
          ]);
        $tahun = $request->filter_tahun;
        $bulan = $request->filter_bulan;
    
        $now = Carbon::now();
        $nowMonth = strlen($bulan) == 1? '0'.$bulan: $bulan;
        $startMonth = Carbon::now()->startOfYear()->format('m');
        $dataMTC = DB::table('vw_tk_fr')->select(
            'no',
            'kd_opco as kode_opco',
            DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY') as tahun"),
            DB::raw("TO_CHAR(tanggal::timestamp, 'MM') as bulan"),
            DB::raw('SUM(rkap_pemeliharaan) as rkap_pemeliharaan'),
            DB::raw('SUM(pemeliharaan) as pemeliharaan'),
            DB::raw('prod_semen + clinker_sold as produksi'),
            DB::raw('rkap_prod_semen + rkap_clinker_sold as rkap_produksi')
        );
        $arr_tahun = [$request->filter_tahun, ($request->filter_tahun - 1)];
        $dataMTC = $dataMTC->whereIn(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $arr_tahun);
        $dataMTC = $dataMTC->where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $nowMonth);
        $dataMTC = $dataMTC->groupBy('kode_opco')->groupBy('tanggal')->groupBy('no')->groupBy('prod_semen')->groupBy('clinker_sold')->groupBy('rkap_prod_semen')->groupBy('rkap_clinker_sold')
        ->orderBy('no')->get()->toArray();

        $dataMTCYtd = DB::table('vw_tk_fr')->select(
            'kd_opco as kode_opco',
            DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY') as tahun"),
            DB::raw('SUM(rkap_pemeliharaan) as rkap_pemeliharaan'),
            DB::raw('SUM(pemeliharaan) as pemeliharaan'),
            DB::raw('SUM(prod_semen + clinker_sold) as produksi'),
            DB::raw('SUM(rkap_prod_semen + rkap_clinker_sold) as rkap_produksi')
        );
        $arr_tahun = [$request->filter_tahun, ($request->filter_tahun - 1)];
        $dataMTCYtd = $dataMTCYtd->whereIn(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $arr_tahun);
        $dataMTCYtd = $dataMTCYtd->whereBetween(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), [ $startMonth, $nowMonth]);
        $dataMTCYtd = $dataMTCYtd->groupBy('kode_opco')->groupBy(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"))->groupBy('no')->orderBy('no')->get()->toArray();

        $exchange_rate = ExchangeRate::pluck('nilai',DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY') as tahun"))->toArray();

        foreach ($dataMTC as $key => $value) {
            $data[$value->tahun][$value->kode_opco]=  array(
              'kode_opco' => $value->kode_opco,
              'tahun' => $value->tahun,
              'rkap_pemeliharaan' => $value->rkap_pemeliharaan,
              'pemeliharaan' => $value->pemeliharaan,
              'rkap_produksi' => $value->rkap_produksi,
              'produksi' => $value->produksi,
            );
            $opco[$value->kode_opco]=[];
        }
        foreach ($dataMTCYtd as $key => $value) {
            $dataYTD[$value->tahun][$value->kode_opco]=  array(
              'kode_opco' => $value->kode_opco,
              'tahun' => $value->tahun,
              'rkap_pemeliharaan' => $value->rkap_pemeliharaan,
              'pemeliharaan' => $value->pemeliharaan,
              'rkap_produksi' => $value->rkap_produksi,
              'produksi' => $value->produksi,
            );
        }
        $kategori = ['COST','RUPIAH/TON','USD/TON'];
        foreach ($kategori as $key => $kat) {
            $dataAll[$kat]['SIG']=$opco;
        }
        foreach ($data as $thn => $tahun) {
            $sumRealCost = 0;
            $sumRkapCost = 0;
            $sumRealProd = 0;
            $sumRkapProd = 0;
            $exchange = array_key_exists($thn, $exchange_rate)? $exchange_rate[$thn] : 15000;
            foreach ($tahun as $kd_opco => $opco) {
                $real_cost = $opco['pemeliharaan'];
                $rkap_cost = $opco['rkap_pemeliharaan'];
                $real_rupiah_ton = $opco['produksi'] <= 0 ? ($real_cost *1000000): ($real_cost *1000000) / $opco['produksi']; 
                $rkap_rupiah_ton = $opco['rkap_produksi'] <= 0 ? ($rkap_cost *1000000): ($rkap_cost *1000000) / $opco['rkap_produksi'];
                $real_usd_ton = $real_rupiah_ton / $exchange; 
                $rkap_usd_ton = $rkap_rupiah_ton / $exchange;

                $dtNow[$thn][$kd_opco] =[
                    'kode_opco' => $opco['kode_opco'],
                    'real_cost' => round($real_cost,0),
                    'rkap_cost' => round($rkap_cost,0),
                    'real_rupiah_ton'   => round($real_rupiah_ton,2),
                    'rkap_rupiah_ton'   => round($rkap_rupiah_ton,2),
                    'real_usd_ton'  => round($real_usd_ton,2),
                    'rkap_usd_ton'  => round($rkap_usd_ton,2)
                ];  

                $sumRealCost += $opco['pemeliharaan'];
                $sumRkapCost += $opco['rkap_pemeliharaan'];
                $sumRealProd += $opco['produksi'];
                $sumRkapProd += $opco['rkap_produksi'];
            }
            $real_cost = $sumRealCost;
            $rkap_cost = $sumRkapCost;
            $real_rupiah_ton = $sumRealProd <= 0 ? ($real_cost *1000000): ($real_cost *1000000) / $sumRealProd; 
            $rkap_rupiah_ton = $sumRkapProd <= 0 ? ($rkap_cost *1000000): ($rkap_cost *1000000) / $sumRkapProd;
            $real_usd_ton = $real_rupiah_ton / $exchange; 
            $rkap_usd_ton = $rkap_rupiah_ton / $exchange;

            $dtNow[$thn]['SIG'] =[
                'kode_opco' => 'SIG',
                'real_cost' => round($real_cost,0),
                'rkap_cost' => round($rkap_cost,0),
                'real_rupiah_ton'   => round($real_rupiah_ton,2),
                'rkap_rupiah_ton'   => round($rkap_rupiah_ton,2),
                'real_usd_ton'  => round($real_usd_ton,2),
                'rkap_usd_ton'  => round($rkap_usd_ton,2)
            ];  
        }
        foreach ($dataYTD as $thn => $tahun) {
            $sumRealCost = 0;
            $sumRkapCost = 0;
            $sumRealProd = 0;
            $sumRkapProd = 0;
            $exchange = array_key_exists($thn, $exchange_rate)? $exchange_rate[$thn] : 15000;
            foreach ($tahun as $kd_opco => $opco) {
                $real_cost = $opco['pemeliharaan'];
                $rkap_cost = $opco['rkap_pemeliharaan'];
                $real_rupiah_ton = $opco['produksi'] <= 0 ? ($real_cost *1000000): ($real_cost *1000000) / $opco['produksi']; 
                $rkap_rupiah_ton = $opco['rkap_produksi'] <= 0 ? ($rkap_cost *1000000): ($rkap_cost *1000000) / $opco['rkap_produksi'];
                $real_usd_ton = $real_rupiah_ton / $exchange; 
                $rkap_usd_ton = $rkap_rupiah_ton / $exchange;

                $dtYtd[$thn][$kd_opco] =[
                    'kode_opco' => $opco['kode_opco'],
                    'real_cost' => round($real_cost,0),
                    'rkap_cost' => round($rkap_cost,0),
                    'real_rupiah_ton'   => round($real_rupiah_ton,2),
                    'rkap_rupiah_ton'   => round($rkap_rupiah_ton,2),
                    'real_usd_ton'  => round($real_usd_ton,2),
                    'rkap_usd_ton'  => round($rkap_usd_ton,2)
                ];  

                $sumRealCost += $opco['pemeliharaan'];
                $sumRkapCost += $opco['rkap_pemeliharaan'];
                $sumRealProd += $opco['produksi'];
                $sumRkapProd += $opco['rkap_produksi'];
            }
            $real_cost = $sumRealCost;
            $rkap_cost = $sumRkapCost;
            $real_rupiah_ton = $sumRealProd <= 0 ? ($real_cost *1000000): ($real_cost *1000000) / $sumRealProd; 
            $rkap_rupiah_ton = $sumRkapProd <= 0 ? ($rkap_cost *1000000): ($rkap_cost *1000000) / $sumRkapProd;
            $real_usd_ton = $real_rupiah_ton / $exchange; 
            $rkap_usd_ton = $rkap_rupiah_ton / $exchange;

            $dtYtd[$thn]['SIG'] =[
                'kode_opco' => 'SIG',
                'real_cost' => round($real_cost,0),
                'rkap_cost' => round($rkap_cost,0),
                'real_rupiah_ton'   => round($real_rupiah_ton,2),
                'rkap_rupiah_ton'   => round($rkap_rupiah_ton,2),
                'real_usd_ton'  => round($real_usd_ton,2),
                'rkap_usd_ton'  => round($rkap_usd_ton,2)
            ];  
        }

        $tahun = $request->filter_tahun;
        foreach ($dataAll as $kKet => $ket) {
            if($kKet == 'COST'){
                $tagRkap = 'rkap_cost';
                $tagReal = 'real_cost';
            }
            else if($kKet == 'RUPIAH/TON'){
                $tagRkap = 'rkap_rupiah_ton';
                $tagReal = 'real_rupiah_ton';
            }
            else{
                $tagRkap = 'rkap_usd_ton';
                $tagReal = 'real_usd_ton';
            }
            foreach ($ket as $kSig => $sig) {
              foreach ($sig as $kOpco => $opco) {
                $dataAll[$kKet][$kSig][$kOpco] = [
                  'kode_opco'   => $kOpco,
                  'rkap'.$tahun.'_this_month'       => $dtNow[$tahun][$kOpco][$tagRkap],
                  'real'.$tahun.'_this_month'       => $dtNow[$tahun][$kOpco][$tagReal],
                  'real'.($tahun-1).'_this_month'   => $dtNow[$tahun - 1][$kOpco][$tagReal],
                  '2_1'  => $dtNow[$tahun][$kOpco][$tagRkap] > 0 ? round(($dtNow[$tahun][$kOpco][$tagReal] / $dtNow[$tahun][$kOpco][$tagRkap])*100,2)  : $dtNow[$tahun][$kOpco][$tagReal],
                  '2_3'  => $dtNow[$tahun - 1][$kOpco][$tagReal] > 0 ? round(($dtNow[$tahun][$kOpco][$tagReal] / $dtNow[$tahun - 1][$kOpco][$tagReal])*100,2)  : $dtNow[$tahun][$kOpco][$tagReal],
                  'rkap'.$tahun.'_until_month'      =>$dtYtd[$tahun][$kOpco][$tagRkap],
                  'real'.$tahun.'_until_month'      =>$dtYtd[$tahun][$kOpco][$tagReal],
                  'real'.($tahun-1).'_until_month'  =>$dtYtd[$tahun - 1][$kOpco][$tagReal],
                  '5_4'  => $dtYtd[$tahun][$kOpco][$tagRkap] > 0 ? round(($dtYtd[$tahun][$kOpco][$tagReal] / $dtYtd[$tahun][$kOpco][$tagRkap])*100,2)  : $dtYtd[$tahun][$kOpco][$tagReal],
                  '5_6'  => $dtYtd[$tahun - 1][$kOpco][$tagReal] > 0 ? round(($dtYtd[$tahun][$kOpco][$tagReal] / $dtYtd[$tahun - 1][$kOpco][$tagReal])*100,2)  : $dtYtd[$tahun][$kOpco][$tagReal],
                ];
              }
            }
            $dataAll[$kKet][$kSig]['kode'] = 'SIG';
            
            $dataAll[$kKet][$kSig]['SIG'] = [
                'rkap'.$tahun.'_this_month'       => $dtNow[$tahun]['SIG'][$tagRkap],
                'real'.$tahun.'_this_month'       => $dtNow[$tahun]['SIG'][$tagReal],
                'real'.($tahun-1).'_this_month'   => $dtNow[$tahun - 1]['SIG'][$tagReal],
                '2_1'  => $dtNow[$tahun]['SIG'][$tagRkap] > 0 ? round(($dtNow[$tahun]['SIG'][$tagReal] / $dtNow[$tahun]['SIG'][$tagRkap])*100,2)  : $dtNow[$tahun]['SIG'][$tagReal],
                '2_3'  => $dtNow[$tahun - 1]['SIG'][$tagReal] > 0 ? round(($dtNow[$tahun]['SIG'][$tagReal] / $dtNow[$tahun - 1]['SIG'][$tagReal])*100,2)  : $dtNow[$tahun]['SIG'][$tagReal],
                'rkap'.$tahun.'_until_month'      =>$dtYtd[$tahun]['SIG'][$tagRkap],
                'real'.$tahun.'_until_month'      =>$dtYtd[$tahun]['SIG'][$tagReal],
                'real'.($tahun-1).'_until_month'  =>$dtYtd[$tahun - 1]['SIG'][$tagReal],
                '5_4'  => $dtYtd[$tahun]['SIG'][$tagRkap] > 0 ? round(($dtYtd[$tahun]['SIG'][$tagReal] / $dtYtd[$tahun]['SIG'][$tagRkap])*100,2)  : $dtYtd[$tahun]['SIG'][$tagReal],
                '5_6'  => $dtYtd[$tahun - 1]['SIG'][$tagReal] > 0 ? round(($dtYtd[$tahun]['SIG'][$tagReal] / $dtYtd[$tahun - 1]['SIG'][$tagReal])*100,2)  : $dtYtd[$tahun]['SIG'][$tagReal],
            ];
        }
        return($dataAll);
    }
}
