<?php

namespace App\Imports;

use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
class ImportKoreksiBdp implements ToCollection, WithHeadingRow
{
    public $data;

    public function collection(Collection $rows)
    {
        $this->data = $rows;
    }
    
    public function rules(): array
    {
        return[
            'plant' =>['required', 'string'],
            'tahun' => ['required', 'numeric'],
            'bdp_sig_tonday' => ['required', 'numeric'],
            'bdp_ghopo_tonday' => ['required', 'numeric'],
            'bdp_sg_tonday'    => ['required', 'numeric'],
            'bdp_sp_tonday'    => ['required', 'numeric'],
            'bdp_st_tonday' => ['required', 'numeric'],
            'bdp_sbi_tonday'    => ['required', 'numeric'],
            'bdp_tlcc_tonday'    => ['required', 'numeric']
        ];
    }
}
