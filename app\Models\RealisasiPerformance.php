<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RealisasiPerformance extends Model
{
    use SoftDeletes;

    protected $table = 'ts_realisasi_performance';
    protected $primaryKey = 'id_realisasi_performance';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [  
        'kode_opco',
        'kode_plant',
        'tanggal',
        'oph',
        'updt',
        'pdt',
        'stop_idle',
        'fy_stop',
        'frek_updt',
        'cal',
        'net_avail',
        'koreksi',
        'act_prod',
        'act_idle_prod',
        'tahun',
        'bulan',
        'rate_gross',
        'rate_netto'
    ];

}
