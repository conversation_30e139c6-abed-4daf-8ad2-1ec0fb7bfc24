<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use App\Models\RealisasiBiaya;
use App\Models\Opco;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\RealCostExport;

class RealCostController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Realisasi Cost',
            'breadcrumb' => [
                [
                    'title'=>'Data Realisasi',
                    'url'=>'/real-cost',
                ],
                [
                    'title'=>'Realisasi Cost',
                    'url'=>'',
                ]
            ],
        ];
        return view('realCost', $data);
    }

    public function getDatatables(Request $request)
    {
        $realCost = RealisasiBiaya::select(['id_realisasi_biaya','kode_opco',
            DB::raw("to_char(tanggal,'DD Month YYYY') as tanggal"),
            DB::raw("to_char(bahan_bakar, '999G999G999G999G999') as bahan_bakar"),
            DB::raw("to_char(bahan_baku, '999G999G999G999G999') as bahan_baku"),
            DB::raw("to_char(listrik, '999G999G999G999G999') as listrik"),
            DB::raw("to_char(tenaga_kerja, '999G999G999G999G999') as tenaga_kerja"),
            DB::raw("to_char(pemeliharaan, '999G999G999G999G999') as pemeliharaan"),
            DB::raw("to_char(penyusutan, '999G999G999G999G999') as penyusutan"),
            DB::raw("to_char(administrasi_umum, '999G999G999G999G999') as administrasi_umum"),
            DB::raw("to_char(pajak_asuransi, '999G999G999G999G999') as pajak_asuransi"),
            DB::raw("to_char(elim_bb, '999G999G999G999G999') as elim_bb"),
            DB::raw("to_char(elim_penyusutan, '999G999G999G999G999') as elim_penyusutan"),
            DB::raw("to_char(elim_administrasi, '999G999G999G999G999') as elim_administrasi")]);
        if($request->filter_opco){
            $realCost = $realCost -> where('kode_opco', $request->filter_opco);
        }
        if($request->filter_tahun){
            $realCost = $realCost -> where(DB::raw("extract(year from tanggal)"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $realCost = $realCost -> where(DB::raw("extract(month from tanggal)"), $request->filter_bulan);
        }
        if($request->filter_search){
            $filter = $request->filter_search;
            $realCost = $realCost -> where('kode_opco', 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(tanggal,'DD Month YYYY')"),'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("extract(year from tanggal)"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("extract(month from tanggal)"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(bahan_bakar, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(bahan_baku, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(listrik, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(tenaga_kerja, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(pemeliharaan, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(penyusutan, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(administrasi_umum, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(pajak_asuransi, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(elim_bb, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(elim_penyusutan, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(elim_administrasi, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%');
        }
        $data     = DataTables::of($realCost)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function getFilterRealCost()
    {
        //filter opco
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();

        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow; $i++) { 
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];  
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $bulan = [];
        for ($i=0; $i < count($months); $i++) { 
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => true
                ];
            } 
            else{
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => false
                ];
            }       
        }

        $data = [
            'kode_opco' => $opco,
            'tahun'     => $tahun,
            'bulan'     => $bulan
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function export(Request $request)
    {
        // Expot data with Collection
        $data = RealisasiBiaya::select(['kode_opco','tanggal','bahan_bakar','bahan_baku','listrik','tenaga_kerja','pemeliharaan','penyusutan','administrasi_umum','pajak_asuransi','elim_bb','elim_penyusutan','elim_administrasi']);
        if($request->filter_opco){
            $data = $data -> where('kode_opco', $request->filter_opco);
        }
        if($request->filter_tahun){
            $data = $data -> where(DB::raw("extract(year from tanggal)"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $data = $data -> where(DB::raw("extract(month from tanggal)"), $request->filter_bulan);
        }
        $data = $data->orderBy('id_realisasi_biaya')->get();
        return Excel::download(new RealCostExport($data), 'Realisasi Cost.xlsx');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\RealCostController  $realCostController
     * @return \Illuminate\Http\Response
     */
    public function show(RealCostController $realCostController)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\RealCostController  $realCostController
     * @return \Illuminate\Http\Response
     */
    public function edit(RealCostController $realCostController)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\RealCostController  $realCostController
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, RealCostController $realCostController)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\RealCostController  $realCostController
     * @return \Illuminate\Http\Response
     */
    public function destroy(RealCostController $realCostController)
    {
        //
    }
}
