<?php

namespace App\Console\Commands;

use App\Models\Kiln_Stop;
use App\Models\KilnPlant;
use App\Models\Opc_TIS;
use App\Models\Tis;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SyncTisCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'synchronize:tis';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate OPC TIS data';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $date = date("Y-m-d H:00:00");
        $date_now = date("Ymd");
        $date_nows = date("Y-m-d");
        $date_end = date("Y-m-d 00:00:00");
        $lastdate = Carbon::createFromFormat('Y-m-d', $date_nows)->subDays(1)->format('Y-m-d');
        $date_year = date("Y");
        try {
            DB::beginTransaction();
            $plants = KilnPlant::select('kode_plant')->where('kode_opco', 'SBI')->get();
            foreach ($plants as $plant) {
                $lastTis = Tis::select('kode_plant','status','tanggal')->where(['kode_plant' => $plant->kode_plant])->where('tanggal','>=',$date)->orderBy('id_opc_tis', 'desc')->first();
                if ($lastTis) {
                    $kodePlant = $plant->kode_plant;
                    $date = date($lastTis->tanggal);
                    $lastOpc = Opc_TIS::select()->where(['kode_plant' => $kodePlant, 'source_system' => 'TIS'])->orderBy('id_opc_tis', 'desc')->first();
                    $status = $lastTis->status > 0 ? 1 : 0;
                    if ($lastOpc) {
                        $lastOpc->status = $lastOpc->status > 0 ? 1 : 0;
                    }
                    // jika ada data sebelumnya
                    if ($lastOpc) {
                        // dihari yang sama
                        if (Carbon::parse($lastOpc->tanggal)->format('Ymd') == $date_now) {

                            if ($lastOpc && $lastOpc->status == $status) {
                                // ketika kondisi sebelumnya sama dengan kondisi saat ini contoh : hidup - hidup / mati - mati
                                Opc_TIS::find($lastOpc->id_opc_tis)->update(['update_by' => 'TIS_' . $kodePlant, 'updated_at' => date('Y-m-d H:i:s')]);
                            } else {
                                //Ketika di OPC TIS last status sebelumnya tidak sama dgn yang baru (mati atau hidup kiln status) sama hari
                                $data = [
                                    'kode_plant' => $kodePlant,
                                    'tanggal' => $date,
                                    'status' => $status,
                                    'source_system' => 'TIS',
                                    'create_by' => 'TIS_' . $kodePlant
                                ];
                                Opc_TIS::create($data);
                                // ketika kondisi sebelumnya berbeda dengan kondisi saat ini contoh : mati - hidup / hidup - mati
                                if ($lastOpc->status == 0) {
                                    // ketika kondisi status mati ke hidup
                                    //Mencari Data Kiln Stop di Hari yang sama , yang Tanggal_selesai Null
                                    $lastKilnStop = Kiln_Stop::where('kode_plant', $kodePlant)
                                    ->where(DB::raw("to_char(tanggal_mulai, 'YYYY-MM-DD')"), $date_nows)
                                    ->whereNull('tanggal_selesai')
                                        ->where('source_system', 'TIS')
                                        ->orderBy('tanggal_mulai', 'desc')
                                        ->first();

                                    $updateData['tanggal_selesai'] = $date;
                                    if($lastKilnStop){
                                        $lastKilnStop->update($updateData);
                                    }
                                } else {
                                    // ketika kondisi status hidup ke mati
                                    Kiln_Stop::create([
                                        'kode_plant' => $kodePlant,
                                        'tanggal_mulai' => $date,
                                        'source_system' => 'TIS',
                                        'create_by' => "TIS_" . $kodePlant
                                    ]);
                                }
                            }
                        } 
                        else {
                            // dihari yang berbeda
                            if ($lastOpc && $lastOpc->status == $status) {
                                //Ketika di status sama mati ke mati
                                if ($status == 0) {
                                    $data = [
                                        'kode_plant' => $kodePlant,
                                        'tanggal' => $date,
                                        'status' => $status,
                                        'source_system' => 'TIS',
                                        'create_by' => 'TIS_' . $kodePlant
                                    ];
                                    Opc_TIS::create($data);

                                    $lastKilnStop = Kiln_Stop::where('kode_plant', $kodePlant)
                                    ->where(DB::raw("to_char(tanggal_mulai, 'YYYY-MM-DD')"), $lastdate)
                                    ->whereNull('tanggal_selesai')
                                        ->where('source_system', 'TIS')
                                        ->orderBy('tanggal_mulai', 'desc')
                                        ->first();

                                    $updateData['tanggal_selesai'] = $date_end;
                                    if($lastKilnStop){
                                        $lastKilnStop->update($updateData);
                                    }
                                    Kiln_Stop::create([
                                        'kode_plant' => $kodePlant,
                                        'tanggal_mulai' => $date,
                                        'source_system' => 'TIS',
                                        'create_by' => "TIS_" . $kodePlant
                                    ]);
                                } else {
                                    //Ketika di status sama hidup ke hidup
                                    $data = [
                                        'kode_plant' => $kodePlant,
                                        'tanggal' => $date,
                                        'status' => $status,
                                        'source_system' => 'TIS',
                                        'create_by' => 'TIS_' . $kodePlant
                                    ];
                                    Opc_TIS::create($data);
                                }
                            } else {
                                // ketika di status mati ke hidup
                                if ($status == 1) {
                                    //Ketika di OPC TIS last status sebelumnya tidak sama dgn yang baru (mati atau hidup kiln status) di beda hari
                                    $data = [
                                        'kode_plant' => $kodePlant,
                                        'tanggal' => $date,
                                        'status' => $status,
                                        'source_system' => 'TIS',
                                        'create_by' => 'TIS_' . $kodePlant
                                    ];
                                    Opc_TIS::create($data);

                                    $lastKilnStop = Kiln_Stop::where('kode_plant', $kodePlant)
                                    // ->where(DB::raw("to_char(tanggal_mulai, 'YYYY-MM-DD')"), $lastdate)
                                    ->whereNull('tanggal_selesai')
                                        ->where('source_system', 'TIS')
                                        ->orderBy('tanggal_mulai', 'desc')
                                        ->first();

                                    $updateData['tanggal_selesai'] = DB::raw("TO_CHAR(tanggal_mulai::timestamp, 'YYYY-MM-DD')::date + INTERVAL '1 day'");
                                    if($lastKilnStop){
                                        $lastKilnStop->update($updateData);
                                    }

                                    Kiln_Stop::create([
                                        'kode_plant' => $kodePlant,
                                        'tanggal_mulai' => $date_end,
                                        'tanggal_selesai' => $date,
                                        'source_system' => 'TIS',
                                        'create_by' => "TIS_" . $kodePlant
                                    ]);
                                } else {
                                    // ketika di status hidup ke mati
                                    $data = [
                                        'kode_plant' => $kodePlant,
                                        'tanggal' => $date,
                                        'status' => $status,
                                        'source_system' => 'TIS',
                                        'create_by' => 'TIS_' . $kodePlant
                                    ];
                                    Opc_TIS::create($data);

                                    Kiln_Stop::create([
                                        'kode_plant' => $kodePlant,
                                        'tanggal_mulai' => $date,
                                        'source_system' => 'TIS',
                                        'create_by' => "TIS_" . $kodePlant
                                    ]);
                                }
                            }
                        }
                    } else {
                        // jika data sebelumnya belum ada
                        // dengan status mati
                        if ($status == 0) {
                            $data = [
                                'kode_plant' => $kodePlant,
                                'tanggal' => $date,
                                'status' => $status,
                                'source_system' => 'TIS',
                                'create_by' => 'TIS_' . $kodePlant
                            ];
                            Opc_TIS::create($data);

                            Kiln_Stop::create([
                                'kode_plant' => $kodePlant,
                                'tanggal_mulai' => $date,
                                'source_system' => 'TIS',
                                'create_by' => "TIS_" . $kodePlant
                            ]);
                        } else {
                            // dengan status hidup
                            $data = [
                                'kode_plant' => $kodePlant,
                                'tanggal' => $date,
                                'status' => $status,
                                'source_system' => 'TIS',
                                'create_by' => 'TIS_' . $kodePlant
                            ];
                            Opc_TIS::create($data);
                        }
                    }
                }
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e->getMessage());
        }
    }
}
