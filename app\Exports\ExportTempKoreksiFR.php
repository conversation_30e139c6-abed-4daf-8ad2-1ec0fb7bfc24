<?php

namespace App\Exports;

use App\Models\TKFR;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\FromCollection;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ExportTempKoreksiFR implements FromCollection, WithColumnFormatting, WithHeadings, ShouldAutoSize, WithEvents, WithTitle, WithColumnWidths, WithStyles
{
    public function  __construct($data)
    {
        $this->data = $data;
    }
     //set header value
     public function headings():array
     {
         return [
            'OPCO',
            'Date',
            'THN',
            'RKAP <PERSON>han Bakar',
            'RKAP <PERSON>han Baku Penolong',
            'RKAP Listrik',
            'RKAP Tenaga Kerja',
            'RKAP Pemeliharaan',
            'RKAP Deplesi, Penyusutan & Amortisasi',
            'RKAP Urusan Umum & Adm. Kantor',
            'RKAP Pajak & Asuransi',
            'RKAP Elim. Bahan Baku & Penolong',
            'RKAP Elim. Deplesi, Penyusutan & Amortisasi',
            'RKAP Elim. Urusan Umum & Adm. Kantor',
            'Rkap Prod. Clinker (ton)',
            'Rkap Prod. Cement (ton)',
            'Rkap Clinker sold (ton)',
            'Rkap prod. Output (ton)',
            'Bahan Bakar',
            'Bahan Baku & Penolong',
            'Listrik',
            'Tenaga Kerja',
            'Pemeliharaan',
            'Deplesi, Penyusutan & Amortisasi',
            'Urusan Umum & Adm. Kantor',
            'Pajak dan Asuransi',
            'Elim. Bahan Baku & Penolong',
            'Elim. Deplesi, Penyusutan & Amortisasi',
            'Elim. Urusan Umum & Adm. Kantor',
            'Real. Prod. Clinker (ton)',
            'Real. Prod. Cement (ton)',
            'Real. Clinker sold (ton)',
            'Real prod. Output (ton)',
            'ICS (ton)',
         ];
     }

     public function collection()
    {
        return collect($this->data);
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_DDMMYYYY,
        ];
    }

     public function registerEvents(): array
     {
         return [
             AfterSheet::class    => function(AfterSheet $event) {
                 //set background color for header (A1:D1)
                 $event->sheet->getDelegate()
                 ->getStyle('A1:AH1')
                 ->getFill()
                 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                 ->getStartColor()
                 ->setARGB('C4D79B');
                //  $event->sheet->getDelegate()->getRowDimension('1')->setRowHeight(40);


                 //set header (A1:D1) uneditable and set workspace (A2:D1000) editable
                 // $event->sheet->protectCells('A1:D1', 'PASSWORD');
                 // $event->sheet->getStyle('A2:D1000')->getProtection()->setLocked(\PhpOffice\PhpSpreadsheet\Style\Protection::PROTECTION_UNPROTECTED);
                 // $event->sheet->getDelegate()->getProtection()->setSheet(true);

             },
         ];
     }

     //set name for worksheet
     public function title(): string
     {
         return 'Koreksi FR';
     }

     //set width for header
     public function columnWidths(): array
     {
         return [
             'A' => 15,
             'B' => 15,
             'C' => 15,
             'D' => 15,
             'E' => 15,
             'F' => 15,
             'G' => 15,
             'H' => 15,
             'I' => 15,
             'J' => 15,
             'K' => 15,
             'L' => 15,
             'M' => 15,
             'N' => 15,
             'O' => 15,
             'P' => 15,
             'Q' => 15,
             'R' => 15,
             'S' => 15,
             'T' => 15,
             'U' => 15,
             'V' => 15,
             'W' => 15,
             'X' => 15,
             'Y' => 15,
             'Z' => 15,
             'AA' => 15,
             'AB' => 15,
             'AC' => 15,
             'AD' => 15,
             'AE' => 15,
             'AF' => 15,
             'AG' => 15,
             'AH' => 15,
         ];
     }

     //set bold for header
     public function styles(Worksheet $sheet)
     {
         return [
             'A'    => ['font' => ['bold' => true]],
             'B'    => ['font' => ['bold' => true]],
             'C'    => ['font' => ['bold' => true]],
             'D'    => ['font' => ['bold' => true]],
             'E'    => ['font' => ['bold' => true]],
             'F'    => ['font' => ['bold' => true]],
             'G'    => ['font' => ['bold' => true]],
             'H'    => ['font' => ['bold' => true]],
             'I'    => ['font' => ['bold' => true]],
             'J'    => ['font' => ['bold' => true]],
             'K'    => ['font' => ['bold' => true]],
             'L'    => ['font' => ['bold' => true]],
             'M'    => ['font' => ['bold' => true]],
             'N'    => ['font' => ['bold' => true]],
             'O'    => ['font' => ['bold' => true]],
             'P'    => ['font' => ['bold' => true]],
             'Q'    => ['font' => ['bold' => true]],
             'R'    => ['font' => ['bold' => true]],
             'S'    => ['font' => ['bold' => true]],
             'T'    => ['font' => ['bold' => true]],
             'U'    => ['font' => ['bold' => true]],
             'V'    => ['font' => ['bold' => true]],
             'W'    => ['font' => ['bold' => true]],
             'X'    => ['font' => ['bold' => true]],
             'Y'    => ['font' => ['bold' => true]],
             'Z'    => ['font' => ['bold' => true]],
             'AA'    => ['font' => ['bold' => true]],
             'AB'    => ['font' => ['bold' => true]],
             'AC'    => ['font' => ['bold' => true]],
             'AD'    => ['font' => ['bold' => true]],
             'AE'    => ['font' => ['bold' => true]],
             'AF'    => ['font' => ['bold' => true]],
             'AG'    => ['font' => ['bold' => true]],
             'AH'    => ['font' => ['bold' => true]],
         ];
     }

}
