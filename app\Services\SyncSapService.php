<?php

namespace App\Services;

set_time_limit(0);
use App\Helpers\GlobalHelper;
use App\Models\CostCenter;
use App\Models\CostElement;
use App\Models\DetailBiaya;
use App\Models\DetailCapex;
use App\Models\DetailProduksi;
use App\Models\Opco;
use App\Models\PlantICS;
use App\Models\Project;
use App\Models\RealisasiBiaya;
use App\Models\RealisasiCapex;
use App\Models\RealisasiPenjualan;
use App\Models\RealisasiProduksi;
use App\Models\SyncSapConfig;
use App\Models\SyncSapLog;
use App\Models\ViewOpex;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;

use SAPNWRFC\Connection as SapConnection;
use SAPNWRFC\Exception as SapException;

class SyncSapService extends BaseService
{
    private function getData($config, $status = false,$dateP = null,$opco = null)
    {
        $id = 0;
        $dateParam = [];
        if (!$status || $status == 'resync') {
            $id = $config->id;
        }
        $arrayParam = json_decode($config->parameter, true);
        if ($config->type == 'biaya') {
            $ta = Carbon::now()->subDay()->format('Ymd');
            $tr = Carbon::now()->format('Ymd');
            if($dateP){
                // dd($dateP);
                $ta = $dateP['ta']; 
                $tr = $dateP['tr']; 
            }
            $dateParam['ta']= Carbon::createFromFormat('Ymd', $ta)->format('Y-m-d');
            $dateParam['tr']= Carbon::createFromFormat('Ymd', $tr)->format('Y-m-d');
            $date = [
                'I_BUDAT_FROM' => $ta,//Carbon::now()->subDay()->format('Ymd'),
                'I_BUDAT_TO' => $tr,//Carbon::now()->format('Ymd'),
            ];
            $params = [
                'I_KOKRS' => 'SGG',
                'LR_KOSTL' => [],
                'LR_KSTAR' => []
            ];
                $costCenters = CostCenter::select('cost_center')->get();
            foreach ($costCenters as $costCenter) {
                array_push($params['LR_KOSTL'], [
                    "SIGN" => "I",
                    "OPTION" => "EQ",
                    "LOW" => "$costCenter->cost_center",
                    "HIGH" => ""
                ]);
            }
            $costElements = CostElement::select('cost_element')->get();
            foreach ($costElements as $costElement) {
                $low = str_pad($costElement->cost_element, 10, "0", STR_PAD_LEFT);
                array_push($params['LR_KSTAR'], [
                    "SIGN" => "I",
                    "OPTION" => "EQ",
                    "LOW" => "$low",
                    "HIGH" => ""
                ]);
            }
            $arrayParam = array_merge($params, $date);
        } elseif ($config->type == 'produksi') {
            if ($config->kode_opco == 'SBI') {
                $plants = PlantICS::where('kode_opco', 'SBI')->get();
            } else {
                $plants = PlantICS::where('kode_opco', '<>', 'SBI')->whereNotNull('kode_opco')->get();
            }
            $arrayParam['T_PLANT_RA'] = [];
            foreach ($plants as $plant) {
                array_push($arrayParam['T_PLANT_RA'], [
                    "SIGN" => "I",
                    "OPTION" => "EQ",
                    "LOW" => "$plant->plant",
                    "HIGH" => ""
                ]);
            }
            $ta = Carbon::now()->subDay()->format('Ymd');
            $tr = Carbon::now()->format('Ymd');
            if($dateP){
                // dd($dateP);
                $ta = $dateP['ta']; 
                $tr = $dateP['tr']; 
            }
            $dateParam['ta']= Carbon::createFromFormat('Ymd', $ta)->format('Y-m-d');
            $dateParam['tr']= Carbon::createFromFormat('Ymd', $tr)->format('Y-m-d');
            $date = [
                "T_PSTNG_DATE_RA" => [
                    [
                        "SIGN"      =>  "I",
                        "OPTION"    =>  "BT",
                        "LOW"       =>  $ta,//Carbon::now()->subDay()->format('Ymd'),
                        "HIGH"      =>  $tr,//Carbon::now()->format('Ymd'),
                    ]
                ]
            ];
            $arrayParam = array_merge($arrayParam, $date);
        } elseif ($config->type == 'capex') {
            if ($config->kode_opco == 'SBI') {
                $projects = Project::where('kode_opco', '7001')->get();
            } else {
                $projects = Project::where('kode_opco', '<>', '7001')->get();
            }
            $params['R_PSPID'] = [];
            foreach ($projects as $project) {
                //ambil 8 digit no project terdepan
                $noprjt = str_replace("-","",substr($project->no_project,0,8));
                array_push($params['R_PSPID'], [
                    "SIGN" => "I",
                    "OPTION" => "EQ",
                    "LOW" => "$noprjt",
                    "HIGH" => ""
                ]);
            }
            $ta = Carbon::now()->subDay()->format('Ymd');
            $tr = Carbon::now()->format('Ymd');
            if($dateP){
                $ta = $dateP['ta']; 
                $tr = $dateP['tr']; 
            }
            $dateParam['ta']= Carbon::createFromFormat('Ymd', $ta)->format('Y-m-d');
            $dateParam['tr']= Carbon::createFromFormat('Ymd', $tr)->format('Y-m-d');
            $date = [
                "R_BUDAT" => [
                    [
                        "SIGN"      =>  "I",
                        "OPTION"    =>  "BT",
                        "LOW"       =>  $ta,//Carbon::now()->subDay()->format('Ymd'),
                        "HIGH"      =>  $tr,//Carbon::now()->format('Ymd'),
                    ]
                ]
            ];
            $arrayParam = array_merge($params, $date);
        } elseif ($config->type == 'penjualan') {
            if ($config->kode_opco == 'SBI') {
                $arrayParam = [];
                $ta = Carbon::now()->format('Ymd');
                $tr = Carbon::now()->format('Ymd');
                if($dateP){
                    $ta = $dateP['ta']; 
                    $tr = $dateP['tr']; 
                }
                $date = [
                    'LR_TGL_ISSUED' => array([ "SIGN" => "I",
                    "OPTION" => "BT",
                    "LOW" => "$ta",
                    "HIGH" => "$tr"])
                ];
                $dateParam['ta']= Carbon::createFromFormat('Ymd', $ta)->format('Y-m-d');
                $dateParam['tr']= Carbon::createFromFormat('Ymd', $tr)->format('Y-m-d');
                $arrayParam = array_merge($arrayParam, $date);
                $plants = PlantICS::where('kode_opco', 'SBI')->get();
                $arrayParam['LR_WERKS'] = [];
                foreach ($plants as $plant) {
                    array_push($arrayParam['LR_WERKS'], [
                        "SIGN" => "I",
                        "OPTION" => "EQ",
                        "LOW" => "$plant->plant",
                        "HIGH" => ""
                    ]);
                }

                $listComp = [
                    'LR_VKORG' => array([ "SIGN" => "I",
                    "OPTION" => "EQ",
                    "LOW" => "SCBX",
                    "HIGH" => ""])
                ];
                $arrayParam = array_merge($arrayParam, $listComp);
                $listDiv = [
                    'LR_DIVISION' => array([ "SIGN" => "I",
                    "OPTION" => "EQ",
                    "LOW" => "CM",
                    "HIGH" => ""])
                ];
                $arrayParam = array_merge($arrayParam, $listDiv);
            }else{
            $ta = Carbon::now()->format('Ymd');
            $tr = Carbon::now()->format('Ymd');
            if($dateP){
                $ta = $dateP['ta']; 
                $tr = $dateP['tr']; 
            }
            $date = [
                'X_TGL1' => $ta,//Carbon::now()->format('Ymd'),
                'X_TGL2' => $tr,//Carbon::now()->format('Ymd'),
            ];
            $dateParam['ta']= Carbon::createFromFormat('Ymd', $ta)->format('Y-m-d');
            $dateParam['tr']= Carbon::createFromFormat('Ymd', $tr)->format('Y-m-d');
            $arrayParam = array_merge($arrayParam, $date);
            if($opco){
                $listComp = [
                    'X_VKORG' => $opco,
                ];
                $arrayParam = array_merge($arrayParam, $listComp);
            }
            }
            
        }
        $config->dateParam = $dateParam;
        $config->opco = $opco;
        if ($status == 'resync' || $status == 'test') {
            $config->parsedParam = $config->parameter;
            if($config->type=='penjualan' && $config->kode_opco!='SBI'){ //add kode opco
                $jparam = json_decode($config->parameter,true);
                if(array_key_exists('X_VKORG',$jparam))
                $opco = $jparam['X_VKORG'];
            }
        } else {
            $config->parsedParam = json_encode($arrayParam);
        }
        $ConfigName = $config->name . " ". ($opco == null?"":$opco);
        $model = SyncSapLog::create([
            'sync_sap_config_id' => $id,
            'config_name' => $ConfigName,
            'year' => date('Y'),
            'month' => date('m'),
            'status' => 'process',
            'tcode' => $config->tcode,
            'parameter' => $config->parsedParam,
        ]);

        $response = $this->reqSap($config);

        $statusResponse = 'success';
        if ($response['status'] == 'fail') {
            $statusResponse = 'fail';
            $model->note = substr($response['message'], 0, 190);
        } elseif ($status == 'test') {
            $model->note = "Test Run";
        } elseif ($response['status'] == 'success' && stripos($response['message'], 'success') === false) {
            $model->note = substr($response['message'], 0, 190);
        }

        $model->status = $statusResponse;
        $model->save();

        return $response;
    }

    private function reqSap($config)
    {
        $connSource = 'no_sbi';
        if ($config->kode_opco == 'SBI') {
            $connSource = 'sbi';
        }
        $sapConfig = $this->getSapConfig($connSource);

        try {
            $type = $config->type;
            $param = json_decode($config->parsedParam, true);
            $options = [
                'rtrim' => true
            ];
            $sap = new SapConnection($sapConfig);
            $function = $sap->getFunction($config->tcode);
            $result = $function->invoke($param, $options);
            if ($type == 'penjualan') {
                if ($config->kode_opco == 'SBI') {
                    $data = $result['T_RETURN'];
                }else {
                $data = $result['ZDATA'];
            } 
            }
            else if ($type == 'produksi') {
                //Join GET UOM
                $T_GOODSMVT_ITEM = collect($result['T_GOODSMVT_ITEM'])->pluck('ENTRY_UOM','MAT_DOC')->toArray();
                $T_DATA = collect($result['T_DATA'])->map(function ($item) use($T_GOODSMVT_ITEM){
                         $item['ENTRY_UOM'] = array_key_exists($item['MBLNR'],$T_GOODSMVT_ITEM)?$T_GOODSMVT_ITEM[$item['MBLNR']]:"";
                         $item['ERFMG_DK'] = $item['BWART']=='102'? $item['ERFMG']*-1:$item['ERFMG'];;
                        return $item;
                    })->toArray();
                $data =  $T_DATA ;
            } else {
                $data = $result['T_DATA'];
            }
            $this->store($data, $type,$config);

            return [
                'status' => 'success',
                'message' => 'Successfully synced and saved data',
            ];
        } catch (SapException $e) {
            $errorInfo = $e->getErrorInfo();
            if (stripos($errorInfo['message'], 'no costs') !== false) {
                return [
                    'status' => 'success',
                    'message' => $errorInfo['message'],
                ];
            }
            return [
                'status' => 'fail',
                'message' => $e->getMessage(),
            ];
        } catch (Exception $e) {
            return [
                'status' => 'fail',
                'message' => $e->getMessage(),
            ];
        }
    }

    private function store($data, $type,$config,$isTrx = true)
    {
        try {
            if($isTrx)
            DB::beginTransaction();
            $date = date('Y-m-d H:i:s');
            $opcoModel = Opco::select('kode_opco','reference_sap')->get();
            $plantIcsModel = PlantICS::select('plant','kode_opco')->whereNotNull('kode_opco')->get();
            if ($type == 'biaya') {
                $costElements = CostElement::select('cost_element','cost_element_name')->get()->pluck('cost_element_name','cost_element')->toArray();
                $costCenters = CostCenter::select('cost_center','cost_center_name')->get()->pluck('cost_center_name','cost_center')->toArray();
                $lastDate = '';
                $arr_lastDate = [];
                $date = date('Y-m-d H:i:s');
                if($this->deleteFirst($config)){  
                    //Data Preparation
                    $insertData = [];
                    foreach($data as $item){
                    $lastDate = Carbon::createFromFormat('Ymd', $item['BUDAT'])->format('Y-m-d');
                    $arr_lastDate[] =  (object) array('last_date' => $lastDate);
                    $costElement = substr($item['KSTAR'], 2);
                    $costCenter = $item['KOSTL'];
                        $elementName = array_key_exists($costElement,$costElements)?$costElements[$costElement]:null ;
                        $costCenterName = array_key_exists($costCenter,$costCenters)?$costCenters[$costCenter]:null ;
                        $insertData[] = [
                        'no_dokement' => substr($item['BELNR'], 1),
                        'kode_opco' => $config->kode_opco=='SBI'?'7001':$item['BUKRS'],
                        'cost_element' => $costElement,
                        'cost_center' => $costCenter,
                        'tanggal' => $lastDate,
                        'row' => $item['BUZEI'],
                        'cost_element_name' => $elementName,
                        'cost_center_name' => $costCenterName,
                        'biaya' =>  floatval($item['WTGBTR'])*100, //kali 100 info mas andrie khusus SAP
                        'create_date' => $date,
                        'update_date' => $date,
                        ];
                    }    
                    $chunks = array_chunk($insertData,5000);
                    foreach ($chunks as $chunk)
                    {
                        DetailBiaya::insert($chunk);
                }
                $curMonth = '';
                $lastDate = $this->sotrDate($arr_lastDate);
                if ($lastDate) {
                    $curMonth = Carbon::parse($lastDate)->format('Y-m');
                }

                $opex = ViewOpex::where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $curMonth)->get();

                foreach ($opex as $item) {
                    $realOpex = RealisasiBiaya::where('kode_opco', $item->kode_opco)
                        ->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $curMonth)->first();
                    if ($realOpex) {
                        $realOpex->update([
                            'tanggal' => $lastDate,
                            'bahan_bakar' => $this->inMillion($item->bahan_bakar,3),
                            'bahan_baku' => $this->inMillion($item->bahan_baku,3),
                            'listrik' => $this->inMillion($item->listrik,3),
                            'tenaga_kerja' => $this->inMillion($item->tenaga_kerja,3),
                            'pemeliharaan' => $this->inMillion($item->pemeliharaan,3),
                            'penyusutan' => $this->inMillion($item->penyusutan,3),
                            'administrasi_umum' => $this->inMillion($item->administrasi_umum,3),
                            'pajak_asuransi' => $this->inMillion($item->pajak_asuransi,3),
                            'elim_bb' => $this->inMillion($item->elim_bb,3),
                            'elim_penyusutan' => $this->inMillion($item->elim_penyusutan,3),
                            'elim_administrasi' => $this->inMillion($item->elim_administrasi,3)
                        ]);
                    } else {
                        RealisasiBiaya::create([
                            'kode_opco' => $item->kode_opco,
                            'tanggal' => $lastDate,
                            'bahan_bakar' => $this->inMillion($item->bahan_bakar,3),
                            'bahan_baku' => $this->inMillion($item->bahan_baku,3),
                            'listrik' => $this->inMillion($item->listrik,3),
                            'tenaga_kerja' => $this->inMillion($item->tenaga_kerja,3),
                            'pemeliharaan' => $this->inMillion($item->pemeliharaan,3),
                            'penyusutan' => $this->inMillion($item->penyusutan,3),
                            'administrasi_umum' => $this->inMillion($item->administrasi_umum,3),
                            'pajak_asuransi' => $this->inMillion($item->pajak_asuransi,3),
                            'elim_bb' => $this->inMillion($item->elim_bb,3),
                            'elim_penyusutan' => $this->inMillion($item->elim_penyusutan,3),
                            'elim_administrasi' => $this->inMillion($item->elim_administrasi,3)
                        ]);
                        }
                    }
                }
            } elseif ($type == 'produksi') {
                $selectedOpco = '';
                $tanggalProd = Carbon::now()->format('Y-m-d');
                $arr_kodeopco = [];
                $arr_plant = [];
                //Data Preparation
                // $time_start = microtime(true);
                $insertData = [];
                $plantColl =  $plantIcsModel->pluck('kode_opco','plant')->toArray();
                foreach($data as $item){
                    $arr_plant[$item['PLANT']] = $item['PLANT'];
                    $selectedOpco = array_key_exists($item['PLANT'],$plantColl)?$plantColl[$item['PLANT']]:null ;//data_get($plant, 'kode_opco', null);
                    $arr_kodeopco[$selectedOpco] = array_values($arr_plant);
                    $tanggalProd = Carbon::createFromFormat('Ymd', $item['BUDAT'])->format('Y-m-d');
                    $insertData[]  = [
                        'no_dokument' => $item['MBLNR'],
                        'kode_plant' => $item['PLANT'],
                        'kode_material' => trim($item['MATERIAL']),
                        'tanggal' => $tanggalProd,
                        'produksi' => floatval($item['ERFMG_DK'])
                    ];
                }
                // $time_end = microtime(true);
                // echo "Array : ".number_format($time_end-$time_start,4).'</hr>';
                // echo count($insertData);

                if($this->deleteFirst($config,$arr_plant)){
                    $chunks = array_chunk($insertData,5000);
                    foreach ($chunks as $chunk)
                    {
                        DetailProduksi::insert($chunk);
                    }
                    //Last Date by Filter
                    $to = $config->dateParam['tr'];
                    //Multi Opco
                    foreach($arr_kodeopco as $selectedOpco => $plants){
                        if($selectedOpco){
                            $tanggalCekProd = Carbon::createFromFormat('Y-m-d', $to)->format('Y-m');
                            if($selectedOpco !== 'SBI'){
                            $sumProduksiClinker = DetailProduksi::leftJoin('m_plant_ics', 'm_plant_ics.plant', '=', 't_detail_produksi.kode_plant')
                    ->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $tanggalCekProd)
                    ->where('kode_material', 'like', '121-2%')
                            ->where('m_plant_ics.kode_opco', $selectedOpco)
                    ->select(DB::raw('SUM(produksi) as clinker_prod'))
                    ->first();
                            $sumProduksiSemen = DetailProduksi::leftJoin('m_plant_ics', 'm_plant_ics.plant', '=', 't_detail_produksi.kode_plant')
                    ->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $tanggalCekProd)
                    ->where('kode_material', 'like', '121-3%')
                            ->where('m_plant_ics.kode_opco', $selectedOpco)
                    ->select(DB::raw('SUM(produksi) as semen_prod'))
                    ->first();
                            }else{
                                $sumProduksiClinker = DetailProduksi::leftJoin('m_plant_ics', 'm_plant_ics.plant', '=', 't_detail_produksi.kode_plant')
                                    ->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $tanggalCekProd)
                                    ->whereraw("kode_material::numeric in (select kode_material::numeric from mm_material_sbi WHERE jenis_material='KLINKER')")
                                    ->where('m_plant_ics.kode_opco', $selectedOpco)
                                    ->select(DB::raw('SUM(produksi) as clinker_prod'))
                                    ->first();
                                $sumProduksiSemen = DetailProduksi::leftJoin('m_plant_ics', 'm_plant_ics.plant', '=', 't_detail_produksi.kode_plant')
                                    ->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $tanggalCekProd)
                                    ->whereraw("kode_material::numeric in (select kode_material::numeric from mm_material_sbi WHERE jenis_material != 'KLINKER')")
                                    ->where('m_plant_ics.kode_opco', $selectedOpco)
                                    ->select(DB::raw('SUM(produksi) as semen_prod'))
                                    ->first();
                            }

                            $clinkerProd = data_get($sumProduksiClinker, 'clinker_prod', 0);
                $semenProd = data_get($sumProduksiSemen, 'semen_prod', 0);

                            $lastRealProd = RealisasiProduksi::select('id_realisasi_produksi','tanggal','prod_klinker','prod_semen','kode_opco')->where('kode_opco', $selectedOpco)->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $tanggalCekProd)->first();
                if (!$lastRealProd && $selectedOpco) {
                    RealisasiProduksi::create([
                        'tanggal' => $tanggalProd,
                        'prod_klinker' => round($clinkerProd,2),
                        'prod_semen' => round($semenProd,2),
                        'clinker_sold' => 0,
                        'prod_output' => 0,
                        'ics' => 0,
                        'kode_opco' => $selectedOpco
                    ]);
                } elseif ($lastRealProd) {
                    $lastRealProd->update(['prod_klinker' => round($clinkerProd,2), 'prod_semen' => round($semenProd,2), 'tanggal' => $tanggalProd]);
                }
                        }
                    }
                }
               
            } elseif ($type == 'capex') {
                $lastDate = '';
                $arr_lastDate = [];
                foreach ($data as $item) {
                    $lastDate = Carbon::createFromFormat('Ymd', $item['BUDAT'])->format('Y-m-d');
                    $arr_lastDate[] =  (object) array('last_date' => $lastDate);
                    if ($config->kode_opco !== 'SBI') {
                    DetailCapex::updateOrCreate([
                        'no_billing' => $item['REFBN'],
                        'kode_opco' => $item['BUKRS'],
                        'no_project' => $item['PSPID_S'],
                        'tanggal' => $lastDate,
                        'row' => $item['BUZEI'],
                    ], [
                        'biaya_capex' => floatval($item['WOGBTR'])*100, //kali 100 info mas andrie khusus SAP
                        'mutasi' => $item['BEKNZ'],
                    ]);
                    }else{
                        DetailCapex::updateOrCreate([
                            'no_billing' => $item['REFBN'],
                            'kode_opco' => "7001",
                            'no_project' => $item['PSPID_S'],
                            'tanggal' => $lastDate,
                            'row' => $item['BUZEI'],
                        ], [
                            'biaya_capex' => floatval($item['WOGBTR'])*100, //kali 100 info mas andrie khusus SAP
                            'mutasi' => $item['BEKNZ'],
                        ]);
                    }
                }
                $lastDate =  $this->sotrDate($arr_lastDate);
                $curMonth = '';
                if ($lastDate) {
                    $curMonth = Carbon::parse($lastDate)->format('Y-m');
                }

                $sumCapex = DetailCapex::where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $curMonth)
                    ->select('kode_opco', DB::raw('SUM(biaya_capex) as real_capex'))
                    ->groupBy('kode_opco')
                    ->get();

                foreach ($sumCapex as $item) {
                    $opco = $opcoModel->where('reference_sap', $item->kode_opco)->first();
                    $selectedOpco = data_get($opco, 'kode_opco', null);
                    $realCapex = RealisasiCapex::where('kode_opco', $selectedOpco)
                        ->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $curMonth)->first();
                    if ($realCapex) {
                        $realCapex->update(['tanggal' => $lastDate, 'real_capex' =>  $this->inMillion($item->real_capex,3)]);
                    } else {
                        RealisasiCapex::create([
                            'kode_opco' => $selectedOpco,
                            'tanggal' => $lastDate,
                            'real_capex' =>  $this->inMillion($item->real_capex,3)
                        ]);
                    }
                }
            } elseif ($type == 'penjualan') {
                $selectedOpco = '';
                if ($config->kode_opco == 'SBI') {
                    // SBI
                    $tanggalSPJ = '';
                    $tanggalSPJMont = '';
                    if($this->deleteFirst($config)){
                        //Data Preparation
                        $insertData = [] ;
                        $opcoColl = $opcoModel->pluck('kode_opco','reference_sap')->toArray();
                        foreach($data as $item){
                            //Skip Data Tidak ada TGL
                            // dd($item);
                            if ($item['WEIGHT_OUT_DATE'] == '00000000' || $item['WEIGHT_OUT_DATE'] == '00000000')
                                continue;
                            $tanggalSPJ  = Carbon::createFromFormat('Ymd', $item['WEIGHT_OUT_DATE'])->format('Y-m-d');
                            $tanggalSPJMont  = Carbon::createFromFormat('Ymd', $item['WEIGHT_OUT_DATE'])->format('Y-m');
                            $selectedOpco = "SBI";
                            // dd($item);
                            $insertData[] = [
                                'no_spj' => ($item['LDT_NO']),
                                'no_so' => ($item['SO_NO']),
                                'no_do' => ($item['DO_NO']),
                                'tgl_spj' => Carbon::createFromFormat('Ymd', $item['WEIGHT_OUT_DATE'])->isoFormat('YYYY-MM-DD'),
                                'tgl_do' => Carbon::createFromFormat('Ymd', $item['WEIGHT_OUT_DATE'])->isoFormat('YYYY-MM-DD'),
                                // 'kwantum' => intval($item['KWANTUM']),
                                'kwantumx' => intval($item['DO_QTY']),
                                'no_polisi' => ($item['TRUCK_ID']),
                                // 'no_spss' => ($item['NO_SPPS']),
                                'nama_sopir' => ($item['DRIVER_NAME']),
                                'kode_da' => ($item['SHIPTO_NO']),
                                'nama_toko' => ($item['SHIPTO_NAME']),
                                // 'no_expeditur' => ($item['NO_EXPEDITUR']),
                                // 'nama_expeditur' => ($item['NAMA_EXPEDITUR']),
                                'kode_plant' => ($item['PLANT']),
                                // 'nama_plant' => ($item['NAMA_PLANT']),
                                'sold_to' => ($item['SOLD_TO']),
                                'nama_sold_to' => ($item['SOLD_TO_NAME']),
                                // 'no_po' => ($item['NO_PO']),
                                // 'st' => ($item['STATUS']),
                                'produk' => ($item['PRODUCT_DESCRIPTION']),
                                'item_no' => ($item['MATERIAL_CODE']),
                                'uom' => ($item['UOM']),
                                // 'harga' => intval($item['HARGA']),
                                'kode_opco' => $selectedOpco,
                                'created_at' => Carbon::now()->toDateTimeString(),
                                'updated_at' => Carbon::now()->toDateTimeString()
                            ];
                            // dd($insertData);
                        }

                        $chunks = array_chunk($insertData,5000);
                        foreach ($chunks as $chunk)
                        {
                            RealisasiPenjualan::insert($chunk);
                        }

                        $sumRealPenjualan = RealisasiPenjualan::where('kode_opco', $selectedOpco)
                            ->where(DB::raw("to_char(tgl_spj, 'YYYY-MM')"), $tanggalSPJMont) // Tgl  sesuai tgl SPJ
                            ->whereraw("item_no::numeric in (select kode_material::numeric from mm_material_sbi WHERE jenis_material='KLINKER')")
                            // ->where('item_no', 'like', '121-2%')
                            ->select('kode_opco', DB::raw('SUM(kwantumx) as clinker_sold'))
                            ->groupBy('kode_opco')
                            ->first();

                        $clinkerSold = data_get($sumRealPenjualan, 'clinker_sold', 0);

                        $sumICSPenjualan = RealisasiPenjualan::where('t_realisasi_penjualan.kode_opco', 'SBI')
                        ->join('m_plant_ics', function($join)
                        {
                            $join->on('m_plant_ics.plant', '=', 't_realisasi_penjualan.kode_da');
                        })
                        ->where(DB::raw("to_char(tgl_spj, 'YYYY-MM')"), $tanggalSPJMont) // Tgl  sesuai tgl SPJ
                        ->whereNotNull("m_plant_ics.kode_opco") // Kode Opco yang ada isinya.
                        ->select('t_realisasi_penjualan.kode_opco', DB::raw('SUM(kwantumx) as ics_sold'))
                        ->groupBy('t_realisasi_penjualan.kode_opco')
                        ->first();

                        $icsSold = data_get($sumICSPenjualan, 'ics_sold', 0);

                        $lastRealProd = RealisasiProduksi::where('kode_opco', $selectedOpco)->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $tanggalSPJMont)->first();
                        if (!$lastRealProd && $selectedOpco) {
                            RealisasiProduksi::create([
                                'tanggal' => $tanggalSPJ,
                                'prod_klinker' => 0,
                                'prod_semen' => 0,
                                'clinker_sold' => $clinkerSold,
                                'prod_output' => 0,
                                'ics' => $icsSold,
                                'kode_opco' => $selectedOpco
                            ]);
                        } elseif ($lastRealProd) {
                            $lastRealProd->update(['clinker_sold' => $clinkerSold, 'ics' => $icsSold, 'tanggal' => $tanggalSPJ]);
                        }
                    }
                }else{
                    // SIG
                $tanggalSPJ = '';
                $tanggalSPJMont = '';
                if($this->deleteFirst($config)){
                    //Data Preparation
                    $insertData = [] ;
                    $opcoColl = $opcoModel->pluck('kode_opco','reference_sap')->toArray();
                    foreach($data as $item){
                        //Skip Data Tidak ada TGL
                        if ($item['TGL_SPJ'] == '00000000' || $item['TGL_DO'] == '00000000')
                            continue;
                        $tanggalSPJ  = Carbon::createFromFormat('Ymd', $item['TGL_SPJ'])->format('Y-m-d');
                        $tanggalSPJMont  = Carbon::createFromFormat('Ymd', $item['TGL_SPJ'])->format('Y-m');
                        $selectedOpco = array_key_exists($item['NMORG'],$opcoColl)?$opcoColl[$item['NMORG']]:null;
                        $insertData[] = [
                            'no_spj' => ($item['NO_SPJ']),
                            'no_so' => ($item['NO_SO']),
                            'no_do' => ($item['NO_DO']),
                            'tgl_spj' => Carbon::createFromFormat('Ymd', $item['TGL_SPJ'])->isoFormat('YYYY-MM-DD'),
                            'tgl_do' => Carbon::createFromFormat('Ymd', $item['TGL_DO'])->isoFormat('YYYY-MM-DD'),
                            'kwantum' => intval($item['KWANTUM']),
                            'kwantumx' => intval($item['KWANTUMX']),
                            'no_polisi' => ($item['NO_POLISI']),
                            'no_spss' => ($item['NO_SPPS']),
                                'nama_sopir' => GlobalHelper::convert_UTF_8(($item['NAMA_SOPIR'])),
                            'kode_da' => ($item['KODE_DA']),
                                'nama_toko' => GlobalHelper::convert_UTF_8(($item['NAMA_TOKO'])),
                            'no_expeditur' => ($item['NO_EXPEDITUR']),
                                'nama_expeditur' => GlobalHelper::convert_UTF_8(($item['NAMA_EXPEDITUR'])),
                            'kode_plant' => ($item['PLANT']),
                                'nama_plant' => GlobalHelper::convert_UTF_8(($item['NAMA_PLANT'])),
                            'sold_to' => ($item['SOLD_TO']),
                                'nama_sold_to' => GlobalHelper::convert_UTF_8(($item['NAMA_SOLD_TO'])),
                            'no_po' => ($item['NO_PO']),
                            'st' => ($item['STATUS']),
                            'produk' => ($item['PRODUK']),
                            'item_no' => ($item['ITEM_NO']),
                            'uom' => ($item['UOM']),
                            'harga' => intval($item['HARGA']),
                            'kode_opco' => $selectedOpco,
                            'created_at' => Carbon::now()->toDateTimeString(),
                            'updated_at' => Carbon::now()->toDateTimeString()
                        ];
                    }
                        $chunks = array_chunk($insertData,5000);
                    foreach ($chunks as $chunk)
                    {
                        RealisasiPenjualan::insert($chunk);
                    }

                    $sumRealPenjualan = RealisasiPenjualan::where('kode_opco', $selectedOpco)
                        ->where(DB::raw("to_char(tgl_spj, 'YYYY-MM')"), $tanggalSPJMont) // Tgl  sesuai tgl SPJ
                        ->where('item_no', 'like', '121-2%')
                        ->select('kode_opco', DB::raw('SUM(kwantumx) as clinker_sold'))
                        ->groupBy('kode_opco')
                        ->first();

                    $clinkerSold = data_get($sumRealPenjualan, 'clinker_sold', 0);

                        $sumICSPenjualan = RealisasiPenjualan::where('t_realisasi_penjualan.kode_opco', $selectedOpco)
                    ->join('m_plant_ics', function($join)
                    {
                        $join->on('m_plant_ics.plant', '=', 't_realisasi_penjualan.kode_da');
                    })
                    ->where(DB::raw("to_char(tgl_spj, 'YYYY-MM')"), $tanggalSPJMont) // Tgl  sesuai tgl SPJ
                    ->whereNotNull("m_plant_ics.kode_opco") // Kode Opco yang ada isinya.
                    ->select('t_realisasi_penjualan.kode_opco', DB::raw('SUM(kwantumx) as ics_sold'))
                    ->groupBy('t_realisasi_penjualan.kode_opco')
                    ->first();

                    $icsSold = data_get($sumICSPenjualan, 'ics_sold', 0);

                    $lastRealProd = RealisasiProduksi::where('kode_opco', $selectedOpco)->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $tanggalSPJMont)->first();
                    if (!$lastRealProd && $selectedOpco) {
                        RealisasiProduksi::create([
                            'tanggal' => $tanggalSPJ,
                            'prod_klinker' => 0,
                            'prod_semen' => 0,
                            'clinker_sold' => $clinkerSold,
                            'prod_output' => 0,
                                'ics' => $icsSold,
                            'kode_opco' => $selectedOpco
                        ]);
                    } elseif ($lastRealProd) {
                        $lastRealProd->update(['clinker_sold' => $clinkerSold, 'ics' => $icsSold, 'tanggal' => $tanggalSPJ]);
                        }
                    }
                }
            }
            if($isTrx)
            DB::commit();
        } catch (Exception $e) {
            if($isTrx)
            DB::rollBack();
            throw new Exception($e->getMessage());
        }
    }

    public function execute($id, $type, $date=null,$config = null, $status = false)
    {
        if (!$config) {
            // echo "<br>";var_dump($id,$type);
            $config = SyncSapConfig::where(['id' => $id, 'type' => $type, 'status' => 'active'])->first();
            //Auto Looping Per Company Penjualan.
            if($config){
                if($config->type=='penjualan'){
                    $opco = null;
                    //Untuk Testing Per Opco
                    if($date && array_key_exists('opco',$date))
                        $opco = $date['opco'];
                    if($opco){
                        return $this->getData($config,false,$date,$opco);
                    }
                    //End Testing Per Opco
                    $opcoModel = Opco::select('reference_sap');
                    // Khusus SIG Non SBI looping all company
                    if($config->kode_opco != 'SBI')
                    $opcoModel = $opcoModel->whereNotIn('kode_opco',['SI2000','SBI'])->get();
                    else 
                    $opcoModel = $opcoModel->where('kode_opco','SBI')->get();
                    $arr_return = [];
                    foreach($opcoModel as $opco){
                            $arr_return[]=  $this->getData($config,false,$date,$opco->reference_sap);
                    }
                    return $arr_return;
                    }
                return $this->getData($config,false,$date);
            }
           
            return [
                'status' => 'fail',
                'message' => 'Fail ID & Type',
            ];
        }

        return $this->getData($config, $status,$date);
    }

    private function sotrDate($arr_lastDate){
        // $arr_lastDate[] =  (object) array('last_date' => $lastDate);
        if(count($arr_lastDate)>0){
        $lastDate = collect($arr_lastDate)->sortByDesc(function ($item) {
            return strtotime($item->last_date);
            })->first()->last_date;
        return $lastDate;
        }else
        return '';

    }

    private function inMillion($nominal=null,$digit = 0){
        if($nominal && $nominal > 0){
            $nominal = round($nominal/1000000,$digit);
        }else
            $nominal = 0;
        return $nominal;
    }

    private function deleteFirst($config = null,$plants=null){
        if($config){
            $from = $config->dateParam['ta'];
            $to = $config->dateParam['tr'];
            $opcoModel = Opco::select('kode_opco','reference_sap')->where('kode_opco','<>','SI2000')->get();
            if($config->type=='penjualan'){
                $opco = $opcoModel->where('reference_sap', $config->opco)->first();
                $selectedOpco = data_get($opco, 'kode_opco', null);
                // dd($selectedOpco,$from,$to);
                if($selectedOpco){
                    RealisasiPenjualan::whereBetween('tgl_spj', [$from, $to])->where('kode_opco','=',$selectedOpco)->delete();
                    return true;
                }
                else 
                return false;
            }else if($config->type=='produksi'){
                if($plants){
                    DetailProduksi::whereBetween('tanggal', [$from, $to])->whereIn('kode_plant', $plants)->delete();
                    return true;
                }
                else 
                return false;
            }else if($config->type=='biaya'){
                $a= DetailBiaya::whereBetween('tanggal', [$from, $to])->delete();
                if($a>=0)
                return true;
                else 
                return false;
            }
        }
        return false;
    }
}
