<?php

namespace App\Http\Controllers;

use App\Models\Opco;
use Yajra\DataTables\DataTables;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\CostCenter;
use Illuminate\Support\Facades\DB;

use Storage;
use App\Rules\ExcelRule;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportTemplateCostCenter;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;

class CostCenterController extends Controller
{
  public function index()
  {
    $data = [
            'title' => 'Cost Center',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'/master-costcenter',
                ],
                [
                    'title'=>'Cost Center',
                    'url'=>'',
                ]
            ],
        ];
    $data['menus'] = $this->getDashboardMenu();
    $data['menu']  = Menu::select('id', 'name')->get();
    $data['opco']    = Opco::orderBy('no_pbi')->get();

    return view('costCenter', $data);
  }

  public function datatables(Request $request)
  {
    $query    = CostCenter::orderBy('cost_center')->get();
    $data     = DataTables::of($query)->make(true);
    $response = $data->getData(true);

    return response()->json($response, 200, [], JSON_PRETTY_PRINT);
  }

  /**
   * Store a newly created resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @return \Illuminate\Http\Response
   */
  public function store(Request $request)
  {
    $rules = [
      'kode_opco'     => 'required',
      'cost_center'     => 'required|unique:m_cost_center',
      'cost_center_name'       => 'required',
    ];
    $messages = [
      'required' => trans('messages.required'),
      'unique'   => trans('messages.unique'),
    ];
    $this->validate($request, $rules, $messages);
    try {
      $costCenter = CostCenter::create([
        'kode_opco'     => $request->kode_opco,
        'cost_center'     => $request->cost_center,
        'cost_center_name'       => $request->cost_center_name,
      ]);
      $response = responseSuccess(trans('messages.create-success'), $costCenter);
      return response()->json($response, 201);
    } catch (Exception $e) {
      $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
      return response()->json($response, 500);
    }
  }

  /**
   * Display the specified resource.
   *
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function show($cost_center)
  {
    $query   = CostCenter::select('cost_center', 'cost_center_name', 'm_opco.kode_opco')->leftjoin('m_opco', 'm_opco.reference_sap','m_cost_center.kode_opco')->find($cost_center);
    $response = responseSuccess(trans('messages.read-success'), $query);
    return response()->json($response, 200);
  }

  /**
   * Update the specified resource in storage.
   *
   * @param  \Illuminate\Http\Request  $request
   * @param  int  $id
   * @return \Illuminate\Http\Response
   */
  public function update($id, Request $request)
  {
    $data = $this->findDataWhere(CostCenter::class, ['cost_center' => $id]);

    $rules = [
      'kode_opco'     => 'required',
      'cost_center'     => 'required',
      'cost_center_name'       => 'required',
    ];
    $messages = [
      'required' => trans('messages.required'),
      'unique'   => trans('messages.unique'),
    ];
    $this->validate($request, $rules, $messages);

    DB::beginTransaction();
    try {
      $data->update([
        'kode_opco'     => $request->kode_opco,
        'cost_center'     => $request->cost_center,
        'cost_center_name'       => $request->cost_center_name,
      ]);
      DB::commit();
      $response = responseSuccess(trans("messages.update-success"), $data);
      return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    } catch (Exception $e) {
      DB::rollback();
      $response = responseFail(trans("messages.update-fail"), $e->getMessage());
      return response()->json($response, 500, [], JSON_PRETTY_PRINT);
    }
  }

  public function destroy($id)
  {
    CostCenter::destroy($id);
    $response = responseSuccess(trans('messages.delete-success'));
    return response()->json($response, 200);
  }

  public function temp()
  {
    return Excel::download(new ExportTemplateCostCenter, 'Template Master Data Cost Center.xlsx');
  }

  public function upload(Request $request)
  {
    $request->validate([
      'excel' => ['required', new ExcelRule($request->file('excel'))],
    ]);
    $filename = $request->file('excel')->hashName();

    Storage::disk('public')->put('usertemplate', $request->file('excel'));

    $path = storage_path('app/public/usertemplate/' . $filename);

    $reader = new Xlsx();
    $spreadsheet = $reader->load($path);

    $excel = $spreadsheet->getActiveSheet()->toArray();
    unset($excel[0]);

    foreach ($excel as $key => $data) {
      $costcenterExist   = CostCenter::find($data[0]);
        if (!$costcenterExist) {
          $costCenter = new CostCenter();
          $costCenter->cost_center = $data[0];
          $costCenter->cost_center_name = $data[1];
          $costCenter->kode_opco = strtoupper($data[2]);
          $costCenter->save();
        } else {
          $update = DB::table('m_cost_center')
            ->where("cost_center", $data[0])
            ->update([
              'cost_center' => $data[0],
              'cost_center_name' => $data[1],
              'kode_opco' => strtoupper($data[2])
            ]);
        }
    }
      return redirect('master-costcenter');
  }
}
