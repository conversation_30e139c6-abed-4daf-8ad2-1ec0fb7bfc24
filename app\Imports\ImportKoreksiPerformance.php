<?php

namespace App\Imports;

use App\Models\RKAPCapex;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

class ImportKoreksiPerformance implements ToCollection, WithHeadingRow
{
    public $data;

    public function collection(Collection $rows)
    {
        $data = [];
        foreach($rows as $row){
                if(is_int($row['tanggal'])){
                    // kondisi data jika tanggal format exel
                    $row['tanggal'] = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row['tanggal'])->format('Y-m-d');
                }else{
                    // kondisi data jika tanggal format database atau string
                    $row['tanggal'] = \Carbon\Carbon::createFromFormat('d/m/Y', $row['tanggal'])->format('Y-m-d');
                }
                $data[] = $row;

        }

        $this->data = collect($data);
    }

    public function rules(): array
    {
        return[
            'kode_plant'  => ['required', 'string'],
            'tanggal'  => ['required'],
            'oph' => ['required', 'numeric'],
            'updt' => ['required', 'numeric'],
            'pdt' => ['required', 'numeric'],
            'stop_idle' => ['required', 'numeric'],
            'fy_stop' => ['required', 'numeric'],
            'frek_updt' => ['required', 'numeric'],
            'rkap_oph' => ['required', 'numeric'],
            'rkap_updt' => ['required', 'numeric'],
            'rkap_pdt' => ['required', 'numeric'],
            'rkap_stop_idle' => ['required', 'numeric'],
            'rkap_frek_updt' => ['required', 'numeric'],
            'cal' => ['required', 'numeric'],
            'bdp_rate' => ['required', 'numeric'],
            'rkap_prod_rate' => ['required', 'numeric'],
            'koreksi' => ['required', 'numeric'],
            'act_prod' => ['required', 'numeric'],
            'rkap_prod' => ['required', 'numeric']
        ];
    }
}
