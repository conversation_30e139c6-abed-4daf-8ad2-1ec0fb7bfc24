<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMReasonStop extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_reason_stop', function (Blueprint $table) {
            $table->bigIncrements('id_kategori');
            $table->string('nama_kategori', 100)->unique();
            $table->string('create_by')->nullable();
            $table->string('update_by')->nullable();
            $table->string('deleted_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        DB::table('m_reason_stop')->insert([
            ['id_kategori' => '1', 'nama_kategori' => 'MEKANIKAL'],
            ['id_kategori' => '2', 'nama_kategori' => 'PROSES'],
            ['id_kategori' => '3', 'nama_kategori' => 'ELINST(ELEKTRICAL INSTRUMENT)']
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('m_reason_stop');
    }
}
