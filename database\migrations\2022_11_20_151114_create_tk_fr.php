<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTkFr extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tk_fr', function (Blueprint $table) {
            $table->bigIncrements('id_koreksi_fr');
            $table->integer('no', false,false)->length(4)->nullable();
            $table->string('holding', 10);
            $table->string('kode_opco', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->string('THN', 2);
            $table->decimal('rkap_bahan_bakar',20, 2)->nullable();
            $table->decimal('rkap_bahan_baku',20, 2)->nullable();
            $table->decimal('rkap_listrik',20, 2)->nullable();
            $table->decimal('rkap_tenaga_kerja',20, 2)->nullable();
            $table->decimal('rkap_pemeliharaan',20, 2)->nullable();
            $table->decimal('rkap_penyusutan',20, 2)->nullable();
            $table->decimal('rkap_administrasi_umum',20, 2)->nullable();
            $table->decimal('rkap_pajak_asuransi',20, 2)->nullable();
            $table->decimal('rkap_elim_bb',20, 2)->nullable();
            $table->decimal('rkap_elim_penyusutan',20, 2)->nullable();
            $table->decimal('rkap_elim_administrasi',20, 2)->nullable();
            $table->decimal('rkap_prod_klinker',20, 2)->nullable();
            $table->decimal('rkap_prod_semen',20, 2)->nullable();
            $table->decimal('rkap_clinker_sold',20, 2)->nullable();
            $table->decimal('rkap_prod_output',20, 2)->nullable();
            $table->decimal('bahan_bakar',20, 2)->nullable();
            $table->decimal('bahan_baku',20, 2)->nullable();
            $table->decimal('listrik',20, 2)->nullable();
            $table->decimal('tenaga_kerja',20, 2)->nullable();
            $table->decimal('pemeliharaan',20, 2)->nullable();
            $table->decimal('penyusutan',20, 2)->nullable();
            $table->decimal('administrasi_umum',20, 2)->nullable();
            $table->decimal('pajak_asuransi',20, 2)->nullable();
            $table->decimal('elim_bb',20, 2)->nullable();
            $table->decimal('elim_penyusutan',20, 2)->nullable();
            $table->decimal('elim_administrasi',20, 2)->nullable();
            $table->decimal('prod_klinker',20, 2)->nullable();
            $table->decimal('prod_semen',20, 2)->nullable();
            $table->decimal('clinker_sold',20, 2)->nullable();
            $table->decimal('prod_output',20, 2)->nullable();
            $table->decimal('ics',20, 2)->nullable();
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->index(['kode_opco', 'tanggal']);

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tk_fr');
    }
}
