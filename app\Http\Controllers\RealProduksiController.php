<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use App\Models\RealisasiProduksi;
use App\Models\Opco;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\RealProduksiExport;

class RealProduksiController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Realisasi Produksi',
            'breadcrumb' => [
                [
                    'title'=>'Data Realisasi',
                    'url'=>'/real-produksi',
                ],
                [
                    'title'=>'Realisasi Produksi',
                    'url'=>'',
                ]
            ],
        ];
        return view('realProduksi', $data);
    }

    public function getDatatables(Request $request)
    {
        $realCost = RealisasiProduksi::select(['id_realisasi_produksi','kode_opco', 'kode_plant',
        'prod_klinker','prod_semen','clinker_sold','prod_output','ics', 
        DB::raw("to_char(tanggal,'DD Month YYYY') as tanggal"),
        DB::raw("extract(month from tanggal) as bulan"),
        DB::raw("extract(year from tanggal) as tahun"),
        DB::raw("to_char(prod_klinker, '999G999G999G999G990D99') as new_prod_klinker"),
        DB::raw("to_char(prod_semen, '999G999G999G999G990D99') as new_prod_semen"),
        DB::raw("to_char(clinker_sold, '999G999G999G999G990D99') as new_clinker_sold"),
        DB::raw("to_char(prod_output, '999G999G999G999G990D99') as new_prod_output")]);
        if($request->filter_opco){
            $realCost = $realCost -> where('kode_opco', $request->filter_opco);
        }
        if($request->filter_tahun){
            $realCost = $realCost -> where(DB::raw("extract(year from tanggal)"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $realCost = $realCost -> where(DB::raw("extract(month from tanggal)"), $request->filter_bulan);
        }
        if($request->filter_search){
            $filter = $request->filter_search;
            $realCost = $realCost -> where('kode_opco', 'ilike','%'.$filter.'%')
            -> orWhere('tanggal', 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("extract(year from tanggal)"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("extract(month from tanggal)"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(prod_klinker, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(prod_semen, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(clinker_sold, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(prod_output, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%');
        }
        $data     = DataTables::of($realCost)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function getFilterRealProduksi()
    {
        //filter opco
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();

        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow; $i++) { 
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];  
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $bulan = [];
        for ($i=0; $i < count($months); $i++) { 
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => true
                ];
            } 
            else{
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => false
                ];
            }       
        }

        $data = [
            'kode_opco' => $opco,
            'tahun'     => $tahun,
            'bulan'     => $bulan
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function export(Request $request)
    {
        // Expot data with Collection
        $data = RealisasiProduksi::select([
            'kode_opco',
            'tanggal',
            'prod_klinker',
            'prod_semen',
            'clinker_sold',
            'prod_output',
            'ics',
        ]);
        if($request->filter_opco){
            $data = $data -> where('kode_opco', $request->filter_opco);
        }
        if($request->filter_tahun){
            $data = $data -> where(DB::raw("extract(year from tanggal)"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $data = $data -> where(DB::raw("extract(month from tanggal)"), $request->filter_bulan);
        }
        $data = $data->orderBy('id_realisasi_produksi')->get();
        return Excel::download(new RealProduksiExport($data), 'Realisasi Produksi.xlsx');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\RealProduksiController  $realProduksiController
     * @return \Illuminate\Http\Response
     */
    public function show(RealProduksiController $realProduksiController)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\RealProduksiController  $realProduksiController
     * @return \Illuminate\Http\Response
     */
    public function edit(RealProduksiController $realProduksiController)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\RealProduksiController  $realProduksiController
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, RealProduksiController $realProduksiController)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\RealProduksiController  $realProduksiController
     * @return \Illuminate\Http\Response
     */
    public function destroy(RealProduksiController $realProduksiController)
    {
        //
    }
}
