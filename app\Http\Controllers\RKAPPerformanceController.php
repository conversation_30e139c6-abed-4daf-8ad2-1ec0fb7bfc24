<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\RKAPPerformance;
use App\Models\Opco;
use App\Models\Plant;
use App\Models\KilnPlant;
use App\Models\Parameter;
use DataTables;
use Illuminate\Support\Facades\DB;

use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportTempRKAPPerformance;
use App\Imports\ImportRKAPPerformance;

class RKAPPerformanceController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'RKAP Performance',
            'breadcrumb' => [
                [
                    'title'=>'Input RKAP',
                    'url'=>'/rkap-performance',
                ],
                [
                    'title'=>'RKAP Performance',
                    'url'=>'',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i = 2019; $i <= $yearNow+1; $i++) {
            if ($i == $yearNow) {
                $tahun[] = [
                    'tahun' => $i,
                    'selected' => true
                ];
            } else {
                $tahun[] = [
                    'tahun' => $i,
                    'selected' => false
                ];
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);
        $optTahun = "";
        foreach ($tahun as $value) {
            $tahun = $value['tahun'];
            $selected = $value['selected'];
            if ($selected == true) {
                $optTahun .= "<option value=$tahun selected>$tahun</option>";
            } else {
                $optTahun .= "<option value=$tahun>$tahun</option>";
            }
        }
        $data['tahun'] = $optTahun;
        return view('rkapPerformance', $data);
    }

    public function viewImport()
    {
        $data = [
            'title' => 'RKAP Performance',
            'breadcrumb' => [
                [
                    'title'=>'Input RKAP',
                    'url'=>'/rkap-performance',
                ],
                [
                    'title'=>'RKAP Performance',
                    'url'=>'/rkap-performance/view-import',
                ],
                [
                    'title'=>'Import Data',
                    'url'=>'',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $kilnPlant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
        $optPlant = "";
        foreach ($kilnPlant as $value) {
            $kodePlant = $value['kode_plant'];
            $namePlant = $value['name_plant'];
            $optPlant .= "<option value='$kodePlant'>$namePlant</option>";
        }
        $data['plant'] = $optPlant;
        $param = Parameter::select('id_parameter')->orderBy('id')->get()->toArray();
        $optParam = "";
        foreach ($param as $value) {
            $temp = $value['id_parameter'];
            $optParam .= "<option value='$temp'>$temp</option>";
        }
        $data['parameter'] = $optParam;
        return view('rkapPerformanceImport', $data);
    }

    public function datatables(Request $request)
    {
        $rkap = RKAPPerformance::select([
            'id_rkap_perfom', 'm_opco.nama_opco', 'm_kiln_plant.name_plant', 'm_parameter.nama_parameter',
            'tahun', 'bulan', 'nilai_rkap', DB::raw("to_char(nilai_rkap, '999G999G999G999G990D99') as new_nilai_rkap")
        ])
            ->join('m_kiln_plant', 'rkap_performance.kode_plant', '=', 'm_kiln_plant.kode_plant')
            ->join('m_opco', 'm_kiln_plant.kode_opco', '=', 'm_opco.kode_opco')
            ->join('m_parameter', 'rkap_performance.id_parameter', '=', 'm_parameter.id_parameter');
        if ($request->filter_opco) {
            $rkap = $rkap->where('m_opco.kode_opco', $request->filter_opco);
        }
        if ($request->filter_plant) {
            $rkap = $rkap->where('m_kiln_plant.name_plant', $request->filter_plant);
        }
        if ($request->filter_tahun) {
            $rkap = $rkap->where('tahun', $request->filter_tahun);
        }
        if ($request->filter_search) {
            $filter = $request->filter_search;
            $rkap = $rkap->where('m_parameter.nama_parameter', 'ilike', '%' . $filter . '%')
                ->orWhere('m_kiln_plant.name_plant', 'ilike', '%' . $filter . '%')
                ->orWhere('tahun', 'ilike', '%' . $filter . '%')
                ->orWhere('nilai_rkap', 'ilike', '%' . $filter . '%')
                ->orWhere(DB::raw("to_char(nilai_rkap, '999G999G999G999G990D99')"), 'ilike', '%' . $filter . '%');
        }
        $rkap = $rkap->orderBy('id_rkap_perfom')->get()->toArray();
        if ($rkap == []) {
            $data     = DataTables::of($rkap)->make(true);
            $response = $data->getData(true);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        }
        $result = [];
        $temp = [];
        $bulan = 1;
        foreach ($rkap as $item) {
            $temp['id_rkap_perfom'] =  $item['id_rkap_perfom'];
            $temp['name_plant'] =  $item['name_plant'];
            $temp['nama_parameter'] =  $item['nama_parameter'];
            $temp['tahun'] =  $item['tahun'];
            $temp[$bulan] = $item['new_nilai_rkap'];
            if (count($temp) == 16) { //kode_plant, parameter, tahun, bulan (12) 
                array_push($result, $temp);
                $temp = [];
                $bulan = 1;
            }
            $bulan++;
        }
        $data     = DataTables::of($result)
            ->addIndexColumn()
            ->addColumn('action', function ($row) {
                $btn = '<button type="button" class="deletes btn btn-sm btn-icon btn-outline-danger" title="Delete" data-toggle="tooltip" data-id="' . $row['id_rkap_perfom'] . '" ><i class="fa fa-trash"></i></button>';
                return $btn;
            })
            ->rawColumns(['action'])
            ->make(true);
        $response = $data->getData(true);

        $plant = $response['data'][0]['name_plant'];
        $color = ['#F5F5F5', '#FFFFFF'];
        $index = 0;
        for ($i = 0; $i < count($response['data']); $i++) {
            if ($i == 0 and $plant == $response['data'][$i]['name_plant']) {
                $response['data'][$i]['name_plant'] = $response['data'][$i]['name_plant'];
                $response['data'][$i]['action'] = $response['data'][$i]['action'];
                $response['data'][$i]['color'] = $color[$index];
            } else if ($plant == $response['data'][$i]['name_plant']) {
                $response['data'][$i]['name_plant'] = "";
                $response['data'][$i]['action'] = "";
                $response['data'][$i]['color'] = $color[$index];
            } else {
                $plant = $response['data'][$i]['name_plant'];
                if ($index == 0) {
                    $index = 1;
                } else {
                    $index = 0;
                }
                $response['data'][$i]['color'] = $color[$index];
            }  
        }
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function filter()
    {
        $opco = Opco::select(['kode_opco', 'nama_opco'])->where('kode_opco', '!=', 'SI2000')->orderBy('id')->get()->toArray();
        $kilnPlant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i = 2019; $i <= $yearNow; $i++) {
            if ($i == $yearNow) {
                $tahun[] = [
                    'tahun' => $i,
                    'selected' => true
                ];
            } else {
                $tahun[] = [
                    'tahun' => $i,
                    'selected' => false
                ];
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);
        $data = [
            'opco' => $opco,
            'kilnPlant' => $kilnPlant,
            'tahun' => $tahun
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function getPlant(Request $request)
    {
        $id = $request->query('id');
        if ($id) {
            $kilnPlant = KilnPlant::select(['kode_plant', 'name_plant'])->where('kode_opco', $id)->orderBy('id_kiln_plant')->get()->toArray();
            $data = ['plant' => $kilnPlant];
            return response()->json($data, 200, [], JSON_PRETTY_PRINT);
        }
        $kilnPlant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
        $data = ['plant' => $kilnPlant];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function temp(Request $request)
    {
        $tahun = $request->year;
        $fileName = 'Template RKAP Performance ' . $tahun . '.xlsx';
        return Excel::download(new ExportTempRKAPPErformance($tahun), $fileName);
    }

    public function import(Request $request)
    {
        // validasi
        $this->validate($request, [
            'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author 
        ]);

        // menangkap file excel
        $file = $request->file('excel');

        // import data
        $import = new ImportRKAPPerformance;
        Excel::import($import, $file);
        $datas = ($import->data)->toArray();

        //make parametr for mapping data
        $plants = KilnPlant::select('kode_plant as plant')->orderBy('no_pbi')->get()->toArray();
        $parameters = Parameter::select('id_parameter')->orderBy('id')->get()->toArray();
        $listPlant = [];
        $listParam = [];
        foreach ($plants as $value) {
            array_push($listPlant, $value['plant']);
        }
        foreach ($parameters as $value) {
            array_push($listParam, $value['id_parameter']);
        }
        //mapping data
        $map = [];
        foreach ($listPlant as $plant) {
            foreach ($listParam as $param) {
                foreach ($datas as $data) {
                    if ($data['plant'] == $plant and $data['parameter'] == $param) {
                        array_push($map, $data);
                    }
                }
            }
        }
        //list plant
        $plant = KilnPlant::select('kode_plant as plant')->get()->toArray();
        $arrPlant = [];
        foreach ($plant as $value) {
            array_push($arrPlant, $value['plant']);
        }
        //list plant
        $parameter = Parameter::select('id_parameter')->get()->toArray();
        $arrParameter = [];
        foreach ($parameter as $value) {
            array_push($arrParameter, $value['id_parameter']);
        }
        $result = [];
        $success = true;
        foreach ($map as $data) {
            $format = $data;
            $message = "";
            $status = "Valid";
            if ($data['plant'] == NULL) {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom plant tidak boleh kosong";
                }
            } else if (gettype($data['plant']) != 'string') {
                $status = "Invalid";
                $success = false;
                $message = $message . "Kolom plant hanya berisi huruf";
            } else if (!in_array($data['plant'], $arrPlant)) {
                $status = "Invalid";
                $success = false;
                $message = $message . "Kolom plant tidak ditemukan";
            }
            if ($data['parameter'] == NULL) {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom parameter tidak boleh kosong";
                } else {
                    $message = $message . ", Kolom parameter tidak boleh kosong";
                }
            } else if (gettype($data['parameter']) != 'string') {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom parameter hanya berisi huruf";
                } else {
                    $message = $message . ", Kolom parameter hanya berisi huruf";
                }
            } else if (!in_array($data['parameter'], $arrParameter)) {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom parameter tidak ditemukan";
                } else {
                    $message = $message . ", Kolom parameter tidak ditemukan";
                }
            }
            if ($data['year'] == NULL) {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom tahun tidak ditemukan";
                } else {
                    $message = $message . ", Kolom tahun tidak ditemukan";
                }
            } else if (gettype($data['year']) != 'integer') {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom tahun hanya berisi angka";
                } else {
                    $message = $message . ", Kolom tahun hanya berisi angka";
                }
            }
            $listMonth = ['january', 'february', 'march', 'april', 'mei', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];
            foreach ($listMonth as $value) {
                if ($data[$value] == null) {
                    $format[$value] = 0;
                } else if (gettype($data[$value]) != 'float' and gettype($data[$value]) != 'integer' and gettype($data[$value]) != 'double') {
                    $status = "Invalid";
                    $success = false;
                    if ($message == "") {
                        $message = $message . "Kolom nilai rkap bulan " . $value . " hanya berisi angka";
                    } else {
                        $message = $message . ", Kolom nilai rkap bulan " . $value . " hanya berisi angka";
                    }
                }
            }
            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        // validation input success next to validation value
        if ($success) {
            $allData = $result;
            $loop =  count($allData) / 9;
            $finalData = [];
            for ($i = 0; $i < $loop; $i++) {
                $validation = array_slice($allData, $i * 9,  9);
                $yearNow = ($validation[0]['year']);
                //make a temp month rkap
                $months = ['january', 'february', 'march', 'april', 'mei', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];
                $index = 1;
                foreach ($months as $value) {
                    $temp = [];
                    $temp = array_column($validation, $value);
                    ${$value} = array_slice($temp, 0, 4);
                    $fyStop = array_slice($temp, 8,8);
                    $oph = ${$value}; // oph * 24
                    $oph[0] = $oph[0] * 24; // oph * 24
                    $sum = round(array_sum($oph),0);
                    $max = $fyStop[0] == 0 ? cal_days_in_month(CAL_GREGORIAN, $index, $yearNow) * 24 : 0;
                    ${$value}[] = $sum;
                    ${$value}[] = $max;
                    if ($max == $sum) {
                        ${$value}[] = 'Valid';
                    } else {
                        ${$value}[] = 'Invalid';
                    }
                    ${$value} = array_merge(${$value}, array_slice($temp, 4));
                    $index++;
                }
                $plant = array_column($validation, 'plant');
                array_splice($plant, 4, 0, ['Total (Hour)', 'Max (Hour)', 'Status']);

                $parameter = array_column($validation, 'parameter');
                array_splice($parameter, 4, 0, ['', '', '']);

                $year = array_column($validation, 'year');
                array_splice($year, 4, 0, ['', '', '']);

                $data = [];
                for ($j = 0; $j < count($plant); $j++) {
                    $temp = [];
                    $temp['plant']      = $plant[$j];
                    $temp['parameter']  = $parameter[$j];
                    $temp['year']       = $year[$j];
                    $temp['january']    = $january[$j];
                    $temp['february']   = $february[$j];
                    $temp['march']      = $march[$j];
                    $temp['april']      = $april[$j];
                    $temp['mei']        = $mei[$j];
                    $temp['june']       = $june[$j];
                    $temp['july']       = $july[$j];
                    $temp['august']     = $august[$j];
                    $temp['september']  = $september[$j];
                    $temp['october']    = $october[$j];
                    $temp['november']   = $november[$j];
                    $temp['december']   = $december[$j];
                    $temp['is_valid']   = "Valid";
                    $temp['note']      = "";
                    array_push($data, $temp);
                }
                $finalData[$i] = $data;
            }
            $final = [];
            foreach ($finalData as $value) {
                $final = array_merge($final, $value);
            }
            $data     = DataTables::of($final)->make(true);
            $response = $data->getData(true);
            $plant = $response['data'][0]['plant'];
            $color = ['#F5F5F5', '#FFFFFF'];
            $addition = ['Total (Hour)', 'Max (Hour)', 'Status'];
            $index = 0;
            for ($i = 0; $i < count($response['data']); $i++) {
                if ($i == 0 and $plant == $response['data'][$i]['plant']) {
                    $response['data'][$i]['color'] = $color[$index];
                } else if ($plant == $response['data'][$i]['plant']) {
                    $response['data'][$i]['color'] = $color[$index];
                } else if (in_array($response['data'][$i]['plant'], $addition)) {
                    $response['data'][$i]['color'] = $color[$index];
                } else {
                    $plant = $response['data'][$i]['plant'];
                    if ($index == 0) {
                        $index = 1;
                    } else {
                        $index = 0;
                    }
                    $response['data'][$i]['color'] = $color[$index];
                }
            }
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        }

        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        $plant = $response['data'][0]['plant'];
        $color = ['#F5F5F5', '#FFFFFF'];
        $index = 0;
        for ($i = 0; $i < count($response['data']); $i++) {
            if ($i == 0 and $plant == $response['data'][$i]['plant']) {
                $response['data'][$i]['color'] = $color[$index];
            } else if ($plant == $response['data'][$i]['plant']) {
                $response['data'][$i]['color'] = $color[$index];
            } else {
                $plant = $response['data'][$i]['plant'];
                if ($index == 0) {
                    $index = 1;
                } else {
                    $index = 0;
                }
                $response['data'][$i]['color'] = $color[$index];
            }
        }
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function insertData(Request $request)
    {
        $excel = json_decode($request->excel);
        for ($i = 0; $i < count($excel); $i++) {
            if ($excel[$i][0] == 'Total (Hour)') {
                array_splice($excel, $i, 3);
            }
        }
        //list plant
        $plant = KilnPlant::select('kode_plant as plant')->get()->toArray();
        $arrPlant = [];
        foreach ($plant as $value) {
            array_push($arrPlant, $value['plant']);
        }
        //list plant
        $parameter = Parameter::select('id_parameter')->get()->toArray();
        $arrParameter = [];
        foreach ($parameter as $value) {
            array_push($arrParameter, $value['id_parameter']);
        }
        $result = [];
        $success = true;
        foreach ($excel as $item) {
            $listCol = [
                'plant', 'parameter', 'year', 'january', 'february', 'march', 'april', 'mei',
                'june', 'july', 'august', 'september', 'october', 'november', 'december'
            ];
            $data = [];
            for ($i = 0; $i < count($listCol); $i++) {
                $data[$listCol[$i]] = $item[$i];
            }
            $format = $data;
            $message = "";
            $status = "Valid";
            if ($data['plant'] == NULL) {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom plant tidak boleh kosong";
                }
            } else if (gettype($data['plant']) != 'string') {
                $status = "Invalid";
                $success = false;
                $message = $message . "Kolom plant hanya berisi huruf";
            } else if (!in_array($data['plant'], $arrPlant)) {
                $status = "Invalid";
                $success = false;
                $message = $message . "Kolom plant tidak ditemukan";
            }
            if ($data['parameter'] == NULL) {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom parameter tidak boleh kosong";
                } else {
                    $message = $message . ", Kolom parameter tidak boleh kosong";
                }
            } else if (gettype($data['parameter']) != 'string') {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom parameter hanya berisi huruf";
                } else {
                    $message = $message . ", Kolom parameter hanya berisi huruf";
                }
            } else if (!in_array($data['parameter'], $arrParameter)) {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom parameter tidak ditemukan";
                } else {
                    $message = $message . ", Kolom parameter tidak ditemukan";
                }
            }
            if ($data['year'] == NULL) {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom tahun tidak ditemukan";
                } else {
                    $message = $message . ", Kolom tahun tidak ditemukan";
                }
            } else if (gettype($data['year']) != 'integer') {
                $status = "Invalid";
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom tahun hanya berisi angka";
                } else {
                    $message = $message . ", Kolom tahun hanya berisi angka";
                }
            }
            $listMonth = ['january', 'february', 'march', 'april', 'mei', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];
            foreach ($listMonth as $value) {
                if ($data[$value] == null) {
                    $format[$value] = 0;
                } else if (gettype($data[$value]) != 'float' and gettype($data[$value]) != 'integer' and gettype($data[$value]) != 'double') {
                    $status = "Invalid";
                    $success = false;
                    if ($message == "") {
                        $message = $message . "Kolom nilai rkap bulan " . $value . " hanya berisi angka";
                    } else {
                        $message = $message . ", Kolom nilai rkap bulan " . $value . " hanya berisi angka";
                    }
                }
            }
            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        // validation input success next to validation value
        if ($success) {
            $confirm = true;
            $allData = $result;
            $loop =  count($allData) / 9;
            $finalData = [];
            for ($i = 0; $i < $loop; $i++) {
                $validation = array_slice($allData, $i * 9,  9);
                $yearNow = ($validation[0]['year']);
                //make a temp month rkap
                $months = ['january', 'february', 'march', 'april', 'mei', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];
                $index = 1;
                foreach ($months as $value) {
                    $temp = [];
                    $temp = array_column($validation, $value);
                    ${$value} = array_slice($temp, 0, 4);
                    $oph = ${$value}; // oph * 24
                    $oph[0] = $oph[0] * 24; // oph * 24
                    $sum = array_sum($oph);
                    $max = cal_days_in_month(CAL_GREGORIAN, $index, $yearNow) * 24;
                    ${$value}[] = $sum;
                    ${$value}[] = $max;
                    if ($max == $sum) {
                        ${$value}[] = 'Valid';
                    } else {
                        ${$value}[] = 'Invalid';
                        $confirm = false;
                    }
                    ${$value} = array_merge(${$value}, array_slice($temp, 4));
                    $index++;
                }
                $plant = array_column($validation, 'plant');
                array_splice($plant, 4, 0, ['Total (Hour)', 'Max (Hour)', 'Status']);

                $parameter = array_column($validation, 'parameter');
                array_splice($parameter, 4, 0, ['', '', '']);

                $year = array_column($validation, 'year');
                array_splice($year, 4, 0, ['', '', '']);

                $data = [];
                for ($j = 0; $j < count($plant); $j++) {
                    $temp = [];
                    $temp['plant']      = $plant[$j];
                    $temp['parameter']  = $parameter[$j];
                    $temp['year']       = $year[$j];
                    $temp['january']    = $january[$j];
                    $temp['february']   = $february[$j];
                    $temp['march']      = $march[$j];
                    $temp['april']      = $april[$j];
                    $temp['mei']        = $mei[$j];
                    $temp['june']       = $june[$j];
                    $temp['july']       = $july[$j];
                    $temp['august']     = $august[$j];
                    $temp['september']  = $september[$j];
                    $temp['october']    = $october[$j];
                    $temp['november']   = $november[$j];
                    $temp['december']   = $december[$j];
                    $temp['is_valid']   = "Valid";
                    $temp['note']      = "";
                    array_push($data, $temp);
                }
                $finalData[$i] = $data;
            }
            $final = [];
            foreach ($finalData as $value) {
                $final = array_merge($final, $value);
            }
        } else {
            $data     = DataTables::of($result)->make(true);
            $response = $data->getData(true);
            $plant = $response['data'][0]['plant'];
            $color = ['#F5F5F5', '#FFFFFF'];
            $index = 0;
            for ($i = 0; $i < count($response['data']); $i++) {
                if ($i == 0 and $plant == $response['data'][$i]['plant']) {
                    $response['data'][$i]['color'] = $color[$index];
                } else if ($plant == $response['data'][$i]['plant']) {
                    $response['data'][$i]['color'] = $color[$index];
                } else {
                    $plant = $response['data'][$i]['plant'];
                    if ($index == 0) {
                        $index = 1;
                    } else {
                        $index = 0;
                    }
                    $response['data'][$i]['color'] = $color[$index];
                }
            }
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        }
        $data     = DataTables::of($final)->make(true);
        $response = $data->getData(true);
        $plant = $response['data'][0]['plant'];
        $color = ['#F5F5F5', '#FFFFFF'];
        $addition = ['Total (Hour)', 'Max (Hour)', 'Status'];
        $index = 0;
        for ($i = 0; $i < count($response['data']); $i++) {
            if ($i == 0 and $plant == $response['data'][$i]['plant']) {
                $response['data'][$i]['color'] = $color[$index];
            } else if ($plant == $response['data'][$i]['plant']) {
                $response['data'][$i]['color'] = $color[$index];
            } else if (in_array($response['data'][$i]['plant'], $addition)) {
                $response['data'][$i]['color'] = $color[$index];
            } else {
                $plant = $response['data'][$i]['plant'];
                if ($index == 0) {
                    $index = 1;
                } else {
                    $index = 0;
                }
                $response['data'][$i]['color'] = $color[$index];
            }
        }
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }


    public function saveData(Request $request)
    {


        $excel = json_decode($request->excel);


        for ($i = 0; $i < count($excel); $i++) {
            if ($excel[$i][0] == 'Total (Hour)') {
                array_splice($excel, $i, 3);
            }
        }
        $result = [];
        foreach ($excel as $item) {
            $listCol = ['plant', 'parameter', 'year', 'january', 'february', 'march', 'april', 'mei', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];
            $data = [];
            for ($i = 0; $i < count($listCol); $i++) {
                $data[$listCol[$i]] = $item[$i];
            }
            array_push($result, $data);
        }
        foreach ($result as $value) {
            $months = ['january', 'february', 'march', 'april', 'mei', 'june', 'july', 'august', 'september', 'october', 'november', 'december'];
            for ($i = 0; $i < count($months); $i++) {
                RKAPPerformance::updateOrCreate([
                    'kode_plant'    => $value['plant'],
                    'id_parameter'  => $value['parameter'],
                    'tahun'         => $value['year'],
                    'bulan'         => $i + 1,
                ], [
                    'kode_plant'    => $value['plant'],
                    'id_parameter'  => $value['parameter'],
                    'tahun'         => $value['year'],
                    'bulan'         => $i + 1,
                    'nilai_rkap'    => $value[$months[$i]]
                ]);
            }
        }

        $datas = DB::table('vw_rkap_performance')->where('tahun', $excel[0][2])->get();
        $data_view = [];
        $data_delete = [];

        foreach ($datas as $data) {
            $data_push = [];
            $data_push['kode_plant'] = $data->kode_plant;
            $data_push['bulan'] = $data->bulan;
            $data_push['oph'] = $data->oph;
            $data_push['updt'] = $data->updt;
            $data_push['pdt'] = $data->pdt;
            $data_push['stop_idle'] = $data->stop_idle;
            $data_push['frek_updt'] = $data->frek_updt;
            $data_push['cal'] = (int)$data->cal;
            $data_push['frek_updt'] = $data->frek_updt;
            $data_push['kode_opco'] = $data->kode_opco;
            $data_push['prod_vol'] = $data->prod_vol;
            $data_push['bdp_rate'] = $data->bdp_rate;
            $data_push['prod_rate'] = $data->prod_rate;
            $data_push['fy_stop'] = $data->fy_stop;
            $data_push['tahun'] = $data->tahun;

            array_push($data_view, $data_push);
            array_push($data_delete, $data_push['kode_plant']);
        }

        DB::table('ts_rkap_perfomance')->where('tahun', $excel[0][2])->delete();
      
            $data_insert = json_decode(json_encode($data_view), true);
            DB::table('ts_rkap_perfomance')->insert($data_insert);

            $response = responseSuccess('Data added successfully');
            return response()->json($response, 200);
        
    }

    public function destroy($id)
    {
        $plant = RKAPPerformance::select('kode_plant', 'tahun')->where('id_rkap_perfom', $id)->first();
        RKAPPerformance::where('kode_plant', $plant->kode_plant)->where('tahun', $plant->tahun)->delete();
        $response = responseSuccess("Data deleted successfully");
        return response()->json($response, 200);
    }

    public function monthRender($monthName, $data, $row, $column)
    {
        if ($data === 'Valid') {
            return "<span class='badge badge-pill badge-success font-weight-bolder'>$data</span>";
        } else if ($data === 'Invalid') {
            return "<span class='badge badge-pill badge-danger font-weight-bolder'> $data</span>";
        } else {
            $id = $monthName . '-' . $column;
            return "<span class='editors' style='display: none;'><input for-field='$monthName' for-row='$row' for-column='$column' class='form-control editable edit-number' type='text' id='$id' value='$data'></span><span class='originals inline-view' style='display: block;'>$data</span>";
        }
    }
}
