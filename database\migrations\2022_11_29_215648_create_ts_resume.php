<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTsResume extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ts_resume', function (Blueprint $table) {
            $table->bigIncrements('id_resume');
            $table->string('kode_opco', 30);
            $table->integer('no_plant');
            $table->string('kode_plant', 30);
            $table->timestamp('tanggal')->nullable()->default(NULL);
            $table->string('alasan', 255);
            $table->bigInteger('id_kategori')->nullable();
            $table->string('nama_kategori', 100)->nullable();
            $table->float('oph', 8, 5);
            $table->float('updt', 8, 5);
            $table->float('pdt', 8, 5);
            $table->float('stop_idle', 8, 5);
            $table->integer('total');
            $table->bigInteger('frek');
            $table->integer('sort')->nullable();
            $table->timestamps();
            $table->softDeletes();  
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ts_resume');
    }
}
