<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTKilnStopTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('t_kiln_stop', function (Blueprint $table) {
            $exists = function (string $column) use ($table) {
                return (Schema::hasColumn($table->getTable(), $column));
            };
            if(!$exists('is_sent'))
                $table->integer('is_sent')->default(0);
            if(!$exists('keterangan_sent'))
                $table->longText('keterangan_sent')->nullable();
        });     
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('t_kiln_stop', function (Blueprint $table) {
            $table->dropColumn(['is_sent', 'keterangan_sent']);
        });
    }
}
