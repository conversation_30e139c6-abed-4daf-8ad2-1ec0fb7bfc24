<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Illuminate\Support\Arr;

class ExportDataKoreksiPerformanceFr implements WithMultipleSheets
{
  use Exportable;

  public function __construct($data, $smi, $tahun)
  {
    $this->data = $data;
    $this->smi = $smi;
    $this->tahun = $tahun;
  }

  /**
   * @return array
   */
  public function sheets(): array
  {
    $sheets = [];
    $kategori = ['PROD', 'AVAIL', 'YIELD', 'OEE', 'RASIO'];
    $sheet_name = ['RBG&TLCC', 'SBI','TONASA','PADANG','BUOPSI', 'SMBR','SIG'];

    foreach ($sheet_name as $key => $name) {
      foreach ($kategori as $key => $kat) {
        if ($name == 'BUOPSI') {
          $dt[$kat] = $this->data[$kat]['GHOPO'];
        } else if ($name == 'PADANG') {
          $dt[$kat] = $this->data[$kat]['SP'];
        } else if ($name == 'TONASA') {
          $dt[$kat] = $this->data[$kat]['ST'];
        } else if ($name == 'SBI') {
          $dt[$kat] = $this->data[$kat]['SBI'];
        } else if ($name == 'SMBR') {
          $dt[$kat] = $this->data[$kat]['SMBR'];
        }else if ($name == 'RBG&TLCC') {
          $this->data[$kat]['SG'] = Arr::exists($this->data[$kat], 'SG') ? $this->data[$kat]['SG'] : array('SG' =>  array('kode_opco' => 'SEMEN GRESIK', ));
          $this->data[$kat]['TLCC'] = Arr::exists($this->data[$kat], 'TLCC') ? $this->data[$kat]['TLCC'] : array('TLCC' => array('kode_opco' => 'TLCC', ));
          $this->data[$kat]['RBG']['SG'] = $this->data[$kat]['SG']['SG'];
          $this->data[$kat]['RBG']['kode_opco'] = 'SEMEN GRESIK';
  
          $dt[$kat] = array('RBG' => $this->data[$kat]['RBG'], 'TLCC' => $this->data[$kat]['TLCC']);
        } 
        else if ($name == 'SIG') {
          $dt[$kat]['SIG'] = $this->smi[$kat]['SIG'];
          $dt['MTBF'] = $this->data['MTBF'];
          $dt['SIGMTBF'] = $this->smi['MTBF']['SIG']['SIG'];
          $dt['MTTR'] = $this->data['MTTR']; 
          $dt['SIGMTTR'] = $this->smi['MTTR']['SIG']['SIG'];
        }
      }
      $sheets[] = new ExportDataKoreksiPerformanceFrRBG($dt, $name, $this->tahun);
    }
    return $sheets;
  }
}
