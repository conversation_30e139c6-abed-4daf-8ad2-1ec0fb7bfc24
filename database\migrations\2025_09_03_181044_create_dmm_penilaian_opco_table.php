<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateDmmPenilaianOpcoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('dmm_penilaian_opco', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->uuid('uuid')->default(DB::raw('uuid_generate_v4()'));
            $table->bigInteger('company_id')->unsigned();
            $table->foreign('company_id')
                ->references('id')
                ->on('m_company');
            $table->bigInteger('periode_penilaian_id')->unsigned();
            $table->foreign('periode_penilaian_id')
                ->references('id')
                ->on('dmm_periode_penilaian');
            $table->date('start_date');
            $table->date('end_date');
            $table->char('status',1)->default('y');
            $table->uuid('created_by');
            $table->uuid('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('dmm_penilaian_opco');
    }
}
