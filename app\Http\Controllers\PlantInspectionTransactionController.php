<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use App\Models\RoleHasMenu;
use App\Models\PlantInspection;
use App\Models\PlantCatatan;
use App\Models\Plant;
use App\Models\Area;
use App\Models\KilnPlant;
use App\Models\ItemInspection;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ImportInspectionItemModel;
use App\Exports\ExportDataInspectionItem;
use App\Imports\CheckPlantInspection;

class PlantInspectionTransactionController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Plant Inspection Transaction',
            'breadcrumb' => [
                [
                    'title'=>'Equip Healthy',
                    'url'=>'/plant-Inspection-transaction',
                ],
                [
                    'title'=>'Plant Inspection',
                    'url'=>'',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        return view('plantInspection', $data);
    }

    public function detailPlantInspection(Request $request)
    {
        $data = [
            'title' => 'Detail Plant Inspection Transaction',
            'breadcrumb' => [
                [
                    'title'=>'Plant Inspection Transaction',
                    'url'=>'/plant-Inspection-transaction',
                ],
                [
                    'title'=>'Detail Plant Inspection Transaction',
                    'url'=>'',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        return view('detailPlantInspection', $data);
    }

    public function editPlantInspection(Request $request)
    {
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        return view('editPlantInspection', $data);
    }

    public function tambahPlantInspection(Request $request)
    {
        $data = [
            'title' => 'Tambah Plant Inspection Transaction',
            'breadcrumb' => [
                [
                    'title'=>'Plant Inspection Transaction',
                    'url'=>'/plant-Inspection-transaction',
                ],
                [
                    'title'=>'Detail Plant Inspection Transaction',
                    'url'=>'',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        return view('tambahPlantInspection', $data);
    }

    public function importInspectionItem(Request $request)
    {
        if ($request->no_inspection == "") {
            $last_no = PlantInspection::count('no_inspection');

            $last_no++;


            $no_inspection_number =  $last_no;


            $no_inspection = 'PINS/' . $request->opco . '/' . $no_inspection_number . '';
            $data = array(
                "desc_inspection" => $request->desc_inspection,
                "kode_opco" => $request->opco,
                "kode_plant" => $request->plant,
                "status" => 'Draft',
                "function" => $request->function,
                "no_inspection" => $no_inspection,
                "create_date" => $request->date_inspection_header,
                "create_time" => Carbon::now(),
                "delete_mark" => 0
            );
            PlantInspection::insert($data);
        } else {
            $no_inspection = $request->no_inspection;
            ItemInspection::where('no_inspection', $no_inspection)->delete();
        }

        Excel::import(new ImportInspectionItemModel($no_inspection), $request->file('excel_file'));

        return [
            'message' => 'Success Import Item Inspection',
            'status' => 'success',
            'no_inspection' => $no_inspection
        ];
    }

    public function checkImportExcel(Request $request)
    {
        $rows = Excel::toArray(new CheckPlantInspection, $request->file('excel_file'));



        foreach ($rows[0] as $row => $value) {
            if ($value[2] != "id_area" and $value[2] != null) {
                $id_area[] = $value[2];
            }
        }


        $distinct_area = array_unique($id_area);
        foreach ($distinct_area as $idArea) {
            $uniq_area[] =  $idArea;
        }

        $newLangsComma = implode(",", $uniq_area);

        $plant_inspection_transaction = PlantInspection::join('t_item_inspection', 't_item_inspection.no_inspection', '=', 't_plant_inspection.no_inspection')
            ->select('t_plant_inspection.no_inspection')
            ->where('status', 'On Review')
            ->whereIn('id_area', $uniq_area)
            ->where('function', $request->function)
            ->groupBy('t_plant_inspection.no_inspection')
            ->get();

        $area = PlantInspection::join('t_item_inspection', 't_item_inspection.no_inspection', '=', 't_plant_inspection.no_inspection')
            ->select('t_item_inspection.nm_area')
            ->where('status', 'On Review')
            ->whereIn('id_area', $uniq_area)
            ->where('function', $request->function)
            ->groupBy('t_item_inspection.nm_area')
            ->get();

        return [
            'message' => 'Success Get Data',
            'data' => $plant_inspection_transaction,
            'area' => $area
        ];
    }


    public function getExportInspectionData($id,$fungsi)
    {
        return Excel::download(new ExportDataInspectionItem($id,$fungsi), 'ExportDataInspectionItem-' . Carbon::now() . '.xlsx');
    }

    public function getAreaRelation(Request $request)
    {

        $data_area = Area::get();

        return [
            'message' => 'Succes Get Data Area',
            'data' => $data_area
        ];
    }

    public function getFilterOpco(Request $request)
    {
        $filter_opco = DB::table('m_opco')->select('kode_opco')->where('kode_opco', Auth::user()->company)->distinct('kode_opco')->get();
        $filter_plant = KilnPlant::select('name_plant')->where('kode_opco', Auth::user()->company)->get();

        $yearNow = now()->year;
        $yearNowPlus = $yearNow+1;
        $tahun = [];
        $minus = 0;
        for ($i = 2019; $i <= $yearNowPlus; $i++) {
          if ($i == $yearNow) {
            $tahun[] = [
              'tahun' => $i,
              'selected' => true
            ];
          } else {
            $tahun[] = [
              'tahun' => $i,
              'selected' => false
            ];
          }
          $minus++;
        }
        $tahun = array_reverse($tahun);
        foreach ($tahun as $value) {
          $tahun = $value['tahun'];
        //   $selected = $value['selected'];
          $optTahun[] = array('tahun' => $tahun);
        }

        return [
            'message' => 'Succes Get Data Filter Opco',
            'data' => $filter_opco,
            'data_plant' => $filter_plant,
            'tahun' => $optTahun
        ];
    }

    public function getDataInputItemInpection(Request $request)
    {
        if ($request->id_area == "empty" || $request->id_area == "") {
            $id_area = '1=1';
        } else {
            $id_area = 'id_area in ( ' . $request->id_area . ' )';
        }
        $area = Area::whereraw($id_area)->get();
        $condition = DB::table('m_kondisi')->get();
        $equipment = DB::table('m_equipment')->get();
        return [
            'message' => 'Succes Get Data',
            'data_condition' => $condition,
            'data_area' => $area,
            'data_equipment' => $equipment
        ];
    }


    public function addInspectionItem(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = array(
                "id_equipment" => $request->id_equipment,
                "desc_equipment" => $request->desc_equipment,
                "id_area" => $request->id_area,
                "nm_area" => $request->nm_area,
                "id_kondisi" => $request->id_kondisi,
                "no_inspection" => $request->no_inspection,
                "remark" => $request->remark,
                "create_date" => $request->date_inspection_header,
                "create_time" => Carbon::now(),
                "function" => $request->function,
                "delete_mark" => 0,
                "update_by" => 'admin'
            );
            ItemInspection::insert($data);
            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Success Input Item Inspection'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $th->getMessage()
            ]);
        }
    }


    public function getDetailPlantInspection(Request $request)
    {
        $data_plant_inspection = PlantInspection::where('id_inspection', $request->id_inspection)->first();

        return [
            'message' => 'Succes Get Data',
            'data_plant' => $data_plant_inspection
        ];
    }

    public function getSummaryKondisi(Request $request)
    {
        $data_count = ItemInspection::where('no_inspection', $request->no_inspection)->count();

        $data_risk = DB::select("select nm_area,
        COUNT(CASE WHEN m_kondisi.nm_kondisi = 'GOOD' THEN 1 END) AS GOOD,
        COUNT(CASE WHEN m_kondisi.nm_kondisi = 'LOW RISK' THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN m_kondisi.nm_kondisi = 'MED RISK' THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN m_kondisi.nm_kondisi = 'HIGH RISK' THEN 1 END) AS HIGH_RISK
        FROM t_item_inspection
        join m_kondisi on t_item_inspection.id_kondisi = m_kondisi.id_kondisi
        where no_inspection = ?
        GROUP BY t_item_inspection.nm_area", [$request->no_inspection]);

        return [
            'message' => 'Succes Get Data',
            'data_risk' => $data_risk,
            'data_count' => $data_count
        ];
    }

    //Main
    public function getDataPlantInspectionTransaction(Request $request)
    {
        $request->validate([
            'filter_data_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'filter_data_opco'  => 'nullable|exists:m_opco,kode_opco',
            'filter_data_bulan' => 'nullable|numeric',
            'filter_data_tahun' => 'nullable|numeric',
        ]);

        $plant_inspection_transaction = PlantInspection::select(['create_date', 'function', 'id_inspection', 'no_inspection', 'desc_inspection', 'kode_opco', 'kode_plant', 'status', 'remark', 'create_time']);
            if ($request->filter_data_opco) {
                $plant_inspection_transaction = $plant_inspection_transaction->where('kode_opco', $request->filter_data_opco);
        }
            if ($request->filter_data_plant) {
                $plant_inspection_transaction = $plant_inspection_transaction->where('kode_plant', $request->filter_data_plant);
        }
            if ($request->filter_data_tahun) {
                $plant_inspection_transaction = $plant_inspection_transaction->where(DB::raw("TO_CHAR(create_time, 'YYYY')"), $request->filter_data_tahun);
        }
            if ($request->filter_data_month) {
                $plant_inspection_transaction = $plant_inspection_transaction->where(DB::raw("TO_CHAR(create_time, 'MM')"), $request->filter_data_month);
        }
            if ($request->roles_id !== 6) {
                $plant_inspection_transaction = $plant_inspection_transaction->whereIn('status', ['On Review','Approve']);
        }
            $plant_inspection_transaction = $plant_inspection_transaction->where('kode_opco', Auth::user()->company)
            ->orderBy('no_inspection', 'desc');

        return DataTables::of($plant_inspection_transaction)->make();
    }

    //Add New Inspection Item
    public function getItemInspection(Request $request)
    {
        $data_item_inspection = ItemInspection::select('no_item_inspection', 'nm_area', 'id_equipment', 'desc_equipment', 'create_date', 'nm_kondisi', 't_item_inspection.remark', 'no_inspection')
            ->join('m_kondisi', 't_item_inspection.id_kondisi', '=', 'm_kondisi.id_kondisi')
            ->where('no_inspection', $request->no_inspection)
            ->orderBy('no_item_inspection', 'asc')
            ->get();

        return DataTables::of($data_item_inspection)->make();
    }

    public function getDataInputPlantInspection(Request $request)
    {
        $opco = DB::table('m_opco')
            ->select('kode_opco')->where('kode_opco', Auth::user()->company)
            ->get();

        $function = DB::table('m_user_functional')->whereRaw('id in (' . Auth::user()->functional_relation . ')')->get();
        $plant = KilnPlant::select('kode_opco', 'kode_plant', 'name_plant')->where('kode_opco', '=', $request->kode_opco)->get();

        $PlantInspection = KilnPlant::select('kode_opco', 'kode_plant', 'name_plant')->where('kode_opco', '=', $request->kode_opco)->get();

        return [
            'message' => 'Succes Get Data Filter Opco',
            'data_opco' => $opco,
            'function' => $function,
            'data_plant' => $plant
        ];
    }

    public function checkInspectionOnReview(Request $request)
    {
        if ($request->id_area != "") {
            $id_area = explode(",", $request->id_area);
            $plant_inspection_transaction = PlantInspection::join('t_item_inspection', 't_item_inspection.no_inspection', '=', 't_plant_inspection.no_inspection')
                ->select('t_plant_inspection.no_inspection')
                ->where('status', 'On Review')
            ->whereIn('id_area', $id_area)
                ->where('function', $request->function)
                ->groupBy('t_plant_inspection.no_inspection')
                ->get();

            $area = PlantInspection::join('t_item_inspection', 't_item_inspection.no_inspection', '=', 't_plant_inspection.no_inspection')
                ->select('t_item_inspection.nm_area')
                ->where('status', 'On Review')
                ->whereIn('id_area', $id_area)
                ->where('function', $request->function)
                ->groupBy('t_item_inspection.nm_area')
                ->get();
        } else {
            $plant_inspection_transaction = "empty";
            $area = "empty";
        }

        return [
            'message' => 'Succes Get Data',
            'data' => $plant_inspection_transaction,
            'area' =>  $area
        ];
    }


    public function addInspectionPlant(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = array(
                "desc_inspection" => $request->desc_inspection,
                "kode_opco" => $request->opco,
                "kode_plant" => $request->plant,
                "status" => $request->status,
                "function" => $request->function,
                "delete_mark" => 0
            );
            PlantInspection::where('no_inspection', $request->no_inspection_item)->update($data);


            $id_inspection =  PlantInspection::where('no_inspection', $request->no_inspection_item)->first();
            $data_catatan = array(
                "catatan" => $request->txtCatatan,
                "create_date" => Carbon::now(),
                "create_by" => Auth::user()->username,
                "status" => $request->status,
                "id_inspection" => $id_inspection->id_inspection
            );
            PlantCatatan::insert($data_catatan);
            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Success Input Plant Inspection'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $th->getMessage()
            ]);
        }
    }

    public function deleteItemInspection(Request $request)
    {
        DB::beginTransaction();
        try { //Mencoba apakah ada koneksi internet / dan koneksi database
            ItemInspection::where('no_item_inspection', $request->no_item_inspection)->delete();
            DB::commit();
            return [
                'message' => 'Successfull Delete Data',
                'status' => 'success'
            ];
        } catch (Exception $e) { // Jika ada gangguan internet / dan koneksi daya base akan memberikan notigikasi error
            DB::rollback();
            return [
                'status' => 'failed',
                'message' => $e->getMessage() // kembalikan ke API pesan error
            ];
        }
    }
    public function updateItemInspection(Request $request)
    {
        DB::beginTransaction();
        try { //Mencoba apakah ada koneksi internet / dan koneksi database

            ItemInspection::where('no_item_inspection', $request->no_item_inspection)->update([
                "id_kondisi" => $request->edit_id_kondisi,
                "remark" => $request->edit_remark,
                "update_date" => Carbon::now(),
                "update_by" => Auth::user()->username
            ]);
            DB::commit();

            return [
                'message' => 'Successfull Update Data',
                'status' => 'success'
            ];
        } catch (Exception $e) { // Jika ada gangguan internet / dan koneksi daya base akan memberikan notigikasi error
            DB::rollback();
            return [
                'status' => 'failed',
                'message' => $e->getMessage() // kembalikan ke API pesan error
            ];
        }
    }
    public function getDataUpdateInspectionItem(Request $request)
    {
        try { //Mencoba apakah ada koneksi internet / dan koneksi database
            if ($request->id_area == "empty" || $request->id_area == "") {
                $id_area = '1=1';
            } else {
                $id_area = 'id_area in ( ' . $request->id_area . ' )';
            }
            $area = Area::whereraw($id_area)->get();
            $inspection = ItemInspection::where('no_item_inspection', $request->no_item_inspection)->first();
            $condition = DB::table('m_kondisi')->get();
            $equipment = DB::table('m_equipment')->get();

            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data_inspection' => $inspection,
                'data_condition' => $condition,
                'data_area' => $area,
                'data_equipment' => $equipment
            ];
        } catch (Exception $e) { // Jika ada gangguan internet / dan koneksi daya base akan memberikan notigikasi error

            return [
                'status' => 'failed',
                'message' => $e->getMessage() // kembalikan ke API pesan error
            ];
        }
    }
}
