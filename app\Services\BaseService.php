<?php

namespace App\Services;

class BaseService
{
    public function getSapConfig($sbi = 'no_sbi')
    {
        $configs = [
            'no_sbi' => [
                'ashost' => env('SAP_ASHOST', '**********'),
                'sysnr' => env('SAP_SYSNR', '10'),
                'client' => env('SAP_CLIENT', '110'),
                'user' => env('SAP_USER', 'DMMRFC'),
                'passwd' => env('SAP_PASSWD', 'sisiDMM'),
            ],
            'sbi' => [
                'ashost' => env('SBI_SAP_ASHOST', '*************'),
                'sysnr' => env('SBI_SAP_SYSNR', '00'),
                'client' => env('SBI_SAP_CLIENT', '400'),
                'user' => env('SBI_SAP_USER', 'RAUTEXT03'),
                'passwd' => env('SBI_SAP_PASSWD', 'dmmsbi2022'),
            ]
        ];

        return $configs[$sbi];
    }
}
