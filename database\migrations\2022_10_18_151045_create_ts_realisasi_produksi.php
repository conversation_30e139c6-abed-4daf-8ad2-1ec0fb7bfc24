<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTSRealisasiProduksi extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ts_realisasi_produksi', function (Blueprint $table) {
            $table->bigIncrements('id_realisasi_produksi');
            $table->string('kode_plant', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->float('prod_klinker', 8, 5);
            $table->float('prod_semen', 8, 5);
            $table->float('clinker_sold', 8, 5);
            $table->float('prod_output', 8, 5);
            $table->float('ics', 8, 5);
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->softDeletes();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ts_realisasi_produksi');
    }
}
