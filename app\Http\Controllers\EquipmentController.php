<?php

namespace App\Http\Controllers;

use App\Exports\ExportEquipment;
use App\Imports\ImportEquipment;
use App\Models\Area;
use DataTables;
use Illuminate\Http\Request;
use App\Models\Equipment;
use App\Models\KilnPlant;
use App\Models\m_equipment;
use App\Models\m_kategori_equipment;
use App\Models\Opco;
use App\Services\SyncEquipmentService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;

class EquipmentController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Equipment',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'/equipment',
                ],
                [
                    'title'=>'Equipment',
                    'url'=>'',
                ]
            ],
        ];
        // $data['category'] = m_kategori_equipment::all();
        $data['area'] = Area::all();
        $data['opco'] = Opco::all();
        return view('equipment', $data);
    }

    public function datatables(Request $request)
    {
        $query    = m_equipment::leftjoin(
                        'm_kategori_equipment', 'm_equipment.id_kategori', '=', 'm_kategori_equipment.id_kategori')
                        ->leftjoin(
                        'm_area', 'm_equipment.id_area', '=', 'm_area.id_area')
                        ->leftjoin(
                        'm_opco', 'm_equipment.kode_opco', '=', 'm_opco.kode_opco')
                        ->select(
                            'm_equipment.id_equipment as id_equipment',
                            'm_equipment.kode_equipment as kode_equipment',
                            'm_equipment.nm_equipment as nm_equipment',
                            'm_opco.kode_opco as kode_opco',
                            'm_area.nm_area as nm_area',
                            )
                        ->get();
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    // public function datatablescategory(Request $request)
    // {
    //     $query    = m_kategori_equipment::get();
    //     $data     = DataTables::of($query)->make(true);
    //     $response = $data->getData(true);
    //     return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    // }

    public function importEquipment()
    {
        $data = [
            'title' => 'Import Equipment',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'/equipment',
                ],
                [
                    'title'=>'Equipment',
                    'url'=>'/equipment-import',
                ],
                [
                    'title'=>'Import Equipment',
                    'url'=>'',
                ]
            ],
        ];
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();
        $opt_opco = "";
        foreach ($opco as $value) {
            $kode_opco = $value['kode_opco'];
            $nama_opco = $value['nama_opco'];
            $opt_opco .= "<option value='$kode_opco'>$nama_opco</option>";
        }
        $data['opco'] = $opt_opco;
        $area = Area::select(['id_area', 'nm_area'])->orderBy('id_area')->get()->toArray();
        $opt_area = "";
        foreach ($area as $value) {
            $id_area = $value['id_area'];
            $nama_area = $value['nm_area'];
            $opt_area .= "<option value='$nama_area'>$nama_area</option>";
        }
        $data['area'] = $opt_area;
        return view('equipmentImport', $data);
    }

    public function insertData(Request $request)
    {
        $excel = json_decode($request->excel);
        $result = [];
        $success = true;
        //list opco
        $opco = Opco::select('kode_opco')->get()->toArray();
        $arrOpco = [];
        foreach ($opco as $value) {
            array_push($arrOpco, $value['kode_opco']);
        }

        $area = Area::select('nm_area')->get()->toArray();
        $arrArea = [];
        foreach ($area as $value) {
            array_push($arrArea, $value['nm_area']);
        }

        foreach ($excel as $data) {
            $oldData = $data;
            $data['no'] = $data[0];
            $data['equipment_code'] = $data[1];
            $data['equipment_name'] = $data[2];
            $data['area'] = $data[3];
            $data['kode_opco'] = $data[4];
            $data['is_valid'] = $data[5];
            $data = array_diff_key($data, $oldData);
            $format = $data;
            $message = "";
            $status = "Valid";

            if ($data['equipment_code'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom Kode Equipment tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom Kode Equipment tidak boleh kosong";
                }
            }

            if ($data['equipment_name'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom Nama Equipment tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom Nama Equipment tidak boleh kosong";
                }
            }

            if ($data['area'] == NULL) {
                $status = 'Invalid';
                $message = $message . "Kolom Area tidak boleh kosong ";
            } else if (!in_array($data['area'], $arrArea)) {
                $status = "Invalid";
                $message = $message . "Kolom Area tidak ditemukan";
            }

            if ($data['kode_opco'] == NULL) {
                $status = 'Invalid';
                $message = $message . "Kolom Opco tidak boleh kosong ";
            } else if (!in_array($data['kode_opco'], $arrOpco)) {
                $status = "Invalid";
                $message = $message . "Kolom Opco tidak ditemukan";
            }

            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function import(Request $request)
    {
        // validasi
        $this->validate($request, [
            'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author
        ]);

        // menangkap file excel
        $file = $request->file('excel');

        // import data
        $import = new ImportEquipment;
        Excel::import($import, $file);
        //list opco
        $opco = Opco::select('kode_opco')->get()->toArray();
        $arrOpco = [];
        foreach ($opco as $value) {
            array_push($arrOpco, $value['kode_opco']);
        }

        $area = Area::select('nm_area')->get()->toArray();
        $arrArea = [];
        foreach ($area as $value) {
            array_push($arrArea, $value['nm_area']);
        }

        $datas = ($import->data);
        $result = [];
        foreach ($datas as $data) {
            $format = $data;
            $message = "";
            $status = "Valid";

            if ($data['equipment_code'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom Kode Equipment tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom Kode Equipment tidak boleh kosong";
                }
            }

            if ($data['equipment_name'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom Nama Equipment tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom Nama Equipment tidak boleh kosong";
                }
            }

            if ($data['kode_opco'] == NULL) {
                $status = 'Invalid';
                $message = $message . "Kolom Opco tidak boleh kosong ";
            } else if (!in_array($data['kode_opco'], $arrOpco)) {
                $status = "Invalid";
                $message = $message . "Kolom Opco tidak ditemukan";
            }

            if ($data['area'] == NULL) {
                $status = 'Invalid';
                $message = $message . "Kolom Area tidak boleh kosong ";
            } else if (!in_array($data['area'], $arrArea)) {
                $status = "Invalid";
                $message = $message . "Kolom Area tidak ditemukan";
            }

            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function temp()
    {
        $fileName = 'Template Equipment.xlsx';
        return Excel::download(new ExportEquipment, $fileName);
    }

    public function datatablesarea(Request $request)
    {
        $query    = Area::get();
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    public function store(Request $request)
    {

        $request->validate([
            'kode_equipment'    => 'required',
            'nm_equipment'      => 'required',
            'kode_opco'         => 'required',
            'id_area'           => 'required',
            // 'id_kategori'       => 'required',
        ]);

        try{
            $equipment = m_equipment::create([
                'kode_equipment' => $request->kode_equipment,
                'nm_equipment' => $request->nm_equipment,
                'kode_opco' => $request->kode_opco,
                'id_area' => $request->id_area,
                // 'id_kategori' => $request->id_kategori,
                'create_by' => Auth::user()->username,
            ]);

        $response = responseSuccess(trans('message.read-success'),$equipment);
        return response()->json($response,200);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }

    }

    // public function storeCategory(Request $request)
    // {

    //     $request->validate([
    //         'nm_kategori'    => 'required',
    //     ]);

    //     try{
    //     $equipmentCategory = m_kategori_equipment::create([
    //         'nm_kategori' => $request->nm_kategori,
    //         'create_by' => Auth::user()->username,
    //     ]);

    //     $response = responseSuccess(trans('message.read-success'),$equipmentCategory);
    //     return response()->json($response,200);
    //     } catch (Exception $e) {
    //         $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
    //         return response()->json($response, 500);
    //     }
    // }

    public function storeArea(Request $request)
    {

        $request->validate([
            'nm_area'    => 'required',
        ]);

        try{
        $equipmentArea = Area::create([
            'nm_area' => $request->nm_area,
            'create_by' => Auth::user()->username,
        ]);

        $response = responseSuccess(trans('message.read-success'),$equipmentArea);
        return response()->json($response,200);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($initiator)
    {
        $query   = m_equipment::leftjoin(
                        'm_kategori_equipment', 'm_equipment.id_kategori', '=', 'm_kategori_equipment.id_kategori')
                        ->leftjoin(
                        'm_area', 'm_equipment.id_area', '=', 'm_area.id_area')
                        ->select(
                            'm_equipment.id_equipment as id_equipment',
                            'm_equipment.kode_equipment as kode_equipment',
                            'm_equipment.nm_equipment as nm_equipment',
                            'm_equipment.kode_opco as kode_opco',
                            // 'm_kategori_equipment.id_kategori as id_kategori',
                            'm_area.id_area as id_area',
                            )->find($initiator);
        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }

    // public function showCategory($initiator)
    // {
    //     $query   = m_kategori_equipment::find($initiator);
    //     $response = responseSuccess(trans('message.read-success'),$query);
    //     return response()->json($response,200);
    // }

    public function showArea($initiator)
    {
        $query   = Area::find($initiator);
        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $query   = Equipment::find($id);
        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update($id, Request $request)
    {
        $initiator = m_equipment::find($id);
        $data = $this->findDataWhere(m_equipment::class, ['id_equipment' => $id]);

         $request->validate([
            'kode_equipment'    => 'required',
            'nm_equipment'      => 'required',
            'kode_opco'         => 'required',
            'id_area'           => 'required',
            // 'id_kategori'       => 'required',
        ]);

        //   dd($data);exit();
          DB::beginTransaction();
          try {
              $data->update([
                    'kode_equipment' => $request->kode_equipment,
                    'nm_equipment' => $request->nm_equipment,
                    'kode_opco' => $request->kode_opco,
                    'id_area' => $request->id_area,
                    // 'id_kategori' => $request->id_kategori,
                    'update_by'         => Auth::user()->username,
              ]);
              DB::commit();
              $response = responseSuccess(trans("messages.update-success"), $data);
              return response()->json($response, 200, [], JSON_PRETTY_PRINT);
          } catch (Exception $e) {
              DB::rollback();
              $response = responseFail(trans("messages.update-fail"), $e->getMessage());
              return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }

    }

    public function saveData(Request $request)
    {
        $excel = json_decode($request->excel);
        foreach ($excel as $item) {
            $area = Area::select('id_area','nm_area')->where('nm_area', $item[3])->first();
            m_equipment::create([
                'kode_equipment' => $item[1],
                'nm_equipment' => $item[2],
                'kode_opco' => $item[4],
                'id_area' => $area->id_area,
                'create_by' => Auth::user()->username,
            ]);
        }
        $response = responseSuccess('Data added successfully');
        return response()->json($response,200);
    }

    // public function updateCategory($id, Request $request)
    // {
    //     $initiator = m_kategori_equipment::find($id);
    //     $data = $this->findDataWhere(m_kategori_equipment::class, ['id_kategori' => $id]);

    //     $request->validate([
    //         'nm_kategori'    => 'required',
    //     ]);

    //     //   dd($data);exit();
    //       DB::beginTransaction();
    //       try {
    //           $data->update([
    //                 'nm_kategori' => $request->nm_kategori,
    //                 'update_by' => Auth::user()->username,
    //           ]);
    //           DB::commit();
    //           $response = responseSuccess(trans("messages.update-success"), $data);
    //           return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    //       } catch (Exception $e) {
    //           DB::rollback();
    //           $response = responseFail(trans("messages.update-fail"), $e->getMessage());
    //           return response()->json($response, 500, [], JSON_PRETTY_PRINT);
    //         }

    // }

    public function updateArea($id, Request $request)
    {
        $initiator = Area::find($id);
        $data = $this->findDataWhere(Area::class, ['id_area' => $id]);

        $request->validate([
            'nm_area'    => 'required',
        ]);

        //   dd($data);exit();
          DB::beginTransaction();
          try {
              $data->update([
                    'nm_area' => $request->nm_area,
                    'update_by' => Auth::user()->username,
              ]);
              DB::commit();
              $response = responseSuccess(trans("messages.update-success"), $data);
              return response()->json($response, 200, [], JSON_PRETTY_PRINT);
          } catch (Exception $e) {
              DB::rollback();
              $response = responseFail(trans("messages.update-fail"), $e->getMessage());
              return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }

    }

    public function destroy($id)
    {
        m_equipment::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }

    // public function destroyCategory($id)
    // {
    //     m_kategori_equipment::destroy($id);
    //     $response = responseSuccess(trans('message.delete-success'));
    //     return response()->json($response,200);
    // }

    public function destroyArea($id)
    {
        Area::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }

    public function sync(Request $request)
    {
        $request->validate([
            'date_sync' => 'required',
            'opco_sync' => 'required',
            'plant_sync' => 'required'
        ]);

        try {
            if ($request->opco_sync == 'GHOPO') {
                $request->opco_sync = 'SG';
            }

            $tplnr = $request->opco_sync . '-' . $request->plant_sync;
            $date = Carbon::parse($request->date_sync)->format('Ymd');

            $result = (new SyncEquipmentService)->execute($date, $tplnr);
            if ($result['status'] == 'fail') {
                throw new Exception($result['message']);
            }
            $response = responseSuccess(trans('messages.create-success'));
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }
}
