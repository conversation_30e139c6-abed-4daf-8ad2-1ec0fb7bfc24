<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTKilnStop extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('t_kiln_stop', function (Blueprint $table) {
            $table->bigIncrements('id_kiln_stop');
            $table->string('kode_plant', 30);
            $table->timestamp('tanggal_mulai')->nullable()->default(NULL);
            $table->timestamp('tanggal_selesai')->nullable()->default(NULL);
            $table->string('jenis_downtime', 30)->nullable();
            $table->unsignedBigInteger('id_kategori')->nullable();
            $table->string('alasan', 200)->nullable();
            $table->string('source_system', 30);
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->string('deleted_by', 30)->nullable();
            $table->softDeletes();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('t_kiln_stop');
    }
}
