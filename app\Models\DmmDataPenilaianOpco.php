<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class DmmDataPenilaianOpco extends Model
{
    //
    protected $table = 'dmm_data_penilaian_opco';
    protected $fillable = [
        'penilaian_opco_id',
        'created_by',
        'updated_by',
    ];

    public static function list()
    {
        return DB::table('dmm_data_penilaian_opco as ddpo')
        ->leftjoin('dmm_penilaian_opco as dpo','ddpo.penilaian_opco_id' ,'dpo.id')
        ->leftjoin('m_company as mc','dpo.company_id','mc.id')
        ->leftjoin('dmm_periode_penilaian as dpp','dpo.periode_penilaian_id','dpp.id')
        ->leftjoin('users as u', 'dpo.created_by', '=', 'u.id')
        ->leftjoin('users as u2', 'ddpo.created_by', '=', 'u2.id')
        ->select('ddpo.*','mc.company as company_id','mc.description as company_desc','dpp.tahun','dpp.periode','dpo.end_date','u.username as creator','u2.username as created_by_name');
    }

    public function penilaianOpco()
    {
        return $this->hasOne(PenilaianOpco::class,'id','penilaian_opco_id');
    }

}
