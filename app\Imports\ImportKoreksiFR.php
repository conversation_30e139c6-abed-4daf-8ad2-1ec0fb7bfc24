<?php

namespace App\Imports;

use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class ImportKoreksiFR implements ToCollection, WithHeadingRow
{
    public $data;

    public function collection(Collection $rows)
    {
        $data = [];
        foreach($rows as $row){
            if($row['opco'] != ''){
                if(is_int($row['date'])){
                    // kondisi data jika tanggal format exel
                    $row['date'] = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row['date'])->format('Y-m-d');
                    // dd($row['tanggal']);
                }else{
                    // kondisi data jika tanggal format database atau string
                    $row['date'] = \Carbon\Carbon::createFromFormat('d/m/Y', $row['date'])->format('Y-m-d');
                }

                // $row['date'] = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row['date'])->format('Y-m-d');
                $data[] = $row;
            }
        }
        $this->data = collect($data);
    }
}
