<?php

namespace App\Http\Controllers;

use DataTables;
use App\Models\ExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ExchangeRateController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = [
            'title' => 'Exchange Rate',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'/exchangerate',
                ],
                [
                    'title'=>'Exchange Rate',
                    'url'=>'',
                ]
            ],
        ];
        return view('master.ExchangeRate',$data);
    }

    public function datatables(Request $request)
    {
        $query    = ExchangeRate::select('id',DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY') AS tanggal"),DB::raw("to_char(nilai, '999G999G999G999G999') as nilai"))->get();
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $username = Auth::user()->username;

        $request->validate([
            'tahun' => 'required',
            'nilai' => 'required'
        ]);

        try{
            $date = date($request->tahun."-01-01");

            $functional = ExchangeRate::updateOrCreate([
                'tanggal' => $date,
            ],[
                'tanggal' => $date,
                'nilai' => $request->nilai,
                'create_by' => $username
            ]);

        $response = responseSuccess(trans('message.read-success'),$functional);
        return response()->json($response,200);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $query   = ExchangeRate::select('id',DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY') AS tanggal"),'nilai')->findOrFail($id);
        $response = responseSuccess(trans('messages.read-success'), $query);
        return response()->json($response, 200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update($id, Request $request)
    {
        $request->validate([
            'tahun' => 'required',
            'nilai' => 'required'
        ]);

        $username = Auth::user()->username;
        $request->tahun = date($request->tahun."-01-01");

        $cek_tahun = ExchangeRate::select('id', 'tanggal')->where('tanggal', '=', $request->tahun)->first();

        try {
            if ($cek_tahun) {
                if ($request->tahun === $cek_tahun->tanggal) {
                $model_delete = ExchangeRate::findOrFail($id);
                $model_delete->delete();

                $model = ExchangeRate::updateOrCreate([
                            'tanggal' => $request->tahun,
                        ],[
                            'tanggal' => $request->tahun,
                            'nilai' => $request->nilai,
                            'create_by' => $username
                        ]);
                }
            }else{
        $model = ExchangeRate::findOrFail($id);

            $model->update(array_merge(['tanggal' => $request->tahun],['nilai' => $request->nilai],['update_by' => $username]));

            }


            $response = responseSuccess(trans('messages.update-success'), $model);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.update-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $model = ExchangeRate::findOrFail($id);
        try {
            $model->delete();
            $response = responseSuccess(trans('messages.delete-success'), $model);
            return response()->json($response, 200);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.delete-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }
}
