<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVwFrAll extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("DROP VIEW IF EXISTS vw_fr_all");
        DB::statement("CREATE OR REPLACE VIEW vw_fr_all AS
                SELECT m_opco.no_pbi AS no,
                m_opco.holding,
                m_opco.kode_opco AS kd_opco,
                m_opco.name_opco AS nm_opco,
                    CASE
                        WHEN rkap_cost.tahun IS NULL THEN NULL::text
                        WHEN concat(rkap_cost.bulan, '-', rkap_cost.tahun) = to_char(now(), 'FMMM-YYYY'::text) THEN to_char(now(), 'YYYY-MM-DD'::text)
                        ELSE concat(to_char(concat(rkap_cost.tahun, '-', rkap_cost.bulan, '-01')::date::timestamp with time zone, 'YYYY-MM'::text), '-', to_char((date_trunc('month'::text, concat(rkap_cost.tahun, '-', rkap_cost.bulan, '-01')::date::timestamp with time zone) + '1 mon -1 days'::interval)::date::timestamp with time zone, 'DD'::text))
                    END AS tanggal,
                COALESCE(rkap_cost.rkap_bahan_bakar, 0::bigint) AS rkap_bahan_bakar,
                COALESCE(rkap_cost.rkap_bahan_baku, 0::bigint) AS rkap_bahan_baku,
                COALESCE(rkap_cost.rkap_listrik, 0::bigint) AS rkap_listrik,
                COALESCE(rkap_cost.rkap_tenaga_kerja, 0::bigint) AS rkap_tenaga_kerja,
                COALESCE(rkap_cost.rkap_pemeliharaan, 0::bigint) AS rkap_pemeliharaan,
                COALESCE(rkap_cost.rkap_penyusutan, 0::bigint) AS rkap_deplesi_penyusutan_mortisasi,
                COALESCE(rkap_cost.rkap_administrasi_umum, 0::bigint) AS rkap_urusan_umum_dan_adm_kantor,
                COALESCE(rkap_cost.rkap_pajak_asuransi, 0::bigint) AS rkap_pajak_dan_asuransi,
                COALESCE(rkap_cost.rkap_elim_bb, 0::bigint) AS rkap_elim_bahan_baku_dan_penolong,
                COALESCE(rkap_cost.rkap_elim_penyusutan, 0::bigint) AS rkap_elim_deplesi_penyusutan_amortisasi,
                COALESCE(rkap_cost.rkap_elim_administrasi, 0::bigint) AS rkap_elim_urusan_umum_adm_kantor,
                COALESCE(rp.rkap_prod_klinker, 0::bigint) AS rkap_prod_clinker_ton,
                COALESCE(rp.rkap_prod_semen, 0::bigint) AS rkap_prod_cement_ton,
                COALESCE(rp.rkap_clinker_sold, 0::bigint) AS rkap_clinker_sales_ton,
                COALESCE(rp.rkap_prod_output, 0::bigint) AS rkap_prod_output,
                COALESCE(trb.bahan_bakar, 0::bigint::double precision) AS bahan_bakar,
                COALESCE(trb.bahan_baku, 0::bigint::double precision) AS bahan_baku_dan_penolong,
                COALESCE(trb.listrik, 0::bigint::double precision) AS listrik,
                COALESCE(trb.tenaga_kerja, 0::bigint::double precision) AS tenaga_kerja,
                COALESCE(trb.pemeliharaan, 0::bigint::double precision) AS pemeliharaan,
                COALESCE(trb.penyusutan, 0::bigint::double precision) AS deplesi_penyusutan_amortisasi,
                COALESCE(trb.administrasi_umum, 0::bigint::double precision) AS urusan_umum_adm_kantor,
                COALESCE(trb.pajak_asuransi, 0::bigint::double precision) AS pajak_dan_asuransi,
                COALESCE(trb.elim_bb, 0::bigint::double precision) AS elim_bahan_baku_penolong,
                COALESCE(trb.elim_penyusutan, 0::bigint::double precision) AS elim_deplesi_penyusutan_amortisasi,
                COALESCE(trb.elim_administrasi, 0::bigint::double precision) AS elim_urusan_umum_adm_kantor,
                COALESCE(trp.prod_klinker, 0::bigint::double precision) AS real_prod_clincker_ton,
                COALESCE(trp.prod_semen, 0::bigint::double precision) AS real_prod_cement_ton,
                COALESCE(trp.clinker_sold, 0::bigint::double precision) AS real_clinker_sales_ton,
                COALESCE(trp.prod_output, 0::bigint::double precision) AS real_prod_putput_ton,
                COALESCE(trp.ics, 0::bigint::double precision) AS ics_ton
            FROM m_opco
                LEFT JOIN rkap_cost ON m_opco.kode_opco::text = rkap_cost.kode_opco::text
                LEFT JOIN ts_realisasi_biaya trb ON m_opco.kode_opco::text = trb.kode_opco::text AND concat(rkap_cost.tahun, '-', rkap_cost.bulan) = to_char(trb.tanggal::timestamp with time zone, 'YYYY-FMMM'::text)
                LEFT JOIN rkap_produksi rp ON m_opco.kode_opco::text = rp.kode_opco::text AND concat(rkap_cost.tahun, '-', rkap_cost.bulan) = concat(rp.tahun, '-', rp.bulan)
                LEFT JOIN ts_realisasi_produksi trp ON m_opco.kode_opco::text = trp.kode_opco::text AND concat(rkap_cost.tahun, '-', rkap_cost.bulan) = to_char(trp.tanggal::timestamp with time zone, 'YYYY-FMMM'::text)
            ORDER BY (
                    CASE
                        WHEN rkap_cost.tahun IS NULL THEN NULL::text
                        ELSE to_char(concat('01-', rkap_cost.bulan, '-', rkap_cost.tahun)::date::timestamp with time zone, 'YYYY-MM-DD'::text)
                    END), m_opco.no_pbi");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement("DROP VIEW IF EXISTS vw_fr_all");
        DB::commit();

    }
}
