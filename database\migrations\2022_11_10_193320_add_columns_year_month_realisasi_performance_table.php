<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsYearMonthRealisasiPerformanceTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ts_realisasi_performance', function (Blueprint $table) {
            $table->integer('tahun')->nullable();
            $table->integer('bulan')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ts_realisasi_performance', function (Blueprint $table) {
            $table->dropColumn(['tahun', 'bulan']);
        });
    }
}
