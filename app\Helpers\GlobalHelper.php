<?php

namespace App\Helpers;

class GlobalHelper
{
    public static function getOpco($id) {
        if(!function_exists('getOpco')) {
            $dt = [
                1 => ['opco' => 1, 'opco_name' => 'GHOPO', 'opco_value' => 'ghopo'],
                2 => ['opco' => 2, 'opco_name' => 'SG', 'opco_value' => 'sg'],
                3 => ['opco' => 3, 'opco_name' => 'SP', 'opco_value' => 'sp'],
                4 => ['opco' => 4, 'opco_name' => 'ST', 'opco_value' => 'st'],
                5 => ['opco' => 5, 'opco_name' => 'SBI', 'opco_value' => 'sbi'],
                6 => ['opco' => 6, 'opco_name' => 'TLCC', 'opco_value' => 'tlcc'],
            ];
            return $id == 'all' ? $dt : $dt[$id];
        }
    }

    public static function getYear()
    {
        if(!function_exists('getYear')) {
            for($a=date("Y");$a>=2019;$a--){
                $year[] = $a;
            }
            return $year;
        }
    }

    public static function getMonth($id)
    {
        if(!function_exists('getMonth')) {
            $dt = [
                1 => ['month' => 1, 'month_name' => 'January', 'month_value' => 'january'],
                2 => ['month' => 2, 'month_name' => 'February', 'month_value' => 'february'],
                3 => ['month' => 3, 'month_name' => 'March', 'month_value' => 'march'],
                4 => ['month' => 4, 'month_name' => 'April', 'month_value' => 'april'],
                5 => ['month' => 5, 'month_name' => 'May', 'month_value' => 'may'],
                6 => ['month' => 6, 'month_name' => 'June', 'month_value' => 'june'],
                7 => ['month' => 7, 'month_name' => 'July', 'month_value' => 'july'],
                8 => ['month' => 8, 'month_name' => 'August', 'month_value' => 'august'],
                9 => ['month' => 9, 'month_name' => 'September', 'month_value' => 'september'],
                10 => ['month' => 10, 'month_name' => 'October', 'month_value' => 'october'],
                11 => ['month' => 11, 'month_name' => 'November', 'month_value' => 'november'],
                12 => ['month' => 12, 'month_name' => 'December', 'month_value' => 'december'],
                // 13 => ['month' => 13, 'month_name' => 'All Month', 'month_value' => 'all month'],
            ];
                return $id == 'all' ? $dt : $dt[$id];
        }
    }

    public static function change_commas($value) {

            $data = str_replace(".", ",", round($value, 2));
            return $data;

    }

    public static function fnumber_id_en($value) {
    // contoh angka 1.200,55
    // menjadi angka 1200.55
            $data = str_replace(".", "", $value);
            // $data = str_replace(",", ".", $data);
            return round($data,2);

    }

    public static function convert_UTF_8($value){

        $data = mb_convert_case($value, MB_CASE_TITLE, "UTF-8");
        return $data;
    }
}
