<?php

namespace App\Imports;

use App\Models\ItemInspection;
use Maatwebsite\Excel\Concerns\ToModel;

class ImportInspectionItemModel implements ToModel
{
    public $data;
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */

    public function  __construct($no_inspection)
    {
        $this->no_inspection = $no_inspection;
    }

    public function model(array $row)
    {
        if ($row['2'] != 'id_area' and  $row['2'] != null) {
            return new ItemInspection([
                'id_equipment' => $row['0'],
                'desc_equipment' => $row['1'],
                'id_area' => (int)$row['2'],
                'nm_area' => $row['3'],
                'id_kondisi' => (int)$row['4'],
                'remark' => $row['5'],
                'create_date' => $row['6'],
                'no_inspection' => $this->no_inspection
            ]);
        }
    }
}
