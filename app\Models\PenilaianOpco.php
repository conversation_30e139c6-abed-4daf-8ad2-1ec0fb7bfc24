<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PenilaianOpco extends Model
{
    //
    protected $table = 'dmm_penilaian_opco';
    protected $primaryKey = 'id';
    protected $fillable = ['uuid','company_id','tahun','periode_penilaian_id','start_date','end_date','status','created_by','updated_by'];

    public static function list()
    {
        return self::select('dmm_penilaian_opco.*','mc.company as company_name','mc.description as company_desc','dpp.tahun','dpp.periode','u.username as created_by_name')
            ->leftJoin('m_company as mc','dmm_penilaian_opco.company_id','=','mc.id')
            ->leftJoin('dmm_periode_penilaian as dpp','dmm_penilaian_opco.periode_penilaian_id','=','dpp.id')
            ->leftjoin('users as u', 'dmm_penilaian_opco.created_by', '=', 'u.id');
    }

    public function periodePenilaian()
    {
        return $this->hasOne('App\Models\PeriodePenilaian','id','periode_penilaian_id');
    }
    public function company()
    {
        return $this->hasOne('App\Models\Company','id','company_id');
    }    
}
