<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Events\AfterSheet;
use DB;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithEvents;

class ExportTempKoreksiPerformance implements FromCollection, WithHeadings, WithTitle, WithStyles, WithEvents
{
    public function  __construct($data)
    {
      $this->data = $data;
    }
    /**
     * @return string
     */
    public function title(): string
    {
      return 'Koreksi Performance Level 1';
    }
  
    public function headings(): array
    {
      return [
      'Kode Plant',
      'Tanggal Mulai',
      'Spent(H)',
      '<PERSON><PERSON><PERSON>',
      '<PERSON>asan',
      ];
    }
  
    public function collection()
    {
      return collect($this->data);
    }

  
    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                //set background color for header (A1:D1)
                $event->sheet->getDelegate()
                ->getStyle('A1:E1')
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('C4D79B');

                //set header (A1:D1) uneditable and set workspace (A2:D1000) editable
                // $event->sheet->protectCells('A1:D1', 'PASSWORD');
                // $event->sheet->getStyle('A2:D1000')->getProtection()->setLocked(\PhpOffice\PhpSpreadsheet\Style\Protection::PROTECTION_UNPROTECTED);
                // $event->sheet->getDelegate()->getProtection()->setSheet(true);

            },
        ];
    }
  
  
    //set width for header
    public function columnWidths(): array
    {
      return [
        'A' => 15,
        'B' => 15,
        'C' => 15,
        'D' => 15,
        'E' => 15,
      ];
    }
  
    //set bold for header
    public function styles(Worksheet $sheet)
    {
      return [
        'A1' => ['font' => ['bold' => true]],
        'B1' => ['font' => ['bold' => true]],
        'C1' => ['font' => ['bold' => true]],
        'D1' => ['font' => ['bold' => true]],
        'E1' => ['font' => ['bold' => true]],
      ];
    }

}
