<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Illuminate\Support\Arr;

class ExportDataKoreksiFRMTCCost implements FromCollection, WithTitle, WithStyles
{
  /**
   * @return \Illuminate\Support\Collection
   */
  public function  __construct($data, $sheetName, $tahun)
  {
    $this->data = $data;
    $this->sheetName = $sheetName;
    $this->tahun = $tahun;
  }
  /**
   * @return string
   */
  public function title(): string
  {
    return $this->sheetName;
  }

  public function collection()
  {
    return collect();
  }


  //set width for header
  public function columnWidths(): array
  {
    return [
      'A' => 15,
      'B' => 15,
      'C' => 15,
      'D' => 15,
      'E' => 15,
      'F' => 15,
      'G' => 15,
      'H' => 15,
      'I' => 15,
      'J' => 15,
      'K' => 15,
      'L' => 15,
      'M' => 15,
      'N' => 15,
      'O' => 15,
      'P' => 15,
      'Q' => 15,
      'R' => 15,
      'S' => 15
    ];
  }

  //set bold for header

  public function header($sheet, $start, $textCenter, $fontBold, $border)
  {
    $fillHeader = array(
      'fill' => array(
        'fillType' => Fill::FILL_SOLID,
        'startColor' => array('argb' => 'F64E60')
      )
    );

    $sheet->getStyle("C" . $start . ":L" . $start)->applyFromArray($border);
    $mulai = $start;

    $sheet->setCellValue('B' . $start, 'KETERANGAN');
    $sheet->setCellValue('C' . $start, 'BULAN INI');
    $sheet->setCellValue('H' . $start, 'S/D BULAN INI');
    $start++;
    $sheet->getStyle("F" . $start . ":G" . $start)->applyFromArray($border);
    $sheet->getStyle("K" . $start . ":L" . $start)->applyFromArray($border);
    $sheet->setCellValue('C' . $start, 'RKAP ' . $this->tahun);
    $sheet->setCellValue('D' . $start, 'REAL ' . $this->tahun);
    $sheet->setCellValue('E' . $start, 'REAL ' . ($this->tahun-1));
    $sheet->setCellValue('F' . $start, '%');
    $sheet->setCellValue('H' . $start, 'RKAP ' . $this->tahun);
    $sheet->setCellValue('I' . $start, 'REAL ' . $this->tahun);
    $sheet->setCellValue('J' . $start, 'REAL ' . ($this->tahun-1));
    $sheet->setCellValue('K' . $start, '%');
    $start++;
    $sheet->setCellValue('C' . $start, '(1)');
    $sheet->setCellValue('D' . $start, '(2)');
    $sheet->setCellValue('E' . $start, '(3)');
    $sheet->setCellValue('F' . $start, '(2:1)');
    $sheet->setCellValue('G' . $start, '(2:3)');
    $sheet->setCellValue('H' . $start, '(4)');
    $sheet->setCellValue('I' . $start, '(5)');
    $sheet->setCellValue('J' . $start, '(6)');
    $sheet->setCellValue('K' . $start, '(5:4)');
    $sheet->setCellValue('L' . $start, '(5:6)');

    $akhir = $start;

    $sheet->mergeCells('B' . $mulai . ':B' . $akhir);
    $sheet->mergeCells('C' . $mulai . ':G' . $mulai);
    $sheet->mergeCells('H' . $mulai . ':L' . $mulai);
    $sheet->mergeCells('F' . ($mulai + 1) . ':G' . ($mulai + 1));
    $sheet->mergeCells('K' . ($mulai + 1) . ':L' . ($mulai + 1));

    $sheet->getStyle("B" . $mulai . ":L" . $akhir)->applyFromArray($textCenter);
    $sheet->getStyle("B" . $mulai . ":L" . $akhir)->applyFromArray($fontBold);
    // $sheet->getStyle("B" . $mulai . ":L" . $akhir)->applyFromArray($fillHeader);

    $cell = ['B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L'];
    foreach ($cell as $key => $value) {
      $sheet->getColumnDimension($value)->setAutoSize(true);
      $sheet->getStyle($value . $mulai . ":" . $value . $akhir)->applyFromArray($border);
    }
  }

  public function dataSMI($sheet, $start, $datas, $fontBold, $border, $textCenter, $colorBlue, $colorRed)
  {
    foreach ($datas as $key => $value) {
      $datasmi[$key] =  $value;
    }

    $akhir = 0;
    $smi_kat = ['SIG', 'MTBF'];
    foreach ($smi_kat as $key => $smi) {
      if ($smi == 'SIG') {
        $awal = $start;
        $tahun = $this->tahun;
        $cell_sum = ['C', 'D', 'E', 'H', 'I', 'J'];
        $init = [
          ['B', 'kode_plant'],
          ['C', 'rkap' . $tahun . '_this_month'],
          ['D', 'real' . $tahun . '_this_month'],
          ['E', 'real' . ($tahun - 1) . '_this_month'],
          ['F', '2_1'],
          ['G', '2_3'],
          ['H', 'rkap' . $tahun . '_until_month'],
          ['I', 'real' . $tahun . '_until_month'],
          ['J', 'real' . ($tahun - 1) . '_until_month'],
          ['K', '5_4'],
          ['L', '5_6'],

        ];
        $kategori = ['COST','RUPIAH/TON','USD/TON'];

        $keterangan = [
          ['DALAM JUTA RUPIAH (Juta) :', 'COST'],
          ['Rupiah PER TON (Rp/ton) : ', 'RUPIAH/TON'],
          ['USD PER TON ($/ton) : ', 'USD/TON'],
        ];

        foreach ($keterangan as $keyKeterangan => $valKet) {
          $sheet->setCellValue('B' . $start, $valKet[0]);
          $sheet->getStyle("B" . $start)->applyFromArray($fontBold);
          $start++;

          $getData = $datasmi[$valKet[1]]['SIG'];
          $kodeOpco = $getData['kode'];

          foreach ($init as $key => $value) {
            if ($value[1] == 'kode_plant') {
              $sheet->setCellValue($value[0] . $start, $kodeOpco);
              $sheet->getStyle($value[0] . $start)->applyFromArray($fontBold);
            }
          }
          $sum = $start;
          $start++;
          $start_sum = $start;
          foreach ($getData as $key => $dt) { //opco
            if (!in_array($key, ['kode', $kodeOpco])) {
              foreach ($init as $key => $value) {
                if ($value[1] == 'kode_plant') {
                  $valOpco = Arr::exists($dt, 'kode_opco') ? $dt['kode_opco'] : '';
                  $sheet->setCellValue($value[0] . $start, $valOpco);
                } else {
                  $val = Arr::exists($dt, $value[1]) ? $dt[$value[1]] : '0';
                  $sheet->setCellValue($value[0] . $start, $val);
                  if ($valKet[1] == 'USD/TON'){
                    $sheet->getStyle($value[0] . $start)->getNumberFormat()->setFormatCode('#,##0.00');
                    if (in_array($value[1], ['2_1', '2_3', '5_4', '5_6'])) {
                      $sheet->getStyle($value[0] . $start)->getNumberFormat()->setFormatCode('#,##0.00');
                      if ($val > 100) {
                        $sheet->getStyle($value[0] . $start)->applyFromArray($colorRed);
                      }
                    } 
                  }
                  else{
                      $sheet->getStyle($value[0] . $start)->getNumberFormat()->setFormatCode('#,##0');
                    if (in_array($value[1], ['2_1', '2_3', '5_4', '5_6'])) {
                      $sheet->getStyle($value[0] . $start)->getNumberFormat()->setFormatCode('#,##0.00');
                      if ($val > 100) {
                        $sheet->getStyle($value[0] . $start)->applyFromArray($colorRed);
                      }
                    } 
                    }
                }
              }
              $start++;
            }
          }
          $end_sum = ($start - 1);
          $start++;
          $dataSIG = $datasmi[$valKet[1]]['SIG']['SIG'];
          foreach ($init as $key => $value) {
            if ($value[1] == 'kode_plant') {
              $valOpco = Arr::exists($dataSIG, 'kode_opco') ? $dataSIG['kode_opco'] : '';
              $sheet->setCellValue($value[0] . $start, $valOpco);
              $sheet->getStyle($value[0] . $start)->applyFromArray($fontBold);
            }
            else{
              $val = Arr::exists($dataSIG, $value[1]) ? $dataSIG[$value[1]] : '0';;
              $sheet->setCellValue($value[0] . ($start_sum-1), $val);
              $sheet->getStyle($value[0] . ($start_sum-1))->applyFromArray($fontBold);
              if ($valKet[1] == 'USD/TON'){
                $sheet->getStyle($value[0] . ($start_sum-1))->getNumberFormat()->setFormatCode('#,##0.00');
                if (in_array($value[1], ['2_1', '2_3', '5_4', '5_6'])) {
                  $sheet->getStyle($value[0] . ($start_sum-1))->getNumberFormat()->setFormatCode('#,##0.00');
                  if ($val > 100) {
                    $sheet->getStyle($value[0] . ($start_sum-1))->applyFromArray($colorRed);
                  }
                } 
              }
              else{
                  $sheet->getStyle($value[0] . ($start_sum-1))->getNumberFormat()->setFormatCode('#,##0');
                if (in_array($value[1], ['2_1', '2_3', '5_4', '5_6'])) {
                  $sheet->getStyle($value[0] . ($start_sum-1))->getNumberFormat()->setFormatCode('#,##0.00');
                  if ($val > 100) {
                    $sheet->getStyle($value[0] . ($start_sum-1))->applyFromArray($colorRed);
                  }
                } 
                }
            }
            }
          }
          foreach (['F', 'G', 'K', 'L'] as $key => $cellDiv) {
            $sheet->getStyle($cellDiv . $sum)->applyFromArray($fontBold);
            $sheet->getStyle($cellDiv . $sum)->getNumberFormat()->setFormatCode('#,##0.00');
          }
        
        $akhir = $start - 1;
        foreach ($init as $key => $val) {
          $sheet->getStyle("B" . ($awal - 1) . ":" . $val[0] . $akhir)->applyFromArray($border);
        }
      }
    }

    foreach (['D','I'] as $key => $value) {
      $sheet->getStyle($value)->applyFromArray($colorBlue);
    }
  }

  public function styles(Worksheet $sheet)
  {
    $textCenter = array(
      'alignment' => array(
        'horizontal' => Alignment::HORIZONTAL_CENTER,
        'vertical' => Alignment::VERTICAL_CENTER,
      )
    );

    $fontBold = array(
      'font'  => array(
        'bold'  => true,
      )
    );

    $border = array(
      'borders' => array(
        'outline' => array(
          'borderStyle' => Border::BORDER_THIN,
          'color' => array('rgb' => '000000')
        )
      )
    );

    $colorBlue = array(
      'font'  => array(
        'color' => array('rgb' => '0000FF'),
      )
    );

    $colorRed = array(
      'font'  => array(
        'color' => array('rgb' => 'FF0000'),
      )
    );

    $start = 6;
    $startHeader = 2;

    $this->header($sheet, $startHeader, $textCenter, $fontBold, $border);

    if ($this->sheetName == 'SIG') {
      $this->dataSMI($sheet, $start, $this->data, $fontBold, $border, $textCenter, $colorBlue, $colorRed);
    }
  }
}
