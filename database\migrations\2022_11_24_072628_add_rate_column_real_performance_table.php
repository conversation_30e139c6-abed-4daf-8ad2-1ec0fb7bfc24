<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddRateColumnRealPerformanceTable extends Migration
{
    public function up()
    {
        // Only add columns if table exists and columns don't exist
        if (Schema::hasTable('ts_realisasi_performance_daily')) {
            Schema::table('ts_realisasi_performance_daily', function (Blueprint $table) {
                if (!Schema::hasColumn('ts_realisasi_performance_daily', 'rate_gross')) {
                    $table->float('rate_gross', 52, 0)->nullable();
                }
                if (!Schema::hasColumn('ts_realisasi_performance_daily', 'rate_netto')) {
                    $table->float('rate_netto', 52, 0)->nullable();
                }
            });
        }
        // If table doesn't exist, do nothing - it will be created later with these columns
    }

    public function down()
    {
        if (Schema::hasTable('ts_realisasi_performance_daily')) {
            Schema::table('ts_realisasi_performance_daily', function (Blueprint $table) {
                $table->dropColumn(['rate_gross', 'rate_netto']);
            });
        }
    }
}
