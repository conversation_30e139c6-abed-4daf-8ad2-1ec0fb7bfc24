<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMParameterTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_parameter', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('id_parameter', 30)->unique();
            $table->string('nama_parameter', 100)->unique();
            $table->string('create_by')->nullable();
            $table->string('update_by')->nullable();
            $table->string('deleted_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        DB::table('m_parameter')->insert([
            ['id_parameter' => 'OPH', 'nama_parameter' => 'OPH'],
            ['id_parameter' => 'UPDT', 'nama_parameter' => 'UPDT'],
            ['id_parameter' => 'PDT', 'nama_parameter' => 'PDT'],
            ['id_parameter' => 'STOP IDLE', 'nama_parameter' => 'STOP IDLE'],
            ['id_parameter' => 'FREK UPDT', 'nama_parameter' => 'FREK UPDT'],
            ['id_parameter' => 'PROD. VOL', 'nama_parameter' => 'PROD. VOL'],
            ['id_parameter' => 'BDP RATE', 'nama_parameter' => 'BDP RATE'],            
            ['id_parameter' => 'PROD. RATE', 'nama_parameter' => 'PROD. RATE'],
        ]);

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('m_parameter');
    }
}
