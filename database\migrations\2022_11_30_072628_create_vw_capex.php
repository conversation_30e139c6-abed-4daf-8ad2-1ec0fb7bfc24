<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateVwCapex extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // First check if holding column exists, if not add it
        if (!Schema::hasColumn('m_opco', 'holding')) {
            Schema::table('m_opco', function (Blueprint $table) {
                $table->string('holding', 50)->nullable();
            });
        }

        DB::statement('
            CREATE OR REPLACE VIEW vw_capex AS 
            SELECT op.no_pbi,
                   COALESCE(op.holding, \'\') as holding,
                   rcd.kode_opco,
                   CASE
                       WHEN rcd.tahun IS NULL THEN NULL::text
                       WHEN concat(rcd.bulan, \'-\', rcd.tahun) = to_char(now(), \'FMMM-YYYY\'::text) THEN to_char(now(), \'YYYY-MM-DD\'::text)
                       ELSE concat(to_char(concat(rcd.tahun, \'-\', rcd.bulan, \'-01\')::date::timestamp with time zone, \'YYYY-MM\'::text), \'-\', to_char((date_trunc(\'month\'::text, concat(rcd.tahun, \'-\', rcd.bulan, \'-01\')::date::timestamp with time zone) + \'1 mon -1 days\'::interval)::date::timestamp with time zone, \'DD\'::text))
                   END AS tanggal,
                   CASE
                       WHEN rcd.tahun IS NULL THEN NULL::double precision
                       WHEN concat(rcd.bulan, \'-\', rcd.tahun) = to_char(now(), \'FMMM-YYYY\'::text) THEN rcd.rkap_capex::double precision * round((to_char(now(), \'DD\'::text)::double precision / to_char((date_trunc(\'month\'::text, now()) + \'1 mon -1 days\'::interval)::date::timestamp with time zone, \'DD\'::text)::double precision)::numeric, 2)::double precision
                       ELSE rcd.rkap_capex::double precision
                   END AS rkap_capex,
                   COALESCE(trc.real_capex, 0::bigint::double precision) AS real_capex
            FROM m_opco op
                LEFT JOIN rkap_capex rcd ON rcd.kode_opco::text = op.kode_opco::text
                LEFT JOIN ts_realisasi_capex trc ON rcd.kode_opco::text = trc.kode_opco::text AND concat(rcd.bulan, \'-\', rcd.tahun) = to_char(trc.tanggal::timestamp with time zone, \'FMMM-YYYY\'::text)
            ORDER BY (
                CASE
                    WHEN rcd.tahun IS NULL THEN NULL::text
                    ELSE to_char(concat(\'01-\', rcd.bulan, \'-\', rcd.tahun)::date::timestamp with time zone, \'DD-MM-YYYY\'::text)
                END), op.no_pbi
        ');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::statement('DROP VIEW IF EXISTS vw_capex');
    }
}
