<?php

namespace App\Services;

use App\Models\Kiln_Stop;
use App\Models\KilnOutput;
use App\Models\KilnPlant;
use App\Models\KilnRate;
use App\Models\Opc_TIS;
use App\Models\RealisasiProduksi;
use App\Models\SyncOpcConfig;
use App\Models\SyncOpcLog;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Support\Facades\DB;

class SyncOpcClientService
{
    private function getData($config, $test = false)
    {
        $id = 0;
        if (!$test) {
            $id = $config->id;
        }
        $model = SyncOpcLog::create([
            'sync_opc_config_id' => $id,
            'config_name' => $config->name,
            'year' => date('Y'),
            'month' => date('m'),
            'status' => 'process',
            'url' => $config->url,
            'parameter' => $config->parameter,
        ]);

        $response = $this->reqApi($config);

        $status = 'success';
        if ($response['status'] == 'fail') {
            $status = 'fail';
            $model->note = substr($response['message'], 0, 190);
        } elseif ($test) {
            $model->note = "Test Run";
        }

        $model->status = $status;
        $model->save();

        return $response;
    }

    private function reqApi($config)
    {
        $client = new Client();
        //manually get tag reference opc output SP6
        $plantSP6 = KilnPlant::select('reference_opc_output')->where('kode_plant', 'SP6')->first();

        try {
            // echo "prop : ".property_exists($config,'type').property_exists($config,'parameter').property_exists($config,'url');
            // if(property_exists($config,'type') && property_exists($config,'parameter') && property_exists($config,'url')){
            $username = 'alamin.ibad';
            $password = 'oprex2024';
            $type = $config->type;
            $parameter = trim(preg_replace('/\s+/', ' ', $config->parameter));
            $url = $config->url . "" . $parameter;
            $response = $client->request("GET", $url, [
                'debug' => false,
                'headers' => [
                    'uid' => 'f925888cab56821b-1d246b9d-de013a58',
                ]
            ]);
            $content = json_decode(str_replace(");", "", str_replace("(", "", $response->getBody()->getContents())));
            $data = [];
            if ($type == 'api_opc_client') {
                if (property_exists($content, 'tags')) {
                    $data = $content->tags;
                } else {
                    throw new Exception("tags not found");
                }
            } elseif ($type == 'api_opc_sp') {
                $data = $content->variables;
                //condition SP6 manually add tag and value
                if ($config->name == 'OPC Semen Padang - Kiln Output') {
                    $data[4] = (object)[$plantSP6->reference_opc_output => $content->values[4]];
                }
            }
            $this->store($data, $type, $config->kode_opco);
            return [
                'status' => 'success',
                'message' => 'Successfully synced and saved data',
                'test' => $content,
                'url' => $url,
                'config' => $config,
            ];
            // }else{
            //     throw new Exception("config cant be executed".property_exists($config,'type').property_exists($config,'parameter').property_exists($config,'url'));
            // }
        } catch (ClientException $e) {
            $message = $e->getResponse()->getReasonPhrase();
            return [
                'status' => 'fail',
                'message' => $message,
                'test1' => 'x',
                'config' => $config,
            ];
        } catch (Exception $e) {
            return [
                'status' => 'fail',
                'message' => $e->getMessage(),
                'test2' => 'x2',
                'config' => $config,
            ];
        }
    }

    private function store($tags, $type, $kodeOpco)
    {
        // start debuging

        // $tags = [
        //         0 => (object)["SMBR1.status"=>0]
        //     ];
        // $kodeOpco = 'SMBR';
        // $type = 'api_opc_smbr';
        // // $createdate = date_create("2022-12-27 13:00:00");//hidup
        // // $createdate = date_create("2022-12-27 16:00:00");//hidup
        // // $createdate = date_create("2022-12-27 20:00:00");//mati
        // // $createdate = date_create("2022-12-28 13:00:00");//mati
        // // $createdate = date_create("2022-12-29 16:00:00");//hidup
        // // $createdate = date_create("2022-12-30 01:00:00");//hidup
        // // $createdate = date_create("2022-12-31 10:00:00");//mati

        // $createdate = date_create("2023-12-01 10:00:00");//mati

        // // $createdate = date_create("2023-12-12 11:00:00");//hidup
        // // $createdate = date_create("2023-12-12 15:00:00");//mati
        // // $createdate = date_create("2023-12-12 19:00:00");//mati
        // // $createdate = date_create("2023-12-12 22:00:00");//hidup

        // // $createdate = date_create("2023-12-13 01:00:00");//mati
        // // $createdate = date_create("2023-12-13 09:00:00");//mati
        // // $createdate = date_create("2023-12-13 20:00:00");//mati

        // // $createdate = date_create("2023-12-15 01:00:00");//mati
        // // $createdate = date_create("2023-12-17 01:00:00");//mati
        // // $createdate = date_create("2023-12-13 05:00:00");//hidup
        // // $createdate = date_create("2023-12-13 06:00:00");//mati
        // // $createdate = date_create("2023-12-14 07:00:00");//hidup
        // // $createdate = date_create("2023-12-14 08:00:00");//hidup
        // // $createdate = date_create("2023-12-15 09:00:00");//hidup
        // $date_nows = date_format($createdate,"Y-m-d");
        // $lastdate = Carbon::createFromFormat('Y-m-d', $date_nows)->subDays(1)->format('Y-m-d');//hidup
        // $date = date_format($createdate,"Y-m-d H:i:s");
        // $date_end = date_format($createdate,"Y-m-d 00:00:00");
        // $date_now = date_format($createdate,"Ymd");
        // $date_year = date_format($createdate,"Y");

        // end debuging

        $references = $this->getReference($kodeOpco);
        $date = date("Y-m-d H:i:s");
        $date_now = date("Ymd");
        $date_nows = date("Y-m-d");
        $date_end = date("Y-m-d 00:00:00");
        $lastdate = Carbon::createFromFormat('Y-m-d', $date_nows)->subDays(1)->format('Y-m-d');
        $date_year = date("Y");
        try {
            DB::beginTransaction();
            $kilnPlant = KilnPlant::select('kode_opco', 'kode_plant')->get();
            foreach ($tags as $tag) {
                $name = $this->getName($tag, $type);
                if (in_array(strtolower($name), array_keys($references['status']))) {
                    $plant = $references['status'][$name];
                    $lastOpc = Opc_TIS::where(['kode_plant' => $plant, 'source_system' => 'OPC'])->orderBy('id_opc_tis', 'desc')->first();
                    $status = $this->getStatus($tag, $type);
                    if ($status === "fail") { //skip jaringan issue
                        continue;
                    }
                    // jika ada data sebelumnya
                    if ($lastOpc) {
                        // dihari yang sama
                        if (Carbon::parse($lastOpc->tanggal)->format('Ymd') == $date_now) {

                            if ($lastOpc && $lastOpc->status == $status) {
                                // ketika kondisi sebelumnya sama dengan kondisi saat ini contoh : hidup - hidup / mati - mati
                                Opc_TIS::find($lastOpc->id_opc_tis)->update(['update_by' => 'OPC_' . $plant, 'updated_at' => date('Y-m-d H:i:s')]);
                            } else {
                                //Ketika di OPC TIS last status sebelumnya tidak sama dgn yang baru (mati atau hidup kiln status) sama hari
                                $data = [
                                    'kode_plant' => $plant,
                                    'tanggal' => $date,
                                    'status' => $status,
                                    'source_system' => 'OPC',
                                    'create_by' => 'OPC_' . $plant
                                ];
                                Opc_TIS::create($data);
                                // ketika kondisi sebelumnya berbeda dengan kondisi saat ini contoh : mati - hidup / hidup - mati
                                if ($lastOpc->status == 0) {
                                    // ketika kondisi status mati ke hidup
                                    //Mencari Data Kiln Stop di Hari yang sama , yang Tanggal_selesai Null
                                    $lastKilnStop = Kiln_Stop::where('kode_plant', $plant)
                                        ->where(DB::raw("to_char(tanggal_mulai, 'YYYY-MM-DD')"), $date_nows)
                                        ->whereNull('tanggal_selesai')
                                        ->where('source_system', 'OPC')
                                        ->orderBy('tanggal_mulai', 'desc')
                                        ->first();

                                    $updateData['tanggal_selesai'] = $date;
                                    if ($lastKilnStop != NULL) {
                                        $lastKilnStop->update($updateData);
                                    }
                                } else {
                                    // ketika kondisi status hidup ke mati
                                    Kiln_Stop::create([
                                        'kode_plant' => $plant,
                                        'tanggal_mulai' => $date,
                                        'source_system' => 'OPC',
                                        'create_by' => "OPC_" . $plant
                                    ]);
                                }
                            }
                        } else {
                            // dihari yang berbeda
                            if ($lastOpc && $lastOpc->status == $status) {
                                //Ketika di status sama mati ke mati
                                if ($status == 0) {
                                    $data = [
                                        'kode_plant' => $plant,
                                        'tanggal' => $date,
                                        'status' => $status,
                                        'source_system' => 'OPC',
                                        'create_by' => 'OPC_' . $plant
                                    ];
                                    Opc_TIS::create($data);
                                    // DB::enableQueryLog();
                                    $lastKilnStop = Kiln_Stop::where('kode_plant', $plant)
                                        ->where(DB::raw("to_char(tanggal_mulai, 'YYYY-MM-DD')"), $lastdate)
                                        ->whereNull('tanggal_selesai')
                                        ->where('source_system', 'OPC')
                                        ->orderBy('tanggal_mulai', 'desc')
                                        ->first();
                                    // dd(DB::getQueryLog());
                                    $updateData['tanggal_selesai'] = $date_end;
                                    if ($lastKilnStop != NULL) {
                                        $lastKilnStop->update($updateData);
                                    }

                                    Kiln_Stop::create([
                                        'kode_plant' => $plant,
                                        'tanggal_mulai' => $date,
                                        'source_system' => 'OPC',
                                        'create_by' => "OPC_" . $plant
                                    ]);
                                } else {
                                    //Ketika di status sama hidup ke hidup
                                    $data = [
                                        'kode_plant' => $plant,
                                        'tanggal' => $date,
                                        'status' => $status,
                                        'source_system' => 'OPC',
                                        'create_by' => 'OPC_' . $plant
                                    ];
                                    Opc_TIS::create($data);
                                }
                            } else {
                                // ketika di status mati ke hidup
                                if ($status == 1) {
                                    //Ketika di OPC TIS last status sebelumnya tidak sama dgn yang baru (mati atau hidup kiln status) di beda hari
                                    $data = [
                                        'kode_plant' => $plant,
                                        'tanggal' => $date,
                                        'status' => $status,
                                        'source_system' => 'OPC',
                                        'create_by' => 'OPC_' . $plant
                                    ];
                                    Opc_TIS::create($data);

                                    $lastKilnStop = Kiln_Stop::where('kode_plant', $plant)
                                        // ->where(DB::raw("to_char(tanggal_mulai, 'YYYY-MM-DD')"), $lastdate)
                                        ->whereNull('tanggal_selesai')
                                        ->where('source_system', 'OPC')
                                        ->orderBy('tanggal_mulai', 'desc')
                                        ->first();

                                    $updateData['tanggal_selesai'] = DB::raw("TO_CHAR(tanggal_mulai::timestamp, 'YYYY-MM-DD')::date + INTERVAL '1 day'");
                                    if ($lastKilnStop != NULL) {
                                        $lastKilnStop->update($updateData);
                                    }

                                    Kiln_Stop::create([
                                        'kode_plant' => $plant,
                                        'tanggal_mulai' => $date_end,
                                        'tanggal_selesai' => $date,
                                        'source_system' => 'OPC',
                                        'create_by' => "OPC_" . $plant
                                    ]);
                                } else {
                                    // ketika di status hidup ke mati
                                    $data = [
                                        'kode_plant' => $plant,
                                        'tanggal' => $date,
                                        'status' => $status,
                                        'source_system' => 'OPC',
                                        'create_by' => 'OPC_' . $plant
                                    ];
                                    Opc_TIS::create($data);

                                    Kiln_Stop::create([
                                        'kode_plant' => $plant,
                                        'tanggal_mulai' => $date,
                                        'source_system' => 'OPC',
                                        'create_by' => "OPC_" . $plant
                                    ]);
                                }
                            }
                        }
                    } else {
                        // jika data sebelumnya belum ada
                        // dengan status mati
                        if ($status == 0) {
                            $data = [
                                'kode_plant' => $plant,
                                'tanggal' => $date,
                                'status' => $status,
                                'source_system' => 'OPC',
                                'create_by' => 'OPC_' . $plant
                            ];
                            Opc_TIS::create($data);

                            Kiln_Stop::create([
                                'kode_plant' => $plant,
                                'tanggal_mulai' => $date,
                                'source_system' => 'OPC',
                                'create_by' => "OPC_" . $plant
                            ]);
                        } else {
                            // dengan status hidup
                            $data = [
                                'kode_plant' => $plant,
                                'tanggal' => $date,
                                'status' => $status,
                                'source_system' => 'OPC',
                                'create_by' => 'OPC_' . $plant
                            ];
                            Opc_TIS::create($data);
                        }
                    }
                } elseif (in_array(strtolower($name), array_keys($references['rate']))) {
                    $plant = $references['rate'][$name];
                    $kilnRate = $this->getRate($tag, $type);
                    if ($kilnRate === "fail") { //skip jaringan issue
                        continue;
                    }
                    KilnRate::create([
                        'kode_plant' => $plant,
                        'tanggal' => $date,
                        'kiln_rate' => $kilnRate,
                        'source_system' => 'OPC',
                        'create_by' => "OPC_" . $plant
                    ]);
                } elseif (in_array(strtolower($name), array_keys($references['output']))) {
                    $plant = $references['output'][$name];
                    $ystd = date("Y-m-d", strtotime("-1 days"));
                    $ystd_hour = date("Y-m-d H:i:s", strtotime("-1 days"));

                    $output = $this->getOutput($tag, $type);
                    if ($output === "fail") { //skip jaringan issue
                        continue;
                    }
                    $opco = $kilnPlant->where('kode_plant', $plant)->first()->kode_opco;
                    $lastKilnOutput = KilnOutput::where('kode_plant', $plant)->where(DB::raw("to_char(tanggal, 'YYYY-MM-DD')"), $ystd)
                        ->where('source_system', 'OPC')
                        ->orderBy('id_kiln_output', 'desc')
                        ->first();
                    if (!$lastKilnOutput) {
                        KilnOutput::create([
                            'kode_plant' => $plant,
                            'tanggal' => $ystd_hour,
                            'produksi_output' => intval($output),
                            'source_system' => 'OPC',
                            'create_by' => "OPC_" . $plant,
                            'kode_opco' => $opco
                        ]);
                    } else {
                        $lastKilnOutput->update(['produksi_output' => intval($output), 'update_by' => "OPC_" . $plant, 'kode_opco' => $opco]);
                    }

                    $sumKilnOutput = KilnOutput::where('kode_opco', $opco)->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), date("Y-m"))
                        ->select('kode_opco', DB::raw('SUM(produksi_output) as prod_output'))
                        ->groupBy('kode_opco')
                        ->first();

                    $lastRealProd = RealisasiProduksi::where('kode_opco', $opco)->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), date("Y-m"))->first();
                    $sumProdOuput = 0;
                    if ($sumKilnOutput) {
                        $sumProdOuput = $sumKilnOutput->prod_output;
                    }
                    if (!$lastRealProd) {
                        RealisasiProduksi::create([
                            'tanggal' => $date,
                            'prod_klinker' => 0,
                            'prod_semen' => 0,
                            'clinker_sold' => 0,
                            'prod_output' => $sumProdOuput,
                            'ics' => 0,
                            'kode_opco' => $opco
                        ]);
                    } else {
                        $lastRealProd->update(['prod_output' => $sumProdOuput, 'tanggal' => $date]);
                    }
                }
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e->getMessage());
        }
    }

    private function getReference($kodeOpco)
    {
        $references = KilnPlant::where(['kode_opco' => $kodeOpco])->get();
        $result = [
            'status' => [],
            'rate' => [],
            'output' => []
        ];
        foreach ($references as $reference) {
            $result['status'][strtolower($reference['reference_opc_status'])] = $reference['kode_plant'];
            $result['rate'][strtolower($reference['reference_opc_rate'])] = $reference['kode_plant'];
            $result['output'][strtolower($reference['reference_opc_output'])] = $reference['kode_plant'];
        }

        return $result;
    }

    private function getName($tag, $type)
    {
        $name = '';
        if ($type == 'api_opc_client') {
            $name = $tag->name;
        } elseif ($type == 'api_opc_sp' || $type == 'api_opc_smbr') {
            $keys = array_keys((array)$tag);
            $name = implode('+', $keys);
        }

        return strtolower($name);
    }

    private function getStatus($data, $type)
    {
        $value = '';
        if ($type == 'api_opc_client') {
            //Cek apakah Gangguan jaringan : tidak ada val
            if (data_get($data->props[0], 'val', 'fail') == 'fail') {
                return "fail";
            } else {
                $value = strtolower(data_get($data->props[0], 'val', 'false')) == 'true' ? 1 : 0;
            }
        } elseif ($type == 'api_opc_sp' || $type == 'api_opc_smbr') {
            $value = array_sum((array)$data);
        }

        return $value;
    }

    private function getRate($data, $type)
    {
        $value = '';
        if ($type == 'api_opc_client') {
            //Cek apakah Gangguan jaringan : tidak ada val
            if (data_get($data->props[0], 'val', 'fail') == 'fail') {
                return "fail";
            } else {
                $value = data_get($data->props[0], 'val', 0);
            }
        } elseif ($type == 'api_opc_sp') {
            $value = array_sum((array)$data);
        }

        return $value;
    }

    private function getOutput($data, $type)
    {
        $value = '';
        if ($type == 'api_opc_client') {
            //Cek apakah Gangguan jaringan : tidak ada val
            if (data_get($data->props[0], 'val', 'fail') == 'fail') {
                return "fail";
            } else {
                $value = round(floatval(str_replace(',', '.', data_get($data->props[0], 'val', 0))), 2);
            }
        } elseif ($type == 'api_opc_sp') {
            $value = array_sum((array)$data);
        }

        return $value;
    }

    public function execute($id, $type, $config = null)
    {
        if (!$config) {
            $config = SyncOpcConfig::where(['id' => $id, 'type' => $type, 'status' => 'active'])->first();
            return $this->getData($config);
        }

        return $this->getData($config, true);
    }
}
