<?php

namespace App\Rules;

use App\Models\DmmDataPenilaianOpco;
use App\Models\PenilaianOpco;
use Illuminate\Contracts\Validation\Rule;

class storeAssessmentRules implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public $message = 'Periode Penilaian Opco yang Dipilih Belum Tersedia atau Sedang Tidak Aktif';
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        //
        $penilaianOpco = PenilaianOpco::where($value)->where('status','y')->first();
        if(!$penilaianOpco){
            return false;
        }
        $existPenilaian = DmmDataPenilaianOpco::where('penilaian_opco_id',$penilaianOpco->id)
        ->where('created_by',auth()->user()->id)
        ->first();
        if($existPenilaian){
            $this->message = 'Anda Sudah Membuat Penilaian Opco Ini, Silahkan Perik<PERSON>';
            return false;
        }

        if(date('Y-m-d') >= $penilaianOpco->start_date && date('Y-m-d') <= $penilaianOpco->end_date){
            return true;
        }else if(date('Y-m-d') < $penilaianOpco->start_date){
            $this->message = 'Periode Penilaian Opco yang Dipilih Belum Dimulai';
            return false;
        }else if(date('Y-m-d') >= $penilaianOpco->end_date){
            $this->message = 'Periode Penilaian Opco yang Dipilih Sudah Berakhir';
            return false;
        }
        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->message;
    }
}
