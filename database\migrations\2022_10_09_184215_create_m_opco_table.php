<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMOpcoTable extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('m_opco')) {
            Schema::create('m_opco', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('kode_opco', 30);
                $table->string('nama_opco', 100);
                $table->string('holding', 30);
                $table->string('reference_sap', 30)->nullable();
                $table->string('status')->nullable();
                $table->string('create_by')->nullable();
                $table->string('update_by')->nullable();
                $table->string('deleted_by', 30)->nullable();
                $table->softDeletes();
                $table->timestamps();
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('m_opco');
    }
}
