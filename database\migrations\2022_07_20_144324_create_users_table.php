<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Expression;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->uuid('id')->nullable()->default(new Expression('(uuid_generate_v1())'));
            $table->string('username')->nullable()->unique();
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->char('status',1)->default('1');
            $table->string('mk_nopeg')->nullable();
            $table->string('mk_nama')->nullable();
            $table->string('company')->nullable();
            $table->string('company_default')->nullable();
            $table->unsignedBigInteger('costcenter')->nullable();
            $table->unsignedBigInteger('costcenter2')->nullable();
            $table->unsignedBigInteger('costcenter_default')->nullable();
            $table->string('created_by')->nullable();
            $table->string('updated_by')->nullable();
            $table->string('online')->nullable();
            $table->string('visit')->nullable();
            $table->string('photo')->nullable();
            $table->string('reset')->nullable();
            $table->string('key_devices')->nullable();
            $table->rememberToken();
            $table->date('deleted_at')->nullable();
            $table->timestamps();


        });
	
/*MYSQL
 DB::unprepared("            
            CREATE TRIGGER before_insert_users
            BEFORE INSERT ON users
            FOR EACH ROW
            BEGIN
              IF new.id IS NULL THEN
                SET new.id = uuid();
              END IF;
            END");


*/
/*POSTGREE
         DB::unprepared('
            CREATE FUNCTION proc_insert_user() RETURNS trigger AS $proc_insert_user$
             BEGIN
              IF new.id IS NULL THEN
                SET new.id = uuid_generate_v1();
              END IF;
            END;
            $proc_insert_user$ LANGUAGE plpgsql;
            ');
 
  DB::unprepared("
            CREATE TRIGGER before_insert_users
            BEFORE INSERT ON users
            FOR EACH ROW EXECUTE PROCEDURE proc_insert_user();
           ");*/
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
//        DB::unprepared('DROP TRIGGER [IF EXISTS] `before_insert_users`');
        Schema::dropIfExists('users');
    }
}
