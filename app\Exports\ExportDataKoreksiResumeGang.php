<?php

namespace App\Exports;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use DB;

class ExportDataKoreksiResumeGang implements WithColumnFormatting, WithEvents, FromCollection, WithHeadings, WithTitle, WithStyles
{

  /**
   * @return \Illuminate\Support\Collection
   */
  public function  __construct($data)
  {
    $this->data = $data;
  }
  /**
   * @return string
   */
  public function title(): string
  {
    return 'Koreksi Resume Gang';
  }

  public function headings(): array
  {
    return [
      'Kode Plant',
      'Tanggal',
      'Problem',
      'Oph',
      'Updt',
      'Pdt',
      'Stop idle',
      'Frek updt',
    ];
  }

  public function collection()
  {
    return collect($this->data);
  }

  public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_DDMMYYYY,
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                //set background color for header (A1:D1)
                $event->sheet->getDelegate()
                ->getStyle('A1:H1')
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('C4D79B');

                //set header (A1:D1) uneditable and set workspace (A2:D1000) editable
                // $event->sheet->protectCells('A1:D1', 'PASSWORD');
                // $event->sheet->getStyle('A2:D1000')->getProtection()->setLocked(\PhpOffice\PhpSpreadsheet\Style\Protection::PROTECTION_UNPROTECTED);
                // $event->sheet->getDelegate()->getProtection()->setSheet(true);

            },
        ];
    }

  //set width for header
  public function columnWidths(): array
  {
    return [
      'A' => 15,
      'B' => 15,
      'C' => 15,
      'D' => 15,
      'E' => 15,
      'F' => 15,
      'G' => 15,
      'H' => 15
    ];
  }

  //set bold for header
  public function styles(Worksheet $sheet)
  {
    return [
      'A1' => ['font' => ['bold' => true]],
      'B1' => ['font' => ['bold' => true]],
      'C1' => ['font' => ['bold' => true]],
      'D1' => ['font' => ['bold' => true]],
      'E1' => ['font' => ['bold' => true]],
      'F1' => ['font' => ['bold' => true]],
      'G1' => ['font' => ['bold' => true]],
      'H1' => ['font' => ['bold' => true]]
    ];
  }
}
