<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ModifyXsrfCookieAttributes
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        foreach ($response->headers->getCookies() as $cookie) {
            $response->headers->setCookie(
                cookie(
                    $cookie->getName(),
                    $cookie->getValue(),
                    $cookie->getExpiresTime(),
                    $cookie->getPath(),
                    $cookie->getDomain(),
                    config('session.secure'),
                    true,
                    false,
                    'lax'
                )
            );
        }

        return $response;
    }
}
