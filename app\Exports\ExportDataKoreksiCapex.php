<?php

namespace App\Exports;

use App\Models\Area;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\Models\ItemInspection;
use DB;

class ExportDataKoreksiCapex implements WithColumnFormatting, WithEvents, FromCollection, WithHeadings, WithTitle, WithStyles
{

    /**
     * @return \Illuminate\Support\Collection
     */


    public function  __construct($opco, $tahun, $bulan)
    {
        $this->opco = $opco;
        $this->tahun = $tahun;
        $this->bulan = $bulan;
    }


    /**
     * @return string
     */
    public function title(): string
    {
        return 'Data Export Koreksi Capex';
    }

    public function headings():array
     {
         return [
            'Kode OPCO',
            'Holding',
            'Tanggal',
            'RKAP Capex',
            'Real Capex',
         ];
     }

    public function collection()
    {
        $result =  DB::table('vw_tk_capex')
            ->select('kode_opco','holding','tanggal','rkap_capex','real_capex');
        if($this->opco){
            $result = $result -> where('kode_opco', $this->opco);
        }
        if($this->tahun){
            $result = $result -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $this->tahun);
        }
        if($this->bulan){
            $result = $result -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $this->bulan);
        }
        $result = $result->get();
        return $result;
    }

    public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_DATE_DDMMYYYY,
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                //set background color for header (A1:D1)
                $event->sheet->getDelegate()
                ->getStyle('A1:E1')
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('C4D79B');

                //set header (A1:D1) uneditable and set workspace (A2:D1000) editable
                // $event->sheet->protectCells('A1:D1', 'PASSWORD');
                // $event->sheet->getStyle('A2:D1000')->getProtection()->setLocked(\PhpOffice\PhpSpreadsheet\Style\Protection::PROTECTION_UNPROTECTED);
                // $event->sheet->getDelegate()->getProtection()->setSheet(true);

            },
        ];
    }

     //set bold for header
     public function styles(Worksheet $sheet)
     {
         return [
             'A1'    => ['font' => ['bold' => true]],
             'B1'    => ['font' => ['bold' => true]],
             'C1'    => ['font' => ['bold' => true]],
             'D1'    => ['font' => ['bold' => true]],
             'E1'    => ['font' => ['bold' => true]],
         ];
     }
}
