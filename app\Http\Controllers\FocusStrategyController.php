<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\FocusStrategyRequest;
use DataTables;
use App\Models\Menu;
use App\Models\Routes;
use App\Models\FocusStrategy;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class FocusStrategyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();

        return view('FocusStrategy', $data);
    }

    public function datatables(Request $request)
    {
        $query    = FocusStrategy::get();
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $focusstrategy = FocusStrategy::create([
            'kode' => $request->kode,
            'focus_strategy' => $request->focus_strategy,
        ]);

        $response = responseSuccess(trans('message.read-success'),$focusstrategy);
        // return $response;
        return response()->json($response,200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $query   = FocusStrategy::find($focusstrategy);
        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $query   = FocusStrategy::find($id);
        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $data = $this->findDataWhere(FocusStrategy::class, ['id' => $id]);

        //   dd($data);exit();
        DB::beginTransaction();
        try {
            $data->update([
                    'kode' => $request->kode,
                    'focus_strategy' => $request->focus_strategy,
                ]);
                DB::commit();
                $response = responseSuccess(trans("messages.update-success"), $data);
                return response()->json($response, 200, [], JSON_PRETTY_PRINT);
            } catch (Exception $e) {
                DB::rollback();
                $response = responseFail(trans("messages.update-fail"), $e->getMessage());
                return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        FocusStrategy::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }
}
