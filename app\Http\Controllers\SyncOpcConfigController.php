<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\SyncOpcConfig;
use App\Services\SyncOpcClientService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class SyncOpcConfigController extends Controller
{
    public function datatable(Request $request)
    {
        $query    = SyncOpcConfig::get();

        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|max:255|unique:sync_opc_configs,name',
            'type' => ['required', Rule::in(array_keys(SyncOpcConfig::apiTypes()))],
            'url' => 'required|max:255',
            'parameter' => 'required',
            'kode_opco' => 'required',
            'schedule' => ['required', Rule::in(array_keys(SyncOpcConfig::schedules()))],
            'at_date' => Rule::requiredIf($request->input('schedule') == 'monthly'),
            'at_time' => Rule::requiredIf(in_array($request->input('schedule'), ['daily', 'monthly'])),
            'status' => ['required', Rule::in(array_keys(SyncOpcConfig::statuses()))],
        ]);

        try {
            $model = SyncOpcConfig::create($request->all());

            $response = responseSuccess(trans('messages.create-success'), $model);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function show($id)
    {
        $model = SyncOpcConfig::findOrFail($id);

        $response = responseSuccess(trans('messages.read-success'), $model);
        return response()->json($response, 200);
    }

    public function update(Request $request, $id)
    {
        $model = SyncOpcConfig::findOrFail($id);

        $request->validate([
            'name' => 'required|max:255|unique:sync_opc_configs,name,' . $id . ',id',
            'type' => ['required', Rule::in(array_keys(SyncOpcConfig::apiTypes()))],
            'url' => 'required|max:255',
            'parameter' => 'required',
            'kode_opco' => 'required',
            'schedule' => ['required', Rule::in(array_keys(SyncOpcConfig::schedules()))],
            'at_date' => Rule::requiredIf($request->input('schedule') == 'monthly'),
            'at_time' => Rule::requiredIf(in_array($request->input('schedule'), ['daily', 'monthly'])),
            'status' => ['required', Rule::in(array_keys(SyncOpcConfig::statuses()))],
        ]);
        
        // updated by dani (untuk problem gagal update if date n time null/kosong )
        if ($request['at_date']=='' ) {
            $request->request->remove('at_date');
            $request->merge([
                'at_date' =>null,
            ]);
        }
        if ($request['at_time']=='' ) {
            $request->request->remove('at_time');
            $request->merge([
                'at_time' =>null,
            ]);
        }

        try {
            $model->update($request->all());
            $response = responseSuccess(trans('messages.update-success'), $model);
            return response()->json($response, 200);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.update-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function destroy($id)
    {
        $model = SyncOpcConfig::findOrFail($id);

        try {
            $model->delete();
            $response = responseSuccess(trans('messages.delete-success'), $model);
            return response()->json($response, 200);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.delete-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function testSync(Request $request)
    {
        $config = (object) $request->all();
        $response = (new SyncOpcClientService)->execute(null, null, $config);
        if ($response['status'] == 'fail') {
            return response()->json($response, 500);
        }
        return response()->json($response, 200);
    }
}
