<?php

namespace App\Exports;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Events\AfterSheet;
use DB;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithEvents;

class ExportDataKoreksiPerformance implements FromCollection, WithHeadings, WithTitle, WithStyles, WithColumnFormatting, WithEvents
{

  /**
   * @return \Illuminate\Support\Collection
   */
  public function  __construct($data)
  {
    $this->data = $data;
  }
  /**
   * @return string
   */
  public function title(): string
  {
    return 'Koreksi Performance';
  }

  public function headings(): array
  {
    return [
    'Opco'                      ,
    'Plant'                     ,
    'Tanggal'                   ,
    'Op. H'                     ,
    'UPDT'                      ,
    'PDT'                       ,
    'Stop IDLE'                 ,
    'FY. STOP'                  ,
    'net.avail'                 ,
    'Koreksi'                   ,
    'Act. Prod. (ton)'          ,
    'Frek updt'                 ,
    'Act. Idle Prod. (ton)'     ,
    'Rate gross (ton/day)'      ,
    'Rate netto (ton/day)'      ,
    'Cal'                       ,
    'RKAP Op.'                  ,
    'RKAP UPDT'                 ,
    'RKAP PTD'                  ,
    'RKAP IDLE'                 ,
    'RKAP Frek updt'            ,
    'RKAP Prod. (ton)'          ,
    'BDP (ton/day)'             ,
    'RKAP (ton/day)'            ,
    ];
  }

  public function collection()
  {
    return collect($this->data);
  }

  public function columnFormats(): array
    {
        return [
            'C' => NumberFormat::FORMAT_DATE_DDMMYYYY,
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                //set background color for header (A1:D1)
                $event->sheet->getDelegate()
                ->getStyle('A1:X1')
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('C4D79B');

                //set header (A1:D1) uneditable and set workspace (A2:D1000) editable
                // $event->sheet->protectCells('A1:D1', 'PASSWORD');
                // $event->sheet->getStyle('A2:D1000')->getProtection()->setLocked(\PhpOffice\PhpSpreadsheet\Style\Protection::PROTECTION_UNPROTECTED);
                // $event->sheet->getDelegate()->getProtection()->setSheet(true);

            },
        ];
    }


  //set width for header
  public function columnWidths(): array
  {
    return [
      'A' => 15,
      'B' => 15,
      'C' => 15,
      'D' => 15,
      'E' => 15,
      'F' => 15,
      'G' => 15,
      'H' => 15,
      'I' => 15,
      'J' => 15,
      'K' => 15,
      'L' => 15,
      'M' => 15,
      'N' => 15,
      'O' => 15,
      'P' => 15,
      'Q' => 15,
      'R' => 15,
      'S' => 15,
      'T' => 15,
      'U' => 15,
      'V' => 15,
      'W' => 15,
      'X' => 15
    ];
  }

  //set bold for header
  public function styles(Worksheet $sheet)
  {
    return [
      'A1' => ['font' => ['bold' => true]],
      'B1' => ['font' => ['bold' => true]],
      'C1' => ['font' => ['bold' => true]],
      'D1' => ['font' => ['bold' => true]],
      'E1' => ['font' => ['bold' => true]],
      'F1' => ['font' => ['bold' => true]],
      'G1' => ['font' => ['bold' => true]],
      'H1' => ['font' => ['bold' => true]],
      'I1' => ['font' => ['bold' => true]],
      'J1' => ['font' => ['bold' => true]],
      'K1' => ['font' => ['bold' => true]],
      'L1' => ['font' => ['bold' => true]],
      'M1' => ['font' => ['bold' => true]],
      'N1' => ['font' => ['bold' => true]],
      'O1' => ['font' => ['bold' => true]],
      'P1' => ['font' => ['bold' => true]],
      'Q1' => ['font' => ['bold' => true]],
      'R1' => ['font' => ['bold' => true]],
      'S1' => ['font' => ['bold' => true]],
      'T1' => ['font' => ['bold' => true]],
      'U1' => ['font' => ['bold' => true]],
      'V1' => ['font' => ['bold' => true]],
      'W1' => ['font' => ['bold' => true]],
      'X1' => ['font' => ['bold' => true]]
    ];
  }
}
