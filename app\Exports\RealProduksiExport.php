<?php

namespace App\Exports;

use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class RealProduksiExport implements
    FromCollection,
    WithHeadings,
    WithEvents,
    WithColumnWidths,
    WithTitle,
    WithStyles
{
    protected $data;

    // Expot data with collection
    public function __construct($data)
    {
        $this->data = $data;
    }

    // Expot data with collection
    public function collection()
    {
        return collect($this->data);
    }

    public function headings(): array
    {
        return [
            'OPCO',
            'Tanggal',
            'Prod. Clinker',
            'Prod. Semen',
            'Clinker Sold',
            'Prod. Output',
            'ICS',
        ];
    }

    public function registerEvents(): array
    {
        return [

            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet
                    ->getDelegate()
                    ->getStyle('A1:G1')
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('C4D79B');
                $event->sheet->getDelegate()->freezePane('A2');
            },

        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 15,
            'B' => 15,
            'C' => 15,
            'D' => 15,
            'E' => 15,
            'F' => 15,
            'G' => 15,
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Data Realisasi Produksi';
    }

    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => [
                'font' => [
                    'name' => 'Times New Roman',
                    'bold' => true,
                ]
            ],
        ];
    }
}
