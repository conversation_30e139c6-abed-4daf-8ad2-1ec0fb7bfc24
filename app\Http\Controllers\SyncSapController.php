<?php

namespace App\Http\Controllers;
set_time_limit(0);
use App\Models\SampleSap;
use App\Models\CapexDetail;
use App\Models\Opco;
use App\Models\SyncSapConfig;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\SyncSapLog;
use App\Services\SAPRFCNEW;
use App\Services\SyncSapService;

class SyncSapController extends Controller
{
    public function index(Request $request)
    {
        $data = [
            'title' => 'DATA SAP',
            'breadcrumb' => [
                [
                    'title'=>'Sync Data',
                    'url'=>'/sync-sap',
                ],
                [
                    'title'=>'Data SAP',
                    'url'=>'',
                ]
            ],
        ];
        $data['types'] = SyncSapConfig::types();
        $data['schedules'] = SyncSapConfig::schedules();
        $data['statuses'] = SyncSapConfig::statuses();
        $data['configs'] = SyncSapConfig::get();
        return view('sync.sap', $data);
    }

    public function getDataSyncSap(Request $request)
    {
        $model = SyncSapLog::select(['sync_sap_logs.id', 'sync_sap_config_id', 'config_name', 'year', 'month', 'sync_sap_logs.created_at', 'sync_sap_logs.status', 'note']);

        if ($request->filter_data_sap_bulan) {
            $model->where('month', $request->filter_data_sap_bulan);
        }
        if ($request->filter_data_sap_tahun) {
            $model->where('year', $request->filter_data_sap_tahun);
        }
        if ($request->filter_data_sap) {
            $model->where('sync_sap_config_id', $request->filter_data_sap);
        }
        return DataTables::of($model)->make();
    }

    public function getFilterRfcSap(Request $request)
    {
        $filter_sap = SampleSap::select('id_rfc', 'rfc')->distinct('rfc')->get();
        return [
            'message' => 'Succes Get Data Filter Rfc',
            'data' => $filter_sap
        ];
    }
    public function startSync(Request $request)
    {
        //Monitor Sync SAP
        //Create or Update
        $monSap = SampleSap::updateOrCreate([
            'tahun' => $request->year,
            'bulan' => $request->month,
        'bulan' => $request->month,  
            'bulan' => $request->month,
            'rfc' => $request->rfc
        ], [
            'status' => 'Process',
        ]);
        //get Item Sync SAP
        $monSap = SampleSap::where([
            'tahun' => $request->year,
            'bulan' => $request->month,
        'bulan' => $request->month,  
            'bulan' => $request->month,
            'rfc' => $request->rfc
        ])->first();
        //call The RFC and Insert TO DB
        $result = (new SAPRFCNEW)->getData();
        // print_r($monSap);die;
        // $response = responseSuccess(trans("messages.update-success"), $result);
        // return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        if (!isset($result['messages'])) {
            //Get Master Company
            $Company = Opco::get();
            $mapComp = $Company->mapWithKeys(function ($item) {
                return [$item['kode_opco'] => $item['reference_sap']];
            })->toArray();
            DB::beginTransaction();
            try {
                foreach ($result as $k => $v) {
                    // print_r($v['REFBK']);
                    if (in_array($v['REFBK'], $mapComp)) {
                        $capexDetail = CapexDetail::updateOrCreate([
                            'kode_opco' => $v['REFBK'],
                            'no_billing' => $v['REFBN'],
                            'no_project' => $v['PSPID'],
                            'tanggal' =>  $v['BLDAT']
                        ], ['biaya_capex' => $v['WOGBTR']]);
                    }
                }
                $update_sap['status'] = 'Success';
                $monSap->update($update_sap);
                DB::commit();
                $response = responseSuccess(trans("messages.update-success"),  [count($result) . " Record"]);
                return response()->json($response, 200, [], JSON_PRETTY_PRINT);
            } catch (Exception $e) {
                DB::rollback();
                $update_sap['status'] = 'Fail - Error Insert Data';
                $monSap->update($update_sap);
                $response = responseFail(trans("messages.update-fail"), $e->getMessage());
                return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }
        }
        //error connection
        if (isset($result['messages']) && $result['messages'] == "Could not open connection") {
            $update_sap['status'] = 'Fail - Could not open connection';
            $monSap->update($update_sap);
            DB::commit();
            $response = responseFail($result['messages'], []);
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
        $response = responseSuccess(trans('message.read-success'), $monSap);
        return response()->json($response, 200);
    }

    public function reSync($id)
    {
        $log = SyncSapLog::findOrFail($id);
        $config = SyncSapConfig::findOrFail($log->sync_sap_config_id);
        $config->parameter = $log->parameter;
        $config->name = $log->config_name;
        $response = (new SyncSapService)->execute(null, null, null, $config);
        if ($response['status'] == 'fail') {
            return response()->json($response, 500);
        }
        return response()->json($response, 200);
    }

    public function TestSyncManual(Request $request)
    {
        // var_dump($request->id);
        $request->validate([
            'id' => 'required|exists:sync_sap_configs',
            'start_date' => 'required',
            'end_date' => 'required'
        ]);

        $start_date = str_replace("-", "", $request->start_date);
        $end_date = str_replace("-", "", $request->end_date);
        // echo "<br>";var_dump($request->id);
        $type = SyncSapConfig::select('type')->where('id', $request->id)->first();
        $id = $request->id;
        $date = [
            "ta"=>$start_date,
            "tr"=>$end_date
        ];
        // echo "<br>";var_dump($id,$type->type,$date);
        $response = (new SyncSapService)->execute($id, $type->type,$date);
        // echo "<br>";var_dump($response);die();
        if ($response['status'] == 'fail') {
            return response()->json($response, 500);
        }
        return response()->json($response, 200);
    }
}
