<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

use DB;

class PostingJurnalDetail extends Model
{

    protected $table    = 'posting_jurnal_detail';
    protected $fillable = ['id', 'posting_jurnal_id', 'posting_key', 'gl_account', 'gl_value_date', 'gl_costcenter', 'gl_fund', 'gl_funds_ctr', 'gl_cmmt_item_long', 'gl_alloc_nmbr', 'customer_code', 'vendor_code', 'pmnttrms', 'bline_date', 'pmnt_block', 'dsct_days1', 'dsct_days2', 'netterms', 'dsct_pct1', 'dsct_pct2', 'pymt_meth', 'pmtmthsupl', 'paymt_ref', 'bank_id', 'housebankacctid', 'sp_gl_ind', 'partner_bk', 'ref_key_1', 'ref_key_2', 'ref_key_3', 'amount', 'ket_jurnal', 'tambahan_1', 'tambahan_2', 'tambahan_3', 'flag', 'create_by', 'created_at', 'update_by', 'updated_at', 'deleted_at', 'delete_by','contract1', 'contract2', 'taxcode', 'flowtype', 'assigment', 'discamount', 'discbase', 'assigment', 'fixed', 'discamount', 'invoice1', 'invoice2', 'invoice3', 'calculatetax', 'wbs', 'network', 'asset', 'order', 'profit_segment', 'reb', 'salesorder', 'qty', 'bus_area'];

}
