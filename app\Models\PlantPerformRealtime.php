<?php

namespace App\Models;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class PlantPerformRealtime extends Model
{
    //
    protected $table    = 'vw_active_kiln';
    protected $fillable = ['id', 'uuid', 'name', 'url', 'order_no', 'permission' ,'iframe_url' ,'icon', 'parent_id', 'status', 'created_by', 'updated_by'];
    protected $primary_key = 'id';
    public $incrementing = false;
    protected $keyType = 'string';
    public static function activeKiln()
    {
        //data per Plant
        $countActiveKiln =DB::table('vw_active_kiln')
            ->select(DB::raw('concat(kode_opco,kode_plant) as key'),'kode_opco','kode_plant','tanggal','status')
            ->get();
        //group per Opco
        $g1 =DB::table('vw_active_kiln')
            ->select(DB::raw("concat(kode_opco,'All Plant') as key,kode_opco,'All Plant' as kode_plant,'' as tanggal, sum(status) as status"))
            ->groupBy('kode_opco')
            ->get();
        //all opco
        $g2 =DB::table('vw_active_kiln')
            ->select(DB::raw("'All OpcoAll Plant' as key,'All Opco' as kode_opco,'All Plant' as kode_plant,'' as tanggal, sum(status) as status"))
            ->get();
        $merged = $countActiveKiln->merge($g1);
        $merged = $merged->merge($g2);
        // dd($merged);
        return $merged;
    }
    
}
