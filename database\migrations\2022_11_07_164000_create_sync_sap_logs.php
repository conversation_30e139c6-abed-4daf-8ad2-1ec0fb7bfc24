<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSyncSapLogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sync_sap_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('sync_sap_config_id');
            $table->integer('year');
            $table->integer('month');
            $table->enum('status', ['success', 'process', 'fail']);
            $table->string('tcode');
            $table->text('parameter');
            $table->string('note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sync_sap_logs');
    }
}
