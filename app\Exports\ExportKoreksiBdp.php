<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Events\AfterSheet;
use DB;

class ExportKoreksiBdp implements WithEvents, FromCollection, WithHeadings, WithTitle, WithStyles
{

  /**
   * @return \Illuminate\Support\Collection
   */
  public function  __construct($data)
  {
    $this->data = $data;
  }
  /**
   * @return string
   */
  public function title(): string
  {
    return 'Data Export Koreksi BDP';
  }

  public function headings(): array
  {
    return [
      'No',
      'Plant',
      'Tahun',
      'BDP SIG (TON/DAY)',
      'BDP GHOPO (TON/DAY)',
      'BDP SG (TON/DAY)',
      'BDP SP (TON/DAY)',
      'BDP ST (TON/DAY)',
      'BDP SBI (TON/DAY)',
      'BDP TLCC (TON/DAY)',
    ];
  }

  public function collection()
  {   
    return collect($this->data);
  }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                //set background color for header (A1:D1)
                $event->sheet->getDelegate()
                ->getStyle('A1:J1')
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('C4D79B');

                //set header (A1:D1) uneditable and set workspace (A2:D1000) editable
                // $event->sheet->protectCells('A1:D1', 'PASSWORD');
                // $event->sheet->getStyle('A2:D1000')->getProtection()->setLocked(\PhpOffice\PhpSpreadsheet\Style\Protection::PROTECTION_UNPROTECTED);
                // $event->sheet->getDelegate()->getProtection()->setSheet(true);

            },
        ];
    }

  //set width for header
  public function columnWidths(): array
  {
    return [
      'A' => 15,
      'B' => 15,
      'C' => 22,
      'D' => 22,
      'E' => 22,
      'F' => 22,
      'G' => 22,
      'H' => 22,
      'I' => 22,
    ];
  }

  //set bold for header
  public function styles(Worksheet $sheet)
  {
    return [
      'A1'    => ['font' => ['bold' => true]],
      'B1'    => ['font' => ['bold' => true]],
      'C1'    => ['font' => ['bold' => true]],
      'D1'    => ['font' => ['bold' => true]],
      'E1'    => ['font' => ['bold' => true]],
      'F1'    => ['font' => ['bold' => true]],
      'G1'    => ['font' => ['bold' => true]],
      'H1'    => ['font' => ['bold' => true]],
      'I1'    => ['font' => ['bold' => true]],
      'J1'    => ['font' => ['bold' => true]]
    ];
  }
}
