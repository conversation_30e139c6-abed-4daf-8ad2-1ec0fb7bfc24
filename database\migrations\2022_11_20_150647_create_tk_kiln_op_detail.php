<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTkKilnOpDetail extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //Delete Insert Table not use SoftDelete
        Schema::create('tk_kiln_op_detail', function (Blueprint $table) {
            $table->bigIncrements('id_koreksi_kilnopdetail');
            $table->integer('no', false,false)->length(4)->nullable();
            $table->string('kode_opco', 30);
            $table->string('kode_plant', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->decimal('oph',10, 2)->nullable();
            $table->decimal('updt',10, 2)->nullable();
            $table->decimal('pdt',10, 2)->nullable();
            $table->decimal('stop_idle',10, 2)->nullable();
            $table->decimal('fy_stop',10, 2)->nullable();
            $table->integer('frek_updt')->nullable();
            $table->decimal('rkap_oph',10, 2)->nullable();
            $table->decimal('rkap_updt',10, 2)->nullable();
            $table->decimal('rkap_pdt',10, 2)->nullable();
            $table->decimal('rkap_stop_idle',10, 2)->nullable();
            $table->integer('rkap_frek_updt')->nullable();
            $table->decimal('cal',10, 2)->nullable();
            // $table->decimal('net_avail',3, 2)->nullable();
            $table->decimal('bdp_rate',10, 2)->nullable();
            $table->decimal('rkap_prod_rate',10, 2)->nullable();
            $table->decimal('koreksi',10, 2)->nullable();
            $table->decimal('act_prod',10, 2)->nullable();
            // $table->decimal('act_idle_prod',10, 2)->nullable();
            // $table->decimal('rate_gros',10, 2)->nullable();
            // $table->decimal('rate_netto',10, 2)->nullable();
            $table->decimal('rkap_prod',10, 2)->nullable();
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();

            $table->index(['kode_plant', 'tanggal']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tk_kiln_op_detail');
    }
}
