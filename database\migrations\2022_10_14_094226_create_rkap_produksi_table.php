<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRkapProduksiTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rkap_produksi', function (Blueprint $table) {
            $table->bigIncrements('id_rkap_produksi');
            $table->string('kode_opco', 30);
            $table->string('tahun', 4);
            $table->string('bulan', 2);
            $table->bigInteger('rkap_prod_klinker');
            $table->bigInteger('rkap_prod_semen');
            $table->bigInteger('rkap_clinker_sold');
            $table->bigInteger('rkap_prod_output');
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();        
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rkap_produksi');
    }
}
