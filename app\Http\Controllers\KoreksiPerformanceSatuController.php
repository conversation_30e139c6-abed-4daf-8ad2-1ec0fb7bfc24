<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Opco;
use App\Models\KilnPlant;
use App\Models\Kiln_Stop;
use Illuminate\Support\Facades\DB;
use DataTables;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Exception;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportTempKoreksiPerformance;
use App\Imports\ImportTempKoreksiPerformance;
use App\Models\Plant;
use App\Models\ReasonStop;
use DateTime;

class KoreksiPerformanceSatuController extends Controller
{
    private $listPlant;

    public function __construct(){
        $plant = KilnPlant::select('kode_plant')->orderBy('no_pbi')->get()->pluck('kode_plant');
        $this->listPlant = $plant;
    } 

    public function index()
    {
        $data = [
            'title' => 'Koreksi Data Performance',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/data-koreksi-satu-performance',
                ],
                [
                    'title'=>'Koreksi Data Performance',
                    'url'=>'',
                ]
            ],
        ];
        return view('koreksiLevel1.koreksiPerformance', $data);
    }

    public function viewImport()
    {
        $data = [
            'title' => 'Koreksi Data Performance',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/data-koreksi-satu-performance',
                ],
                [
                    'title'=>'Koreksi Data Performance',
                    'url'=>'/data-koreksi-satu-performance/import',
                ],
                [
                    'title'=>'Import Data',
                    'url'=>'',
                ]
            ],
        ];
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();
        $opt_opco = "";
        foreach ($opco as $value) {
          $kode_opco = $value['kode_opco'];
          $nama_opco = $value['nama_opco'];
          $opt_opco .= "<option value='$kode_opco'>$nama_opco</option>";
        }
        $data['opco'] = $opt_opco;
    
        $plant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
        $opt_plant = "";
        foreach ($plant as $value) {
          $kode_plant = $value['kode_plant'];
          $nama_plant = $value['name_plant'];
          $opt_plant .= "<option value='$kode_plant'>$nama_plant</option>";
        }
        $data['plant'] = $opt_plant;

        $kategori = DB::table('m_reason_stop')->select(['id_kategori','nama_kategori'])->orderBy('id_kategori')->get()->toArray();
        $opt_kategori = "";
        foreach ($kategori as $value) {
          $id_kategori = $value->id_kategori;
          $nama_kategori = $value->nama_kategori;
          $opt_kategori .= "<option value='$nama_kategori'>$nama_kategori</option>";
        }
        $data['kategori'] = $opt_kategori;
        return view('koreksiLevel1.koreksiPerformanceImport', $data);
    }

    public function datatables(Request $request)
    {
        $request->validate([
            'filter_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
        ]);

        $dataKilnStop = Kiln_Stop::select('t_kiln_stop.kode_plant','tanggal_mulai','tanggal_selesai','jenis_downtime','alasan','t_kiln_stop.is_sent')
        ->join('m_kiln_plant','t_kiln_stop.kode_plant','=','m_kiln_plant.kode_plant')->orderBy('tanggal_mulai','DESC');
        if ($request->filter_opco) {
            $dataKilnStop = $dataKilnStop->where('m_kiln_plant.kode_opco', $request->filter_opco);
        }
        if ($request->filter_plant) {
            $dataKilnStop = $dataKilnStop->where('t_kiln_stop.kode_plant', $request->filter_plant);
        }
        if($request->filter_tahun){
            $dataKilnStop = $dataKilnStop -> where(DB::raw("TO_CHAR(tanggal_mulai::timestamp, 'YYYY')"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $dataKilnStop = $dataKilnStop -> where(DB::raw("TO_CHAR(tanggal_mulai::timestamp, 'MM')"), $request->filter_bulan);
        }
        $dataKilnStop = $dataKilnStop->get();
        $data     = DataTables::of($dataKilnStop)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);   
    }

    public function export(Request $request)
    {
        $request->validate([
            'filter_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
        ]);

        $dataKilnStop = Kiln_Stop::select('kode_plant','tanggal_mulai','tanggal_selesai','m_reason_stop.nama_kategori','alasan')
        ->leftJoin('m_reason_stop','t_kiln_stop.id_kategori','=','m_reason_stop.id_kategori');        
        if ($request->filter_plant) {
            $dataKilnStop = $dataKilnStop->where('kode_plant', $request->filter_plant);
        }
        if($request->filter_tahun){
            $dataKilnStop = $dataKilnStop -> where(DB::raw("TO_CHAR(tanggal_mulai::timestamp, 'YYYY')"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $dataKilnStop = $dataKilnStop -> where(DB::raw("TO_CHAR(tanggal_mulai::timestamp, 'MM')"), $request->filter_bulan);
            $dataKilnStop = $dataKilnStop -> where(DB::raw("TO_CHAR(tanggal_mulai::timestamp, 'YYYY-MM-DD')"),'!=',date("Y-m-d"));
        }
        $dataKilnStop = $dataKilnStop->orderBy('tanggal_mulai','ASC')->get();
        $query = $dataKilnStop;

        foreach ($dataKilnStop as $key => $value) {
            $count = (date_diff(date_create($value->tanggal_mulai),date_create($value->tanggal_selesai)));
            if ($count->d == 1 && $count->h == 0) {
                $value->tanggal_selesai = 24;
            }else if($count->d == 0){
            $hours = $count->h;
            $minutes = ($count->i)/60;
            $spent = round($hours + $minutes,1);
            $value->tanggal_selesai = $spent;
        }
        }
        return Excel::download(new ExportTempKoreksiPerformance($dataKilnStop), 'Template Koreksi Performance.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
    }

    public function import(Request $request)
    {
		// validasi
		$this->validate($request, [
			'excel' => 'required|mimes:csv,xls,xlsx', //spekati satu file, validasi filename -> header -> coba cek dimeta data author
			'excel' => 'max:5000'
		]);

        try{

        // menangkap file excel
        $file = $request->file('excel');
        
        // import data
        $import = new ImportTempKoreksiPerformance;
        Excel::import($import, $file);

        $datas = ($import->data);
        //mapping data from import excel
        $newData = [];
        foreach ($datas as $value) {
            $temp = [];
            $temp['kode_plant'] = $value['kode_plant'];
            $temp['tanggal_mulai'] = $value['tanggal_mulai'];
            $temp['spent'] = $value['spenth'];
            $temp['nama_kategori'] = $value['kategori'] == null ? "": $value['kategori'] ;
            $temp['alasan'] = $value['alasan'] == null ? "": $value['alasan'] ;
            array_push($newData, $temp);
        }
        //validation 
        $result = [];
        foreach ($newData as $value) {
            $format = $value;
            //change format date
            if(is_double($format['tanggal_mulai']) || is_float($format['tanggal_mulai']) || is_int($format['tanggal_mulai'])){
                $format['tanggal_mulai'] = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($format['tanggal_mulai'])->format('Y-m-d H:i:s');
                $format['tanggal_mulai'] = date_format(date_create($format['tanggal_mulai']),"Y-m-d H:i:s");
                $validator = Validator::make($value,[
                    'kode_plant'        => ['required',Rule::in($this->listPlant)],
                    'tanggal_mulai'     => ['required'],
                    'spent'             => ['required', 'numeric','between:0,24'],
                    'nama_kategori'     => ['required', 'exists:m_reason_stop'],
                    'alasan'            => ['required'],
                ]);
            }
            else{
                $validator = Validator::make($value,[
                    'kode_plant'        => ['required',Rule::in($this->listPlant)],
                    'tanggal_mulai'     => ['required', 'date_format:Y-m-d H:i:s'],
                    'spent'             => ['required', 'numeric','between:0,24'],
                    'nama_kategori'     => ['required', 'exists:m_reason_stop'],
                    'alasan'            => ['required'],
                ]);
            }
            $createdate = date_format(date_create($format['tanggal_mulai']),"Y-m-d");
            $date_nows = date("Y-m-d");
            $format['is_valid'] = 'Valid';
            $format['note'] = array_values($validator->errors()->messages());
            $tempMessage = '';
            foreach ($format['note'] as $message) {
                $tempMessage .= $message[0];
            }
            $format['note'] = $tempMessage;
            if ($createdate == $date_nows) {
                $format['is_valid'] = 'Invalid';
                $format['note'] = "Dates cannot be the same";
            }
            $hour = ((strtotime(date_format(date_create($format['tanggal_mulai']),"Y-m-d 24:00:10")) - strtotime($format['tanggal_mulai']))/3600);
            if ($format['spent'] > $hour) {
                $format['note'] .= " Spent max ".(String)$hour;
            }
            $format['note'] == '' ? $format['is_valid'] = 'Valid' : $format['is_valid'] = 'Invalid';
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT); 
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function insertData(Request $request)
    {
        $excel = json_decode($request->excel);
        $listCol = ['kode_plant', 'tanggal_mulai', 'spent', 'nama_kategori', 'alasan'];
        $result = [];
        foreach ($excel as $data) {
            $oldData = $data;
            foreach ($listCol as $index => $col) {
                $data[$col] = $data[$index];
            }
            $data = array_diff_key($data, $oldData); 
            $format = $data;
            $validator = Validator::make($data,[
                'kode_plant'        => ['required',Rule::in($this->listPlant)],
                'tanggal_mulai'     => ['required', 'date_format:Y-m-d H:i:s'],
                'spent'             => ['required', 'numeric','between:0,24'],
                'nama_kategori'     => ['required', 'exists:m_reason_stop'],
                'alasan'            => ['required'],
            ]);
            $createdate = date_format(date_create($format['tanggal_mulai']),"Y-m-d");
            $date_nows = date("Y-m-d");
            $format['is_valid'] = 'Valid';
            $format['note'] = array_values($validator->errors()->messages());
            $tempMessage = '';
            foreach ($format['note'] as $message) {
                $tempMessage .= $message[0];
            }
            $format['note'] = $tempMessage;
            if ($createdate == $date_nows) {
                $format['is_valid'] = 'Invalid';
                $format['note'] = "Dates cannot be the same";
            }
            $hour = ((strtotime(date_format(date_create($format['tanggal_mulai']),"Y-m-d 24:00:10")) - strtotime($format['tanggal_mulai']))/3600);
            if ($format['spent'] > $hour) {
                $format['note'] .= " Spent max ".(String)$hour;
            }
            $format['note'] == '' ? $format['is_valid'] = 'Valid' : $format['is_valid'] = 'Invalid';
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT); 
    }

    public function saveData(Request $request)
    {
        $excel = json_decode($request->excel);
        $listCol = ['kode_plant', 'tanggal_mulai', 'spent', 'nama_kategori', 'alasan'];
        $result = [];
        $month = date('Y-m', strtotime($excel[0][1]));
        $now = date('Y-m');

        //check if multi plant
        $temp = $excel[0][0];
        $multi = false;
        foreach ($excel as $key => $value) {
            if($temp != $value[0]){
                $temp = $value[0];
                $multi=true;
            }
        }
        //check if now month
        if($month == $now){
            $firstDate = date('Y-m-01');
            $endDate = strtotime ( '-1 day' , strtotime (date('Y-m-d'))) ; 
            $endDate = date ( 'Y-m-d' , $endDate );
        }
        else{
            $firstDate = date("Y-m-01", strtotime($excel[0][1]));
            $endDate = date("Y-m-t", strtotime($excel[0][1]));             
        }
        //delete data
        $data = Kiln_Stop::whereBetween(DB::raw("TO_CHAR(tanggal_mulai::timestamp, 'YYYY-MM-DD')"),[$firstDate,$endDate]);
        if(!$multi){
            $data = $data->where('kode_plant',$excel[0][0]);
        } 
        $data = $data->delete(); 
        
        //get nama kategori, jenis downtime and kode opco
        $kategori = ReasonStop::pluck('id_kategori','nama_kategori');
        $jenisDowntime = ReasonStop::pluck('group','nama_kategori');
        $plant = KilnPlant::pluck('kode_opco','kode_plant');
        
        $result = [];
        $dataExcel = $excel;
        $queryYstd = Kiln_Stop::where('tanggal_selesai','<=',$dataExcel[0][1])->orderBy('tanggal_mulai', 'DESC')->first();
        $dataYstdMulai = $queryYstd->tanggal_mulai;
        $dataYstdSelesai = $queryYstd == null? $queryYstd : $queryYstd->tanggal_selesai;

        foreach ($dataExcel as $data) {
            $minutes  = round($data[2] * 60,0); //minutes
            $tgl_selesai = date('Y-m-d H:i:s', strtotime($data[1].' + '.$minutes.' minute'));
            $id_kategori = $kategori[$data[3]];
            $kode_opco = $plant[$data[0]];
            $jenis_downtime = $jenisDowntime[$data[3]];
            if($dataYstdSelesai == null){
                $is_sent = 5;
                $keterangan_sent = 'Data Koreksi Berhasil!';
            }
            else{
                if(date('Y', strtotime($data[1])) != date('Y', strtotime($dataYstdMulai))){
                    $is_sent = 5;
                    $keterangan_sent = 'Data Koreksi Berhasil!';
                }
                else {
                    $minutes = (strtotime($data[1]) - strtotime($dataYstdSelesai))/60; // selisih dan toleransi 10 menit
                    if(date('H', strtotime($data[1])) == '00' and ($minutes <= 10)){
                        $is_sent = 4;
                        $keterangan_sent = 'Off Lintas Hari!';
                    }
                    else{
                        $is_sent = 5;
                        $keterangan_sent = 'Data Koreksi Berhasil!';
                    }
                }
            }
            $dataYstdMulai = $data[1];
            $dataYstdSelesai = $tgl_selesai;

            $result[]=[
                'kode_plant'        => $data[0],
                'tanggal_mulai'     => $data[1],
                'tanggal_selesai'   => $tgl_selesai,
                'id_kategori'       => $id_kategori,
                'jenis_downtime'    => $jenis_downtime,
                'alasan'            => $data[4],
                'source_system'     => $kode_opco == 'SBI'? '-':'OPC',
                'is_sent'           => $is_sent,
                'keterangan_sent'   => $keterangan_sent
            ];
            $kode_plant_terakhir = $data[0];
            $jenis_downtime_terakhir = $jenis_downtime;
            $alasan_terakhir = $data[4];
            $id_kategori_terakhir = $id_kategori;
        }

        $data_tanggal_terakhir = date('Y-m-d', strtotime($tgl_selesai));
        $get_query_tanggal_selesai = Kiln_Stop::select('id_kiln_stop','tanggal_mulai', 'tanggal_selesai', 'is_sent')->where('kode_plant', $kode_plant_terakhir)->where(DB::raw("TO_CHAR(tanggal_mulai::timestamp, 'YYYY-MM-DD')"), $data_tanggal_terakhir)->where('is_sent',4)->first();

        if ($get_query_tanggal_selesai) {
            kiln_stop::where('id_kiln_stop',$get_query_tanggal_selesai->id_kiln_stop)->update(
                    ['id_kategori' => $id_kategori_terakhir, 'jenis_downtime' => $jenis_downtime_terakhir, 'alasan' => $alasan_terakhir]
                );
        }
        
        //bulk insert koreksi data
        Kiln_Stop::insert($result); 

        //update data to table staging(ts_realisasi_performance)
        $dataDate = date('Y-m', strtotime($dataExcel[0][1]));
        $kodePlant = $dataExcel[0][0];
        $dataMonthRealPerform = DB::table('vw_month_real_perform')->where('tanggal',$dataDate)->where('kode_plant',$kodePlant)->first();
        $dataRealisasi = DB::table('ts_realisasi_performance')->where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY-MM')"), $dataDate)->where('kode_plant',$kodePlant)->update([
            'oph'   =>$dataMonthRealPerform->oph,
            'updt'  =>$dataMonthRealPerform->updt,
            'pdt'   =>$dataMonthRealPerform->pdt,
            'stop_idle' =>$dataMonthRealPerform->stop_idle,
            'act_prod'  =>$dataMonthRealPerform->act_prod,
            'act_idle_prod' =>$dataMonthRealPerform->act_idle_prod,
            'frek_updt'   =>$dataMonthRealPerform->frek_updt,
            'fy_stop'   =>$dataMonthRealPerform->fy_stop,
            'cal'   =>$dataMonthRealPerform->cal,
        ]);
        $response = responseSuccess('Data added successfully');
        return response()->json($response,200);
    }
}
