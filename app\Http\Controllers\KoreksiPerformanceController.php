<?php

namespace App\Http\Controllers;

// use App\Models\Menu;
use Illuminate\Http\Request;
use App\Models\KoreksiPerformance;
use App\Models\KilnPlant;
use App\Models\Opco;
use Illuminate\Support\Facades\DB;
use Ya<PERSON>ra\DataTables\DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\KoreksiPerformanceExport;

class KoreksiPerformanceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = [
            'title' => 'Koreksi Data Performance',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/koreksi-data-performance',
                ],
                [
                    'title'=>'Koreksi Data Performance',
                    'url'=>'',
                ]
            ],
        ];
        // $data['menus'] = $this->getDashboardMenu();
        // $data['menu']  = Menu::select('id', 'name')->get();
        $data=[];
        return view('koreksiPerformance', $data);
    }

    public function getDatatables(Request $request)
    {
        if ($request->filter_plant == '') {
            $filter_plant = '1=1';
        } else {
            $filter_plant = "kode_plant = '" . $request->filter_plant . "'";
        }
        if ($request->filter_opco == '') {
            $filter_opco = '1=1';
        } else {
            $filter_opco = "kode_opco = '" . $request->filter_opco . "'";
        }
        if ($request->filter_tahun == '') {
            $filter_tahun = '1=1';
        } else {
            $filter_tahun = " substring(tanggal from 1 for 4)  = '" . $request->filter_tahun . "'";
        }
        if ($request->filter_bulan == '') {
            $filter_bulan = "1=1";
        } else {
            $filter_bulan = " substring(tanggal from 6 for 2)  = '" . $request->filter_bulan . "'";
        }
        $real_capex =  DB::table('vw_month_real_perform')->select(['id_rkap_produksi','last_date','rate_netto', 'rate_gross', 'koreksi', 'act_prod', 'act_idle_prod', 'net_avail', 'kode_opco', 'oph', 'kode_plant', 'tanggal', 'updt', 'pdt', 'stop_idle', 'fy_stop', 'frek_updt'])
            ->whereRaw($filter_plant)
            ->whereRaw($filter_tahun)
            ->whereRaw($filter_bulan)
            ->whereRaw($filter_opco)
            ->orderBy('tanggal','ASC');


        $data     = DataTables::of($real_capex)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function getFilterRealPerformance()
    {
        $filter_plant = KilnPlant::select('kode_plant')->get();
        $filter_opco = Opco::select('kode_opco')->get();

        $data = [
            'kode_plant' => $filter_plant,
            'filter_opco' => $filter_opco
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function editKoreksi(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = array(
                "koreksi" => $request->value,
            );
            DB::table('rkap_produksi')->where('id_rkap_produksi', $request->id_rkap_produksi)->update($data);
            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Success Update Koreksi'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $th->getMessage()
            ]);
        }
    }

    public function export()
    {
        // Expot data with Collection
        $data = DB::table('vw_month_real_perform_riset')->select([
            'kode_opco',
            'kode_plant',
            'tanggal',
            'oph',
            'updt',
            'pdt',
            'stop_idle',
            'fy_stop',
            'frek_updt',
            'net_avail',
            'koreksi',
            'act_prod',
            'act_idle_prod',
            'rate_gross',
            'rate_netto'

        ])->get();
        return Excel::download(new RealPerformanceExport($data), 'RealisasiPerformance.xlsx');

        // Export data with View
        // return Excel::download(new RealPerformanceExport, 'RealisasiPerformance.xlsx');

        // Expot data with format XLSX
        // return (new RealPerformanceExport)->download('RealisasiPerformance.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\RealPerformanceController  $realPerformanceController
     * @return \Illuminate\Http\Response
     */
    public function show(RealPerformanceController $realPerformanceController)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\RealPerformanceController  $realPerformanceController
     * @return \Illuminate\Http\Response
     */
    public function edit(RealPerformanceController $realPerformanceController)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\RealPerformanceController  $realPerformanceController
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, RealPerformanceController $realPerformanceController)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\RealPerformanceController  $realPerformanceController
     * @return \Illuminate\Http\Response
     */
    public function destroy(RealPerformanceController $realPerformanceController)
    {
        //
    }
}
