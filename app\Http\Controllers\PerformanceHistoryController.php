<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Opco;
use App\Models\KilnPlant;
use App\Models\Kiln_Stop;
use App\Models\KilnRate;
use App\Models\ResumePlantEvent;
use Yajra\DataTables\DataTables;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
class PerformanceHistoryController extends Controller
{
    public function index(){
        $data = [
            'title' => 'Plant Performance History',
            'breadcrumb' => [
                [
                    'title'=>'Dashboard',
                    'url'=>'/',
                ],
                [
                    'title'=>'Plant Performance History',
                    'url'=>'',
                ]
            ],
        ];
        return view('PerformanceHistoryNew',$data);
    }

    public function filter()
    {
        $opco = Opco::select(['kode_opco', 'nama_opco'])->where('kode_opco','!=','SI2000')->orderBy('id')->get()->toArray();
        $kilnPlant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow; $i++) {
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $bulan = [];
        for ($i=0; $i < count($months); $i++) {
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => true
                ];
            }
            else{
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => false
                ];
            }
        }

        $data = [
            'opco'      => $opco,
            'kilnPlant' => $kilnPlant,
            'tahun'     => $tahun,
            'bulan'     => $bulan,
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function lineRealTimeChart(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $kilnrate = DB::table('t_kiln_rate')
        ->select('t_kiln_rate.tanggal',DB::raw('extract(epoch from tanggal)*1000 as tanggal_part'),'kiln_rate','t_kiln_rate.kode_plant','factorial')
        ->leftjoin('m_kiln_plant','t_kiln_rate.kode_plant',"=","m_kiln_plant.kode_plant")
        ->where('t_kiln_rate.kode_plant', $request->plant);
        // $activeKiln = DB::table('vw_active_kiln')->select('status')->where('kode_plant',$request->plant);
        if($request->tahun){
            $kilnrate = $kilnrate -> where(DB::raw("extract(year from t_kiln_rate.tanggal)"), $request->tahun);
            // $activeKiln = $activeKiln -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $bulan = explode(",", $request->bulan);
            $kilnrate = $kilnrate -> whereIn(DB::raw("extract(month from t_kiln_rate.tanggal)"), $bulan);
            // $activeKiln = $activeKiln -> whereIn(DB::raw("extract(month from tanggal)"), $bulan);
        }
        $kilnrate = $kilnrate->orderBy('tanggal', 'DESC')->get();
        // $activeKiln = $activeKiln->first();
        $kilnrate = $kilnrate->sortBy('tanggal');
        $dt = "Time, Kiln Rate" . "\n";
        date_default_timezone_set('UTC');
        $datanew = [];
        foreach ($kilnrate as $k => $v) {
            if($v->factorial > 0){
                $v->kiln_rate = (($v->kiln_rate/$v->factorial) * 24);
            }
            else{
                $v->kiln_rate = (($v->kiln_rate) * 24);
            }
            // $date = date_create($v->tanggal);
            // $date_epoch = date_format($date, 'U')*1000;
            $date_epoch = (int)$v->tanggal_part;
            $datanew[] = [
                    $date_epoch, $v->kiln_rate
                ];
            // $dt .= $date_epoch . ", " . $v->kiln_rate . "\n";
        }
        // dd($datanew);
        return response()->json($datanew, 200, [], JSON_PRETTY_PRINT);
        // return [
        //     'message' => 'Succes',
        //     'data' => $kilnrate
        // ];
    }

    public function getNettoAvailability(Request $request)
    {
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily');

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance');
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi');
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->get();
        

        $oph = 0;
        $idle = 0;
        $cal = 0;
        foreach ($dataReal as $key => $item) {
            $oph = $oph + $item->oph;
            $idle = $idle + $item->stop_idle;
            $cal = $cal + $item->cal;
        }

        $rkap_op = 0;
        $rkap_idle = 0;
        $rkap_cal = 0;
        $rkap_oph_netto = 0;
        foreach ($dataRKAP as $key => $value) {
            $rkap_op = $rkap_op + $value->oph;
            $rkap_idle = $rkap_idle + $value->stop_idle;
            $rkap_cal = $rkap_cal + $value->cal;
            $rkap_oph_netto = $rkap_oph_netto + $value->oph_netto;
        }

        if(($oph + $idle) > 1){
            $realNAI = (($oph + $idle) / $cal) * 100;
        }else{
            $realNAI = $oph + $idle;
        }

        if(($rkap_oph_netto) > 1){
            $rkapNAI = (($rkap_oph_netto) / $rkap_cal) * 100;
        }else{
            $rkapNAI = $rkap_oph_netto;
        }

        if($request->tanggal){
            $netDays = round(($rkap_op + $rkap_idle)/24,0);
            $NAIPerDay = $rkapNAI / $netDays;
            // $countDay = $rkap_cal / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
            $firstDay = (int) substr($request->tanggal, 3, 2);
            $lastDay = (int) substr($request->tanggal, 16, 2);
            $rangeDay = $lastDay - $firstDay + 1;
            if ($rangeDay > $netDays) {
                $rkapNAI = $rkapNAI;
            }
            else{
                $rkapNAI = $NAIPerDay * $rangeDay;
            }
        }

        $selisih = round($realNAI,1) - round($rkapNAI,1);

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$realNAI).'#'.sprintf("%.1f",$rkapNAI).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardNettoYield(Request $request)
    {
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select([
                DB::raw('COALESCE(SUM(oph),0) as oph'),
                DB::raw('COALESCE(SUM(stop_idle),0) as stop_idle'),
                DB::raw('COALESCE(SUM(act_prod),0) as act_prod'),
                DB::raw('COALESCE(SUM(act_idle_prod),0) as act_idle_prod'),
                DB::raw('COALESCE(SUM(koreksi),0) as koreksi')
            ])->groupBy('kode_plant');

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select([
                DB::raw('COALESCE(SUM(oph),0) as oph'),
                DB::raw('COALESCE(SUM(stop_idle),0) as stop_idle'),
                DB::raw('COALESCE(SUM(act_prod),0) as act_prod'),
                DB::raw('COALESCE(SUM(act_idle_prod),0) as act_idle_prod'),
                DB::raw('COALESCE(SUM(koreksi),0) as koreksi')
            ])->groupBy('kode_plant');
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->get();

        $tpd = 0;
        foreach ($dataReal as $key => $item) {
            if((($item->oph + $item->stop_idle) / 24) >=1){
                $tpd += ($item->act_prod + $item->act_idle_prod + $item->koreksi) / (($item->oph + $item->stop_idle) / 24);
            }else{
                $tpd += $item->act_prod + $item->act_idle_prod + $item->koreksi;
            }
        }
        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select([
            DB::raw('COALESCE(AVG(bdp_rate),0) as bdp_rate'),
            DB::raw('COALESCE(AVG(prod_rate),0) as prod_rate'),
            DB::raw('COALESCE(SUM(cal),0) as cal'),
        ]);
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAPRes = $dataRKAP->get();
        $dataRealBDP = $dataRKAP->groupBy('kode_plant')->get();

        $bdp = 0;
        foreach ($dataRealBDP as $key => $value) {
            $bdp = $bdp + $value->bdp_rate;
        }

        $rkap_tpd = 0;
        $rkap_bdp = 0;
        $rkap_cal = 0;
        foreach($dataRKAPRes as $key => $value){
            $rkap_tpd = $rkap_tpd + round($value->prod_rate,1);
            $rkap_bdp = $rkap_bdp + round($value->bdp_rate,1);
            $rkap_cal = $rkap_cal + $value->cal;
        }

        // if($request->tanggal){
        //     $countDay = $rkap_cal / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
        //     $firstDay = (int) substr($request->tanggal, 3, 2);
        //     $lastDay = (int) substr($request->tanggal, 16, 2);
        //     $rangeDay = $lastDay - $firstDay + 1;
        //     $bdp = $bdp * $rangeDay / $countDay;
        //     $rkap_tpd = $rkap_tpd * $rangeDay / $countDay;
        // }
        if($tpd > 1 && $bdp > 0){
            $atas = $tpd / $bdp * 100;
        }else{
            $atas = $tpd * 100;
        }

        if($rkap_tpd > 1 && $bdp > 0){
            $bawah = $rkap_tpd / $rkap_bdp *100;
        }else{
            $bawah = $rkap_tpd*100;
        }
        $selisih = round($atas,1) - round($bawah,1);

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardNettoOEE(Request $request)
    {
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select([
                DB::raw('COALESCE(SUM(oph),0) as oph'),
                DB::raw('COALESCE(SUM(stop_idle),0) as stop_idle'),
                DB::raw('COALESCE(SUM(act_prod),0) as act_prod'),
                DB::raw('COALESCE(SUM(act_idle_prod),0) as act_idle_prod'),
                DB::raw('COALESCE(SUM(koreksi),0) as koreksi'),
                DB::raw('COALESCE(SUM(cal),0) as cal')
            ]);

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select([
                DB::raw('COALESCE(SUM(oph),0) as oph'),
                DB::raw('COALESCE(SUM(stop_idle),0) as stop_idle'),
                DB::raw('COALESCE(SUM(act_prod),0) as act_prod'),
                DB::raw('COALESCE(SUM(act_idle_prod),0) as act_idle_prod'),
                DB::raw('COALESCE(SUM(koreksi),0) as koreksi'),
                DB::raw('COALESCE(SUM(cal),0) as cal')
            ]);
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataRealNai = $dataReal->get();
        $dataReal = $dataReal->groupBy('kode_plant')->get();

        $tpd = 0;
        foreach ($dataReal as $key => $item) {
            if((($item->oph + $item->stop_idle) / 24) >=1){
                $tpd += ($item->act_prod + $item->act_idle_prod + $item->koreksi) / (($item->oph + $item->stop_idle) / 24);
            }else{
                $tpd += $item->act_prod + $item->act_idle_prod + $item->koreksi;
            }
        }

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select([
            DB::raw('COALESCE(AVG(bdp_rate),0) as bdp_rate'),
            DB::raw('COALESCE(AVG(prod_rate),0) as prod_rate'),
            DB::raw('COALESCE(SUM(cal),0) as cal'),
            DB::raw('COALESCE(SUM(oph),0) as oph'),
            DB::raw('COALESCE(SUM(stop_idle),0) as stop_idle'),
        ]);
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAPRes = $dataRKAP->get();
        $dataRealBDP = $dataRKAP->groupBy('kode_plant')->get();

        //bdp realisasi
        $bdp = 0;
        foreach ($dataRealBDP as $key => $value) {
            $bdp = $bdp + $value->bdp_rate;
        }

        $rkap_tpd = 0;
        $rkap_bdp = 0;
        $rkap_oph = 0;
        $rkap_stop_idle = 0;
        $rkap_cal = 0;
        foreach($dataRKAPRes as $key => $value){
            $rkap_tpd       += round($value->prod_rate,1);
            $rkap_bdp       += round($value->bdp_rate,1);
            $rkap_oph       += $value->oph;
            $rkap_stop_idle += $value->stop_idle;
            $rkap_cal       += $value->cal;
        }

        $oph = 0;
        $stop_idle = 0;
        $cal = 0;
        foreach($dataRealNai as $key => $value){
            $oph       += $value->oph;
            $stop_idle += $value->stop_idle;
            $cal       += $value->cal;
        }

        //realisasi
        if($tpd > 1 && $bdp > 0){
            $yield = ($tpd / $bdp) *100;
        }else{
            $yield = $tpd*100;
        }

        if(($oph + $stop_idle) >1){
            $nai = (($oph + $stop_idle) / $cal) *100;
        }else{
            $nai = ($oph + $stop_idle) *100;
        }

        //RKAP
        if($rkap_tpd > 1 && $rkap_bdp > 0){
            $rkap_yield = ($rkap_tpd / $rkap_bdp) *100;
        }else{
            $rkap_yield = $rkap_tpd*100;
        }

        if(($rkap_oph + $rkap_stop_idle) > 1){
            $rkap_nai = (($rkap_oph + $rkap_stop_idle) / $rkap_cal) * 100;
        }else{
            $rkap_nai = ($rkap_oph + $rkap_stop_idle)*100;
        }

        // if($request->tanggal){
        //     $countDay = $rkap_cal / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
        //     $firstDay = (int) substr($request->tanggal, 3, 2);
        //     $lastDay = (int) substr($request->tanggal, 16, 2);
        //     $rangeDay = $lastDay - $firstDay + 1;
        //     $rkap_oph = $rkap_oph * $rangeDay / $countDay;
        //     $rkap_stop_idle = $rkap_stop_idle * $rangeDay / $countDay;
        //     $rkap_cal = $rkap_cal * $rangeDay / $countDay;
        // }
        if($request->tanggal){
            $netDays = round(($rkap_oph + $rkap_stop_idle)/24,0);
            $NAIPerDay = $rkap_nai / $netDays;
            // $countDay = $rkap_cal / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
            $firstDay = (int) substr($request->tanggal, 3, 2);
            $lastDay = (int) substr($request->tanggal, 16, 2);
            $rangeDay = $lastDay - $firstDay + 1;
            if ($rangeDay > $netDays) {
                $rkap_nai = $rkap_nai;
            }
            else{
                $rkap_nai = $NAIPerDay * $rangeDay;
            }
        }

        $atas = $yield * $nai  / 100;
        $bawah = $rkap_yield * $rkap_nai / 100;
        $selisih = round($atas,1) - round($bawah,1);

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardMTBF(Request $request)
    {
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily');

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance');
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi');
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->get();

        $frek_updt = 0;
        $oph = 0;
        foreach ($dataReal as $key => $item) {
            $frek_updt = $frek_updt + $item->frek_updt;
            $oph = $oph + $item->oph;
        }

        $rkap_frek_updt = 0;
        $rkap_oph = 0;
        $rkap_cal = 0;
        foreach ($dataRKAP as $key => $item_perform) {
            $rkap_frek_updt = $rkap_frek_updt + $item_perform->frek_updt;
            $rkap_oph = $rkap_oph + $item_perform->oph;
            $rkap_cal = $rkap_cal + $item_perform->cal;
        }

        if($request->tanggal){
            $countDay = $rkap_cal / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
            $firstDay = (int) substr($request->tanggal, 3, 2);
            $lastDay = (int) substr($request->tanggal, 16, 2);
            $rangeDay = $lastDay - $firstDay + 1;
            $rkap_frek_updt = $rkap_frek_updt * $rangeDay / $countDay;
            $rkap_oph = $rkap_oph * $rangeDay / $countDay;
        }

        if($frek_updt > 1){
            $atas = $oph / $frek_updt;
        }else{
            $atas = $oph;
        }

        if($rkap_frek_updt > 1){
            $bawah = $rkap_oph / $rkap_frek_updt;
        }else{
            $bawah = $rkap_oph;
        }

        if($atas > 0 && $bawah > 0){
            $selisih = (($atas - $bawah) / $bawah) * 100;
        }else{
            $selisih = 0;
        }

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardMTTR(Request $request)
    {
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily');

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance');
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi');
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->get();

        $frek_updt = 0;
        $updt = 0;
        foreach ($dataReal as $key => $item) {
            $frek_updt = $frek_updt + $item->frek_updt;
            $updt = $updt + $item->updt;
        }

        $rkap_frek_updt = 0;
        $rkap_updt = 0;
        $rkap_cal = 0;
        foreach ($dataRKAP as $key => $item_perform) {
            $rkap_frek_updt = $rkap_frek_updt + $item_perform->frek_updt;
            $rkap_updt = $rkap_updt + $item_perform->updt;
            $rkap_cal = $rkap_cal + $item_perform->cal;
        }

        if($request->tanggal){
            $countDay = $rkap_cal / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
            $firstDay = (int) substr($request->tanggal, 3, 2);
            $lastDay = (int) substr($request->tanggal, 16, 2);
            $rangeDay = $lastDay - $firstDay + 1;
            $rkap_frek_updt = $rkap_frek_updt * $rangeDay / $countDay;
            $rkap_updt = $rkap_updt * $rangeDay / $countDay;
        }

        if($frek_updt > 1){
            $atas = $updt / $frek_updt;
        }else{
            $atas = $updt;
        }

        if($rkap_frek_updt > 1){
            $bawah = $rkap_updt / $rkap_frek_updt;
        }else{
            $bawah = $rkap_updt;
        }

        if($bawah > 0){
            $selisih = (($bawah - $atas) / $bawah) * 100;
        }else{
            $selisih = $bawah;
        }

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardRatioUpdt(Request $request)
    {
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily');

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance');
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi');
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->get();

        $updt = 0;
        $oph = 0;
        $idle = 0;
        foreach ($dataReal as $key => $item) {
            $updt = $updt + $item->updt;
            $oph = $oph + $item->oph;
            $idle = $idle + $item->stop_idle;
        }

        $rkap_updt = 0;
        $rkap_oph = 0;
        $rkap_idle = 0;
        $rkap_cal = 0;
        foreach ($dataRKAP as $key => $item_perform) {
            $rkap_updt = $rkap_updt + $item_perform->updt;
            $rkap_oph = $rkap_oph + $item_perform->oph;
            $rkap_idle = $rkap_idle + $item_perform->stop_idle;
            $rkap_cal = $rkap_cal + $item_perform->cal;
        }

        if($request->tanggal){
            $countDay = $rkap_cal / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
            $firstDay = (int) substr($request->tanggal, 3, 2);
            $lastDay = (int) substr($request->tanggal, 16, 2);
            $rangeDay = $lastDay - $firstDay + 1;
            $rkap_updt = $rkap_updt * $rangeDay / $countDay;
            $rkap_oph = $rkap_oph * $rangeDay / $countDay;
            $rkap_idle = $rkap_idle * $rangeDay / $countDay;
        }

        if(($oph + $idle) < 1){
            $atas = $updt * 100;

        }else{
            $atas = $updt / ($oph + $idle) * 100;
        }
        if(($rkap_oph + $rkap_idle) < 1){
            $bawah = $rkap_updt * 100;

        }else{
            $bawah = $rkap_updt / ($rkap_oph + $rkap_idle) * 100;
        }
        if ($bawah > 0) {
            $selisih = (($bawah - $atas)/$bawah) * 100;
        }
        else{
            $selisih = $bawah;
        }

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardActiveKiln(Request $request)
    {
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select(DB::raw('SUM(oph) as oph'), 'kode_plant');

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select(DB::raw('SUM(oph) as oph'), 'kode_plant');
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->groupBy('kode_plant')->get();

        $count= 0;
        foreach ($dataReal as $key => $item){
            if($item->oph > 1){
                $count++;
            }
        }

        return [
            'message' => 'Succes',
            'data' => $count
        ];
    }

    public function cardFrekUpdt(Request $request)
    {
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily');

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance');
        }
        $dataReal = $dataReal ->select('frek_updt');
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi');
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->get();

        $frek_updt = 0;
        foreach ($dataReal as $key => $item) {
            $frek_updt = $frek_updt + $item->frek_updt;
        }

        $rkap_frek_updt = 0;
        foreach ($dataRKAP as $key => $item) {
            $rkap_frek_updt = $rkap_frek_updt + $item->frek_updt;
        }

        if($rkap_frek_updt > 0){
            $selisih = $rkap_frek_updt - $frek_updt;
        }else{
            $selisih = $rkap_frek_updt;
        }

        return [
            'message' => 'Succes',
            'data' => $frek_updt.'#'.$rkap_frek_updt.'#'.$selisih
        ];
    }

    public function getPlantEvent(Request $request)
    {
        if($request->tahun <= '2022'){
            $data = ResumePlantEvent::select(DB::raw("to_char(tanggal, 'dd Mon YYYY') as tanggal"), 'kode_plant', 'alasan', 'nama_kategori',
            DB::raw("to_char(tanggal, 'HH24:MI') as time"),DB::raw("updt+pdt+stop_idle as spent"))->where('oph',0);
            if($request->tanggal){
                $firstDay = substr($request->tanggal, 0, 10);
                $lastDay = substr($request->tanggal, 13, 10);
                $data = $data -> whereBetween('tanggal',[$firstDay, $lastDay]);
            }
            if($request->opco){
                $data = $data -> where('kode_opco', $request->opco);
            }
            if($request->plant){
                $data = $data -> where('kode_plant', $request->plant);
            }
            if($request->tahun){
                $data = $data -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
            }
            if($request->bulan){
                $data = $data -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
            }
            $data = $data->orderBy('tanggal','DESC')->get();
        }
        else{
            $data = DB::table('vw_plant_event')->select('vw_plant_event.kode_plant',DB::raw("to_char(tanggal_mulai, 'dd Mon YYYY') as tanggal"),'jenis_downtime as nama_kategori','alasan','spent',
            DB::raw("to_char(tanggal_mulai, 'HH24:MI') as time"))->join('m_kiln_plant','vw_plant_event.kode_plant','=','m_kiln_plant.kode_plant')->where('jenis_downtime','!=','FY STOP');
            if($request->tanggal){
                $firstDay = substr($request->tanggal, 0, 10);
                $lastDay = substr($request->tanggal, 13, 10);
                $data = $data -> whereBetween('tanggal_mulai',[$firstDay, $lastDay]);
            }
            if($request->opco){
                $data = $data -> where('m_kiln_plant.kode_opco', $request->opco);
            }
            if($request->plant){
                $data = $data -> where('vw_plant_event.kode_plant', $request->plant);
            }
            if($request->tahun){
                $data = $data -> where(DB::raw("to_char(tanggal_mulai,'YYYY')"), $request->tahun);
            }
            if($request->bulan){
                $data = $data -> whereIn(DB::raw("to_char(tanggal_mulai,'FMMM')"), $request->bulan);
            }
            $data = $data->orderBy('tanggal_mulai','DESC')->get();
        }
        $result=[];
        foreach ($data as $key => $item) {

            // if($item->tanggal_selesai == null || $item->tanggal_selesai == ''){
            //     $mulai = strtotime($item->tanggal_mulai);
            //     $akhir = strtotime(Carbon::now()->format('Y-m-d H:i:s'));

            //     $diff = $akhir - $mulai;
            //     $hour = floor($diff / (60 * 60));
            // }else{
            //     $mulai = strtotime($item->tanggal_mulai);
            //     $akhir = strtotime($item->tanggal_selesai);

            //     $diff = $akhir - $mulai;
            //     $hour = floor($diff / (60 * 60));
            // }
            if($item->nama_kategori == NULL and $item-> alasan == NULL){
                $item->nama_kategori = '';
            }
            else if($item->nama_kategori == NULL){
                $item->nama_kategori = '-';
            }
            $result[] = array(
                    // 'id'                => $item->id_kiln_stop,
                    'kode_plant'        => $item->kode_plant,
                    'tanggal_mulai'     => $item->tanggal,
                    // 'tanggal_selesai'   => $item->tanggal_selesai,
                    'kategori'          => $item->nama_kategori,
                    'deskripsi'         => $item->alasan == NULL ? '-':$item->alasan,
                    'spent'             => round($item->spent,1),
                    'time'              => $item->time
            );
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function kilnRateHistory(Request $request)
    {
        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant','no_pbi');
        if($request->opco){
            $no_pbi = $no_pbi -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $no_pbi = $no_pbi -> where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
                            ->get();

        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(act_prod) as act_prod'),
                DB::raw('SUM(act_idle_prod) as act_idle_prod'),
                DB::raw('SUM(koreksi) as koreksi'),
                DB::raw('SUM(cal) as cal'),
            );

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(act_prod) as act_prod'),
                DB::raw('SUM(act_idle_prod) as act_idle_prod'),
                DB::raw('SUM(koreksi) as koreksi'),
                DB::raw('SUM(cal) as cal'),
            );
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->orderBy('kode_plant', 'ASC')
                            ->get();
        $data = [];
        foreach($dataReal as $k => $v){
            $data[$v->kode_plant]['real_oph'] = $v->oph;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_act_prod'] = $v->act_prod;
            $data[$v->kode_plant]['real_act_idle_prod'] = $v->act_idle_prod;
            $data[$v->kode_plant]['real_koreksi'] = $v->koreksi;
            $data[$v->kode_plant]['real_cal'] = $v->cal;
        }

        $dtSort=[];
        foreach($no_pbi as $k => $v){
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant,$data)?$data[$v->kode_plant]:[];
        }

        $data = NULL;
        foreach($dtSort as $k =>$v){
            $list_kode_plant[] = $k;
            $data[$k]['real_oph'] = array_key_exists('real_oph',$v)?$v['real_oph']:0;
            $data[$k]['real_stop_idle'] = array_key_exists('real_stop_idle',$v)?$v['real_stop_idle']:0;
            $data[$k]['real_act_prod'] = array_key_exists('real_act_prod',$v)?$v['real_act_prod']:0;
            $data[$k]['real_act_idle_prod'] = array_key_exists('real_act_idle_prod',$v)?$v['real_act_idle_prod']:0;
            $data[$k]['real_koreksi'] = array_key_exists('real_koreksi',$v)?$v['real_koreksi']:0;
            $data[$k]['real_cal'] = array_key_exists('real_cal',$v)?$v['real_cal']:0;
        }

        $data_value = [];

        foreach ($data as $i => $v) {

            if(($data[$i]['real_oph']  / 24) >=1){
                $data_value[$i]['rate_gross_real'] =  round($data[$i]['real_act_prod'] / ($data[$i]['real_oph']/24),1);
            }else{
                $data_value[$i]['rate_gross_real'] =   round($data[$i]['real_act_prod'],1);
            }
        }
        
        $data = [
            'kiln_rate'      => $data_value,
            'list_kode_plant' => $list_kode_plant
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function realSummaryGraph(Request $request)
    {
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select(
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(updt) as updt'),
                DB::raw('SUM(pdt) as pdt'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(cal) as cal'),
            );
            $dataActiveKiln = DB::table('ts_realisasi_performance_daily')->select('kode_plant',
                DB::raw('SUM(oph) as oph'));

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
            $dataActiveKiln = $dataActiveKiln -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select(
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(updt) as updt'),
                DB::raw('SUM(pdt) as pdt'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(cal) as cal'),
            );
            $dataActiveKiln = DB::table('vw_rkap_performance_porsi')->select('kode_plant',
                DB::raw('SUM(oph) as oph'));
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
            $dataActiveKiln = $dataActiveKiln -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
            $dataActiveKiln = $dataActiveKiln -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
            $dataActiveKiln = $dataActiveKiln -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
            $dataActiveKiln = $dataActiveKiln -> whereIn('bulan', $request->bulan);
        }
        $dataActiveKiln = $dataActiveKiln->groupBy('kode_plant')->get();
        $dataReal = $dataReal->first();
        $count= 0;
        foreach ($dataActiveKiln as $key => $item){
            if($item->oph > 1){
                $count++;
            }
        }
        $calTime = $dataReal->cal;

        $total = $dataReal->oph + $dataReal->pdt + $dataReal->updt + $dataReal->stop_idle;

        if ($dataReal->oph != NULL) {
            $dataReal->oph = round($dataReal->oph, 1);
            $dataReal->persen_oph = $total > 0 ? round(($dataReal->oph / $total) * 100, 1): 0;
        }else{
            $dataReal->oph = 0;
            $dataReal->persen_oph = 0;
        }

        if ($dataReal->pdt != NULL) {
            $dataReal->pdt = round($dataReal->pdt, 1);
            $dataReal->persen_pdt = $total > 0 ? round(($dataReal->pdt / $total) * 100, 1): 0;
        }else{
            $dataReal->pdt = 0;
            $dataReal->persen_pdt = 0;
        }

        if ($dataReal->updt != NULL) {
            $dataReal->updt = round($dataReal->updt, 1);
            $dataReal->persen_updt = $total > 0 ?round(($dataReal->updt / $total) * 100, 1): 0;
        }else{
            $dataReal->updt = 0;
            $dataReal->persen_updt = 0;
        }

        if ($dataReal->stop_idle != NULL) {
            $dataReal->stop_idle = round($dataReal->stop_idle, 1);
            $dataReal->persen_stop_idle = $total > 0 ?round(($dataReal->stop_idle / $total) * 100, 1): 0;
        }else{
            $dataReal->stop_idle = 0;
            $dataReal->persen_stop_idle = 0;
        }

        return [
            'message' => 'Succes Get Data',
            'real_summary_graph' => $dataReal,
            'activeKiln'    => $count,
            'calTime'       => $calTime
        ];
    }

    public function rkapSummaryGraph(Request $request)
    {
        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
                DB::raw('SUM(cal) as cal'),
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(updt) as updt'),
                DB::raw('SUM(pdt) as pdt'),
                DB::raw('SUM(stop_idle) as stop_idle'),
            );
        $dataActiveKiln = DB::table('vw_rkap_performance_porsi')->select('kode_plant',
            DB::raw('SUM(oph) as oph'));
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
            $dataActiveKiln = $dataActiveKiln -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
            $dataActiveKiln = $dataActiveKiln -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
            $dataActiveKiln = $dataActiveKiln -> where('tahun', $request->tahun);

        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
            $dataActiveKiln = $dataActiveKiln -> whereIn('bulan', $request->bulan);
        }
        $dataActiveKiln = $dataActiveKiln->groupBy('kode_plant')->get();
        $dataRKAP = $dataRKAP->first();
        $count= 0;
        foreach ($dataActiveKiln as $key => $item){
            if($item->oph > 1){
                $count++;
            }
        }
        $calTime = $dataRKAP->cal?$dataRKAP->cal:0;

        if($request->tanggal){
            $countDay = $dataRKAP->cal / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
            $firstDay = (int) substr($request->tanggal, 3, 2);
            $lastDay = (int) substr($request->tanggal, 16, 2);
            $rangeDay = $lastDay - $firstDay + 1;
            $dataRKAP->oph = round($dataRKAP->oph * $rangeDay / $countDay, 1);
            $dataRKAP->pdt = round($dataRKAP->pdt * $rangeDay / $countDay, 1);
            $dataRKAP->updt = round($dataRKAP->updt * $rangeDay / $countDay, 1);
            $dataRKAP->stop_idle = round($dataRKAP->stop_idle * $rangeDay / $countDay, 1);
        }

        $total = $dataRKAP->oph + $dataRKAP->pdt + $dataRKAP->updt + $dataRKAP->stop_idle;

        if ($dataRKAP->oph != NULL) {
            $dataRKAP->persen_oph = $total > 0 ?round(($dataRKAP->oph / $total) * 100, 1): 0;
        }else{
            $dataRKAP->oph = 0;
            $dataRKAP->persen_oph = 0;
        }

        if ($dataRKAP->pdt != NULL) {
            $dataRKAP->persen_pdt = $total > 0 ?round(($dataRKAP->pdt / $total) * 100, 1): 0;
        }else{
            $dataRKAP->pdt = 0;
            $dataRKAP->persen_pdt = 0;
        }

        if ($dataRKAP->updt != NULL) {
            $dataRKAP->persen_updt = $total > 0 ?round(($dataRKAP->updt / $total) * 100, 1): 0;
        }else{
            $dataRKAP->updt = 0;
            $dataRKAP->persen_updt = 0;
        }

        if ($dataRKAP->stop_idle != NULL) {
            $dataRKAP->persen_stop_idle = $total > 0 ?round(($dataRKAP->stop_idle / $total) * 100, 1): 0;
        }else{
            $dataRKAP->stop_idle = 0;
            $dataRKAP->persen_stop_idle = 0;
        }

        return [
            'message' => 'Succes Get Data',
            'real_summary_graph' => $dataRKAP,
            'activeKiln'    => $count,
            'calTime'       => $calTime
        ];
    }

    public function kilnrateSummaryGraph(Request $request)
    {
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select('kode_plant',
                DB::raw('COALESCE(SUM(oph),0) as oph'),
                DB::raw('COALESCE(SUM(stop_idle),0)  as stop_idle'),
                DB::raw('COALESCE(SUM(act_prod),0)  as act_prod'),
                DB::raw('COALESCE(SUM(act_idle_prod),0)  as act_idle_prod'),
                DB::raw('COALESCE(SUM(koreksi),0)  as koreksi'),
            );

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select('kode_plant',
                DB::raw('COALESCE(SUM(oph),0)  as oph'),
                DB::raw('COALESCE(SUM(stop_idle),0)  as stop_idle'),
                DB::raw('COALESCE(SUM(act_prod),0)  as act_prod'),
                DB::raw('COALESCE(SUM(act_idle_prod),0)  as act_idle_prod'),
                DB::raw('COALESCE(SUM(koreksi),0)  as koreksi'),
            );
        }
        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select('kode_plant',
            DB::raw('COALESCE(AVG(prod_rate),0) as rkap_ton_day'),
        );
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataReal = $dataReal->groupBy('kode_plant')->get();
        $dataRKAP = $dataRKAP->groupBy('kode_plant')->get();

        $gross_real = 0;
        foreach ($dataReal as $key => $item) {
            if(($item->oph / 24) >=1){
                $gross_real += $item->act_prod / ($item->oph / 24);
            }else{
                $gross_real += $item->act_prod;
            }
        }

        $gross_rkap = 0;
        foreach ($dataRKAP as $key => $value) {
            $gross_rkap += $value->rkap_ton_day;
        }

        $netto_real = 0;
        foreach ($dataReal as $key => $item) {
            if((($item->oph + $item->stop_idle) / 24) >=1){
                $netto_real += ($item->act_prod + $item->act_idle_prod + $item->koreksi) / (($item->oph + $item->stop_idle) / 24);
            }else{
                $netto_real += $item->act_prod + $item->act_idle_prod + $item->koreksi;
            }
        }

        $act_rate_result = $gross_real;
        $rate_updt_result = ($gross_rkap - $netto_real) > 0 ? ($gross_rkap - $netto_real):0;
        $rate_idle_result = ($netto_real - $gross_real) > 0 ? ($netto_real - $gross_real):0;
        $total = $act_rate_result + $rate_updt_result + $rate_idle_result;
        $act_rate = $total>0?$act_rate_result / $total * 100:0;
        $rate_updt =  $total>0?$rate_updt_result / $total * 100:0;
        $rate_idle =  $total>0?$rate_idle_result / $total * 100:0;

        $rate_rkap = round($gross_rkap,1);
        return [
            'message'   => 'Succes',
            'data'      => round($total, 1) . '#' . round($act_rate, 2) . '#' . round($rate_updt, 2) . '#' . round($rate_idle, 2)
                            . '#' . round($act_rate_result, 1). '#' . round($rate_updt_result, 1). '#' . round($rate_idle_result, 1),
            'rateRKAP'  => $rate_rkap
        ];

    }

    public function FrekUpdtChart(Request $request)
    {
        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant','no_pbi');
        if($request->opco){
            $no_pbi = $no_pbi -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $no_pbi = $no_pbi -> where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
                            ->get();

        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(frek_updt) as frek_updt'),
            );

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(frek_updt) as frek_updt'),
            );
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->orderBy('kode_plant', 'ASC')
                            ->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(frek_updt) as frek_updt'),
            );
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->orderBy('kode_plant', 'ASC')
                            ->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->get();

        $data = [];
        foreach($dataReal as $k => $v){
            $data[$v->kode_plant]['real_frek_updt'] = $v->frek_updt;
        }

        foreach($dataRKAP as $k => $v){
            $data[$v->kode_plant]['rkap_frek_updt'] = $v->frek_updt;
        }

        $dtSort=[];
        foreach($no_pbi as $k => $v){
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant,$data)?$data[$v->kode_plant]:[];
        }

        $data = NULL;
        foreach($dtSort as $k =>$v){
            $list_kode_plant[] = $k;
            $data[$k]['real_frek_updt'] = array_key_exists('real_frek_updt',$v)?(int)$v['real_frek_updt']:0;
            $data[$k]['rkap_frek_updt'] = array_key_exists('rkap_frek_updt',$v)?(int)$v['rkap_frek_updt']:0;
        }

        return [
            'message' => 'Succes',
            'list_kode_plant' => $list_kode_plant,
            'data_updt' => $data
        ];
    }

    function KilnOpDetailStackChart(Request $request){
        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant','no_pbi');
        if($request->opco){
            $no_pbi = $no_pbi -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $no_pbi = $no_pbi -> where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
                            ->get();
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(fy_stop) as fy_stop'),
                DB::raw('SUM(pdt) as pdt'),
                DB::raw('SUM(updt) as updt'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(oph) as oph'),
            );

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(fy_stop) as fy_stop'),
                DB::raw('SUM(pdt) as pdt'),
                DB::raw('SUM(updt) as updt'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(oph) as oph'),
            );
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->orderBy('kode_plant', 'ASC')
                            ->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->get();
        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(cal) as cal'),
            );
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->orderBy('kode_plant', 'ASC')
                            ->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->get();

        if($request->tanggal){
            $countDay = $dataRKAP[0]->cal / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
            $firstDay = (int) substr($request->tanggal, 3, 2);
            $lastDay = (int) substr($request->tanggal, 16, 2);
            $rangeDay = $lastDay - $firstDay + 1;
            $dataRKAP[0]->oph = $dataRKAP[0]->oph * $rangeDay / $countDay;
            $dataRKAP[0]->cal = $dataRKAP[0]->cal * $rangeDay / $countDay;
        }

        $data = [];
        foreach($dataReal as $k => $v){
            $data[$v->kode_plant]['real_fy_stop'] = $v->fy_stop;
            $data[$v->kode_plant]['real_pdt'] = $v->pdt;
            $data[$v->kode_plant]['real_updt'] = $v->updt;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_oph'] = $v->oph;
        }
        foreach($dataRKAP as $k => $v){
            $data[$v->kode_plant]['rkap_oph'] = $v->oph;
        }

        //sort by NO_PBI
        $dtSort=[];
        foreach($no_pbi as $k => $v){
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant,$data)?$data[$v->kode_plant]:[];
        }
        $list_kode_plant = [];
        $real_fy_stop = [];
        $real_pdt = [];
        $real_updt = [];
        $real_stop_idle = [];
        $real_oph = [];
        $rkap_oph = [];
        foreach($dtSort as $k =>$v){
            $list_kode_plant[] =  $k;
            $real_fy_stop[] = array_key_exists('real_fy_stop',$v)?(float)round($v['real_fy_stop'],2):0;
            $real_pdt[] = array_key_exists('real_pdt',$v)?(float)round($v['real_pdt'],2):0;
            $real_updt[] = array_key_exists('real_updt',$v)?(float)round($v['real_updt'],2):0;
            $real_stop_idle[] = array_key_exists('real_stop_idle',$v)?(float)round($v['real_stop_idle'],2):0;
            $real_oph[] = array_key_exists('real_oph',$v)?(float)round($v['real_oph'],2):0;
            $rkap_oph[] = array_key_exists('rkap_oph',$v)?(float)round($v['rkap_oph'],2):0;
        }
        return [
            'message' => 'Succes',
            'real_fy_stop' => $real_fy_stop,
            'real_pdt' => $real_pdt,
            'real_updt' => $real_updt,
            'real_stop_idle' => $real_stop_idle,
            'real_oph' => $real_oph,
            'rkap_oph' => $rkap_oph,
            'kode_plant' => $list_kode_plant
        ];
    }

    public function KilnOperationDetailsNaiChart(Request $request)
    {
        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant','no_pbi');
        if($request->opco){
            $no_pbi = $no_pbi -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $no_pbi = $no_pbi -> where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
                            ->get();

        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(cal) as cal'),
            );

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(cal) as cal'),
            );
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->orderBy('kode_plant', 'ASC')
                            ->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(cal) as cal'),
            );
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->orderBy('kode_plant', 'ASC')
                            ->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->get();
        $data = [];
        foreach($dataReal as $k => $v){
            $data[$v->kode_plant]['real_oph'] = $v->oph;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_cal'] = $v->cal;
        }

        foreach($dataRKAP as $k => $v){
            $data[$v->kode_plant]['rkap_oph'] = $v->oph;
            $data[$v->kode_plant]['rkap_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['rkap_cal'] = $v->cal;
        }

        $dtSort=[];
        foreach($no_pbi as $k => $v){
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant,$data)?$data[$v->kode_plant]:[];
        }

        $data = NULL;
        foreach($dtSort as $k =>$v){
            $list_kode_plant[] = $k;
            $data[$k]['real_oph'] = array_key_exists('real_oph',$v)?$v['real_oph']:0;
            $data[$k]['real_stop_idle'] = array_key_exists('real_stop_idle',$v)?$v['real_stop_idle']:0;
            $data[$k]['real_cal'] = array_key_exists('real_cal',$v)?$v['real_cal']:0;
            $data[$k]['rkap_oph'] = array_key_exists('rkap_oph',$v)?$v['rkap_oph']:0;
            $data[$k]['rkap_stop_idle'] = array_key_exists('rkap_stop_idle',$v)?$v['rkap_stop_idle']:0;
            $data[$k]['rkap_cal'] = array_key_exists('rkap_cal',$v)?$v['rkap_cal']:0;
        }
        foreach ($data as $key => $value) {
            if(($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) > 1){
                $data[$key]['nai_real'] = round((($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) / $data[$key]['real_cal']) * 100, 1);
            }else{
                $data[$key]['nai_real'] = round($data[$key]['real_oph'] + $data[$key]['real_stop_idle'], 1);
            }
        }

        foreach ($data as $key => $value) {
            if(($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle']) > 1 && $data[$key]['rkap_cal'] > 0){
                $data[$key]['nai_rkap'] = round((($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle']) / $data[$key]['rkap_cal']) * 100, 1);
            }else{
                $data[$key]['nai_rkap'] = round($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle'], 1);
            }
        }

        if($request->tanggal){
            foreach ($data as $key => $value) {
                $netDays = round(($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle'])/24,0);
                $NAIPerDay = $data[$key]['nai_rkap'] / $netDays;
                // $countDay = $data[$key]['rkap_cal'] / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
                $firstDay = (int) substr($request->tanggal, 3, 2);
                $lastDay = (int) substr($request->tanggal, 16, 2);
                $rangeDay = $lastDay - $firstDay + 1;
                if ($rangeDay > $netDays) {
                    $data[$key]['nai_rkap'] = $data[$key]['nai_rkap'];
                }
                else{
                    $data[$key]['nai_rkap'] = round($NAIPerDay * $rangeDay, 1);
                }
                // if ($countDay != 0) {
                //     $data[$key]['rkap_oph'] = $data[$key]['rkap_oph'] * $rangeDay / $countDay;
                //     $data[$key]['rkap_stop_idle'] = $data[$key]['rkap_stop_idle'] * $rangeDay / $countDay;
                //     $data[$key]['rkap_cal'] = $data[$key]['rkap_cal'] * $rangeDay / $countDay;
                // }
            }
        }

        return [
            'message' => 'Succes',
            'data_nai' => $data,
            'list_kode_plant' => $list_kode_plant
        ];
    }

    public function KilnRateChart(Request $request)
    {
        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant','no_pbi');
        if($request->opco){
            $no_pbi = $no_pbi -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $no_pbi = $no_pbi -> where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
                            ->get();

        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(act_prod) as act_prod'),
                DB::raw('SUM(act_idle_prod) as act_idle_prod'),
                DB::raw('SUM(koreksi) as koreksi'),
                DB::raw('SUM(cal) as cal'),
            );

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(act_prod) as act_prod'),
                DB::raw('SUM(act_idle_prod) as act_idle_prod'),
                DB::raw('SUM(koreksi) as koreksi'),
                DB::raw('SUM(cal) as cal'),
            );
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->orderBy('kode_plant', 'ASC')
                            ->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('avg(prod_rate) as avg_act_prod'),
                // DB::raw('SUM(act_prod) as sum_act_prod'),
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(cal) as cal'),
                // DB::raw('sum(act_idle_prod) as act_idle_prod'),
                // DB::raw('sum(koreksi) as koreksi'),
            );
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->orderBy('kode_plant', 'ASC')
                            ->get();
        $data = [];
        foreach($dataReal as $k => $v){
            $data[$v->kode_plant]['real_oph'] = $v->oph;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_act_prod'] = $v->act_prod;
            $data[$v->kode_plant]['real_act_idle_prod'] = $v->act_idle_prod;
            $data[$v->kode_plant]['real_koreksi'] = $v->koreksi;
            $data[$v->kode_plant]['real_cal'] = $v->cal;
        }

        foreach($dataRKAP as $k => $v){
            $data[$v->kode_plant]['rkap_oph'] = $v->oph;
            $data[$v->kode_plant]['rkap_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['rkap_cal'] = $v->cal;
            $data[$v->kode_plant]['rkap_avg_act_prod'] = $v->avg_act_prod;
            // $data[$v->kode_plant]['rkap_sum_act_prod'] = $v->sum_act_prod;
            // $data[$v->kode_plant]['rkap_act_idle_prod'] = $v->act_idle_prod;
            // $data[$v->kode_plant]['rkap_koreksi'] = $v->koreksi;
        }

        $dtSort=[];
        foreach($no_pbi as $k => $v){
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant,$data)?$data[$v->kode_plant]:[];
        }

        $data = NULL;
        foreach($dtSort as $k =>$v){
            $list_kode_plant[] = $k;
            $data[$k]['real_oph'] = array_key_exists('real_oph',$v)?$v['real_oph']:0;
            $data[$k]['real_stop_idle'] = array_key_exists('real_stop_idle',$v)?$v['real_stop_idle']:0;
            $data[$k]['real_act_prod'] = array_key_exists('real_act_prod',$v)?$v['real_act_prod']:0;
            $data[$k]['real_act_idle_prod'] = array_key_exists('real_act_idle_prod',$v)?$v['real_act_idle_prod']:0;
            $data[$k]['real_koreksi'] = array_key_exists('real_koreksi',$v)?$v['real_koreksi']:0;
            $data[$k]['real_cal'] = array_key_exists('real_cal',$v)?$v['real_cal']:0;
            $data[$k]['rkap_oph'] = array_key_exists('rkap_oph',$v)?$v['rkap_oph']:0;
            $data[$k]['rkap_stop_idle'] = array_key_exists('rkap_stop_idle',$v)?$v['rkap_stop_idle']:0;
            $data[$k]['rkap_cal'] = array_key_exists('rkap_cal',$v)?$v['rkap_cal']:0;
            $data[$k]['rkap_avg_act_prod'] = array_key_exists('rkap_avg_act_prod',$v)?$v['rkap_avg_act_prod']:0;
            // $data[$k]['rkap_koreksi'] = array_key_exists('rkap_koreksi',$v)?$v['rkap_koreksi']:0;
        }

        if($request->tanggal){
            foreach ($data as $key => $value) {
                $countDay = $data[$key]['rkap_cal'] / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
                $firstDay = (int) substr($request->tanggal, 3, 2);
                $lastDay = (int) substr($request->tanggal, 16, 2);
                $rangeDay = $lastDay - $firstDay + 1;
                if ($countDay != 0) {
                    $data[$key]['rkap_stop_idle'] = $data[$key]['rkap_stop_idle'] * $rangeDay / $countDay;
                    $data[$key]['rkap_cal'] = $data[$key]['rkap_cal'] * $rangeDay / $countDay;
                    $data[$key]['rkap_oph'] = $data[$key]['rkap_oph'] * $rangeDay / $countDay;
                    $data[$key]['rkap_avg_act_prod'] = $data[$key]['rkap_avg_act_prod'] * $rangeDay / $countDay;
                    // $data[$key]['rkap_koreksi'] = $data[$key]['rkap_koreksi'] * $rangeDay / $countDay;
                }
            }
        }

        $data_value = [];

        foreach ($data as $i => $v) {
            if(($data[$i]['real_oph'] + $data[$i]['real_stop_idle'] / 24) >=1){
                $data_value[$i]['rate_netto_real'] =  round(($data[$i]['real_act_prod'] + $data[$i]['real_act_idle_prod'] + $data[$i]['real_koreksi']) / (($data[$i]['real_oph'] + $data[$i]['real_stop_idle'])/24),1);
            }else{
                $data_value[$i]['rate_netto_real'] =  round($data[$i]['real_act_prod'] + $data[$i]['real_act_idle_prod'] + $data[$i]['real_koreksi'],1);
            }

            if(($data[$i]['real_oph']  / 24) >=1){
                $data_value[$i]['rate_gross_real'] =  round($data[$i]['real_act_prod'] / ($data[$i]['real_oph']/24),1);
            }else{
                $data_value[$i]['rate_gross_real'] =   round($data[$i]['real_act_prod'],1);
            }

            $data_value[$i]['rate_gross_rkap'] =  round($data[$i]['rkap_avg_act_prod'],1);
        }

        $data = [
            'kiln_rate'      => $data_value,
            'list_kode_plant' => $list_kode_plant
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function getKilnGrosOeeChart(Request $request)
    {
        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant','no_pbi');
        if($request->opco){
            $no_pbi = $no_pbi -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $no_pbi = $no_pbi -> where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
                            ->get();

        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(act_prod) as act_prod'),
                DB::raw('SUM(act_idle_prod) as act_idle_prod'),
                DB::raw('SUM(koreksi) as koreksi'),
                DB::raw('SUM(cal) as cal'),
            );

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(act_prod) as act_prod'),
                DB::raw('SUM(act_idle_prod) as act_idle_prod'),
                DB::raw('SUM(koreksi) as koreksi'),
                DB::raw('SUM(cal) as cal'),
            );
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->orderBy('kode_plant', 'ASC')
                            ->get();
        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                // DB::raw('SUM(koreksi) as koreksi'),
                // DB::raw('SUM(act_idle_prod) as act_idle_prod'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(cal) as cal'),
                DB::raw('AVG(bdp_rate) as bdp_rate'),
                DB::raw('AVG(prod_rate) as prod_rate'),
                // DB::raw('SUM(act_prod) as act_prod'),
            );
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->orderBy('kode_plant', 'ASC')
                            ->get();

        $data = [];
        foreach($dataReal as $k => $v){
            $data[$v->kode_plant]['real_oph'] = $v->oph;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_act_prod'] = $v->act_prod;
            $data[$v->kode_plant]['real_act_idle_prod'] = $v->act_idle_prod;
            $data[$v->kode_plant]['real_koreksi'] = $v->koreksi;
            $data[$v->kode_plant]['real_cal'] = $v->cal;
        }

        foreach($dataRKAP as $k => $v){
            $data[$v->kode_plant]['rkap_prod_rate'] = $v->prod_rate;
            $data[$v->kode_plant]['rkap_bdp_rate'] = $v->bdp_rate;
            $data[$v->kode_plant]['rkap_oph'] = $v->oph;
            $data[$v->kode_plant]['rkap_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['rkap_cal'] = $v->cal;
            // $data[$v->kode_plant]['rkap_act_prod'] = $v->act_prod;
            // $data[$v->kode_plant]['rkap_act_idle_prod'] = $v->act_idle_prod;
            // $data[$v->kode_plant]['rkap_koreksi'] = $v->koreksi;
        }

        $dtSort=[];
        foreach($no_pbi as $k => $v){
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant,$data)?$data[$v->kode_plant]:[];
        }

        $data = NULL;
        foreach($dtSort as $k =>$v){
            $list_kode_plant[] = $k;
            $data[$k]['real_oph'] = array_key_exists('real_oph',$v)?$v['real_oph']:0;
            $data[$k]['real_stop_idle'] = array_key_exists('real_stop_idle',$v)?$v['real_stop_idle']:0;
            $data[$k]['real_act_prod'] = array_key_exists('real_act_prod',$v)?$v['real_act_prod']:0;
            $data[$k]['real_act_idle_prod'] = array_key_exists('real_act_idle_prod',$v)?$v['real_act_idle_prod']:0;
            $data[$k]['real_koreksi'] = array_key_exists('real_koreksi',$v)?$v['real_koreksi']:0;
            $data[$k]['real_cal'] = array_key_exists('real_cal',$v)?$v['real_cal']:0;
            $data[$k]['rkap_prod_rate'] = array_key_exists('rkap_prod_rate',$v)?$v['rkap_prod_rate']:0;
            $data[$k]['rkap_bdp_rate'] = array_key_exists('rkap_bdp_rate',$v)?$v['rkap_bdp_rate']:0;
            $data[$k]['rkap_oph'] = array_key_exists('rkap_oph',$v)?$v['rkap_oph']:0;
            $data[$k]['rkap_stop_idle'] = array_key_exists('rkap_stop_idle',$v)?$v['rkap_stop_idle']:0;
            $data[$k]['rkap_cal'] = array_key_exists('rkap_cal',$v)?$v['rkap_cal']:0;
            // $data[$k]['rkap_act_prod'] = array_key_exists('rkap_act_prod',$v)?$v['rkap_act_prod']:0;
            // $data[$k]['rkap_act_idle_prod'] = array_key_exists('rkap_act_idle_prod',$v)?$v['rkap_act_idle_prod']:0;
            // $data[$k]['rkap_koreksi'] = array_key_exists('rkap_koreksi',$v)?$v['rkap_koreksi']:0;
        }

        if($request->tanggal){
            foreach ($data as $key => $value) {
                $countDay = $data[$key]['rkap_cal'] / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
                $firstDay = (int) substr($request->tanggal, 3, 2);
                $lastDay = (int) substr($request->tanggal, 16, 2);
                $rangeDay = $lastDay - $firstDay + 1;
                if ($countDay != 0) {
                    $data[$key]['rkap_prod_rate'] = $data[$key]['rkap_prod_rate'] * $rangeDay / $countDay;
                    $data[$key]['rkap_bdp_rate'] = $data[$key]['rkap_bdp_rate'] * $rangeDay / $countDay;
                    $data[$key]['rkap_cal'] = $data[$key]['rkap_cal'] * $rangeDay / $countDay;
                    $data[$key]['rkap_oph'] = $data[$key]['rkap_oph'] * $rangeDay / $countDay;
                    // $data[$key]['rkap_act_prod'] = $data[$key]['rkap_act_prod'] * $rangeDay / $countDay;
                    // $data[$key]['rkap_act_idle_prod'] = $data[$key]['rkap_act_idle_prod'] * $rangeDay / $countDay;
                    // $data[$key]['rkap_koreksi'] = $data[$key]['rkap_koreksi'] * $rangeDay / $countDay;
                    $data[$key]['rkap_stop_idle'] = $data[$key]['rkap_stop_idle'] * $rangeDay / $countDay;
                }
            }
        }

        $data_value = [];

        foreach ($data as $i => $v) {
            // oee_gross_real
            if (($data[$i]['real_oph']  / 24) >= 1) {
                $rate_gross_real =  $data[$i]['real_act_prod'] / ($data[$i]['real_oph'] / 24);
            } else {
                $rate_gross_real =  $data[$i]['real_act_prod'];
            }

            if ($data[$i]['rkap_bdp_rate'] > 1) {
                $yield_gross_real = $rate_gross_real / $data[$i]['rkap_bdp_rate'];
            } else {
                $yield_gross_real = $rate_gross_real;
            }

            if ($data[$i]['real_cal'] > 1) {
                $gai_real = $data[$i]['real_oph'] / $data[$i]['real_cal'];
            } else {
                $gai_real = $data[$i]['real_oph'];
            }

            $data_value[$i]['oee_gross_real'] = round(round(($yield_gross_real * $gai_real),2)*100,1);

            // oee_gross_rkap
            $rate_gross_rkap = $data[$i]['rkap_prod_rate'];


            if ($data[$i]['rkap_bdp_rate'] > 1) {
                $yield_gross_rkap = $rate_gross_rkap / $data[$i]['rkap_bdp_rate'];
            } else {
                $yield_gross_rkap = $rate_gross_rkap;
            }

            if ($data[$i]['rkap_cal'] > 1) {
                $gai_rkap = $data[$i]['rkap_oph'] / $data[$i]['rkap_cal'];
            } else {
                $gai_rkap = $data[$i]['rkap_oph'];
            }

            $data_value[$i]['oee_gross_rkap'] = round(round(($yield_gross_rkap * $gai_rkap),2)*100,1);
        }

        $data = [
            'oee_gross'      => $data_value,
            'list_kode_plant' => $list_kode_plant
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function KilnNettoOEEChart(Request $request)
    {
        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant','no_pbi');
        if($request->opco){
            $no_pbi = $no_pbi -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $no_pbi = $no_pbi -> where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
                            ->get();
        if($request->tanggal){
            $dataReal = DB::table('ts_realisasi_performance_daily')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(ROUND(oph::numeric,2)) as oph'),
                DB::raw('SUM(ROUND(stop_idle::numeric,2)) as stop_idle'),
                DB::raw('SUM(ROUND(act_prod::numeric,2)) as act_prod'),
                DB::raw('SUM(ROUND(act_idle_prod::numeric,2)) as act_idle_prod'),
                DB::raw('SUM(ROUND(koreksi::numeric,2)) as koreksi'),
                DB::raw('SUM(ROUND(cal::numeric,2)) as cal'),
            );

            $firstDay = substr($request->tanggal, 0, 10);
            $lastDay = substr($request->tanggal, 13, 10);
            $dataReal = $dataReal -> whereBetween('tanggal',[$firstDay, $lastDay]);
        }
        else{
            $dataReal = DB::table('ts_realisasi_performance')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(ROUND(oph::numeric,2)) as oph'),
                DB::raw('SUM(ROUND(stop_idle::numeric,2)) as stop_idle'),
                DB::raw('SUM(ROUND(act_prod::numeric,2)) as act_prod'),
                DB::raw('SUM(ROUND(act_idle_prod::numeric,2)) as act_idle_prod'),
                DB::raw('SUM(ROUND(koreksi::numeric,2)) as koreksi'),
                DB::raw('SUM(ROUND(cal::numeric,2)) as cal'),
            );
        }
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataReal = $dataReal -> where(DB::raw("extract(year from tanggal)"), $request->tahun);
        }
        if($request->bulan){
            $dataReal = $dataReal -> whereIn(DB::raw("extract(month from tanggal)"), $request->bulan);
        }
        $dataReal = $dataReal->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->orderBy('kode_plant', 'ASC')
                            ->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
                'kode_plant as kode_plant',
                'kode_opco as kode_opco',
                DB::raw('SUM(oph) as oph'),
                DB::raw('SUM(stop_idle) as stop_idle'),
                DB::raw('SUM(cal) as cal'),
                DB::raw('AVG(bdp_rate) as bdp'),
                DB::raw('AVG(prod_rate) as prod_rate'),
            );
        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }
        if($request->tahun){
            $dataRKAP = $dataRKAP -> where('tahun', $request->tahun);
        }
        if($request->bulan){
            $dataRKAP = $dataRKAP -> whereIn('bulan', $request->bulan);
        }
        $dataRKAP = $dataRKAP->groupBy('kode_opco')
                            ->groupBy('kode_plant')
                            ->orderBy('kode_plant', 'ASC')
                            ->get();
        $data = [];
        foreach($dataReal as $k => $v){
            $data[$v->kode_plant]['real_oph'] = $v->oph;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_act_prod'] = $v->act_prod;
            $data[$v->kode_plant]['real_act_idle_prod'] = $v->act_idle_prod;
            $data[$v->kode_plant]['real_koreksi'] = $v->koreksi;
            $data[$v->kode_plant]['real_cal'] = $v->cal;
        }

        foreach($dataRKAP as $k => $v){
            $data[$v->kode_plant]['rkap_oph'] = $v->oph;
            $data[$v->kode_plant]['rkap_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['rkap_cal'] = $v->cal;
            $data[$v->kode_plant]['rkap_bdp'] = $v->bdp;
            $data[$v->kode_plant]['rkap_prod_rate'] = $v->prod_rate;
        }

        $dtSort=[];
        foreach($no_pbi as $k => $v){
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant,$data)?$data[$v->kode_plant]:[];
        }

        $data = NULL;
        foreach($dtSort as $k =>$v){
            $data[$k]['real_oph'] = array_key_exists('real_oph',$v)?$v['real_oph']:0;
            $data[$k]['real_stop_idle'] = array_key_exists('real_stop_idle',$v)?$v['real_stop_idle']:0;
            $data[$k]['real_act_prod'] = array_key_exists('real_act_prod',$v)?$v['real_act_prod']:0;
            $data[$k]['real_act_idle_prod'] = array_key_exists('real_act_idle_prod',$v)?$v['real_act_idle_prod']:0;
            $data[$k]['real_koreksi'] = array_key_exists('real_koreksi',$v)?$v['real_koreksi']:0;
            $data[$k]['real_cal'] = array_key_exists('real_cal',$v)?$v['real_cal']:0;
            $data[$k]['rkap_oph'] = array_key_exists('rkap_oph',$v)?$v['rkap_oph']:0;
            $data[$k]['rkap_stop_idle'] = array_key_exists('rkap_stop_idle',$v)?$v['rkap_stop_idle']:0;
            $data[$k]['rkap_cal'] = array_key_exists('rkap_cal',$v)?$v['rkap_cal']:0;
            $data[$k]['rkap_bdp'] = array_key_exists('rkap_bdp',$v)?$v['rkap_bdp']:0;
            $data[$k]['rkap_prod_rate'] = array_key_exists('rkap_prod_rate',$v)?$v['rkap_prod_rate']:0;
        }

        foreach ($data as $key => $value) {
            if((($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) / 24) >=1){
                $data[$key]['tpd'] = ($data[$key]['real_act_prod'] + $data[$key]['real_act_idle_prod'] + $data[$key]['real_koreksi']) / (($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) / 24);
            }else{
                $data[$key]['tpd'] = $data[$key]['real_act_prod'] + $data[$key]['real_act_idle_prod'] + $data[$key]['real_koreksi'];
            }
        }

        foreach ($data as $key => $value) {
            if($data[$key]['tpd'] > 1 && $data[$key]['rkap_bdp'] > 0){
                $data[$key]['yield'] = ($data[$key]['tpd'] / $data[$key]['rkap_bdp'])*100;
            }else{
                $data[$key]['yield'] = $data[$key]['tpd']*100;
            }
        }

        foreach ($data as $key => $value) {
            if(($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) >1){
                $data[$key]['nai'] = (($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) / $data[$key]['real_cal'])*100;
            }else{
                $data[$key]['nai'] = ($data[$key]['real_oph'] + $data[$key]['real_stop_idle'])*100;
            }
        }

        if($request->tanggal){
            foreach ($data as $key => $value) {
                $countDay = $data[$key]['rkap_cal'] / 24 ; // jumlah hari dalam satu bulan; 24 = 24jam
                $firstDay = (int) substr($request->tanggal, 3, 2);
                $lastDay = (int) substr($request->tanggal, 16, 2);
                $rangeDay = $lastDay - $firstDay + 1;
                if ($countDay != 0) {
                    $data[$key]['rkap_oph'] = (int) $data[$key]['rkap_oph'] * $rangeDay / $countDay;
                    $data[$key]['rkap_stop_idle'] = (int) $data[$key]['rkap_stop_idle'] * $rangeDay / $countDay;
                    $data[$key]['rkap_cal'] = (int) $data[$key]['rkap_cal'] * $rangeDay / $countDay;
                    $data[$key]['rkap_bdp'] = (int) $data[$key]['rkap_bdp'] * $rangeDay / $countDay;
                    $data[$key]['rkap_prod_rate'] = (int) $data[$key]['rkap_prod_rate'] * $rangeDay / $countDay;
                }
            }
        }
        foreach ($data as $key => $value) {
            if($data[$key]['tpd'] > 1 && $data[$key]['rkap_bdp'] > 0){
                $data[$key]['rkap_yield'] = ($data[$key]['tpd'] / $data[$key]['rkap_bdp'])*100;
            }else{
                $data[$key]['rkap_yield'] = $data[$key]['tpd']*100;
            }
        }

        foreach ($data as $key => $value) {
            if(($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle']) > 1 && $data[$key]['real_cal'] != 0){
                $data[$key]['rkap_nai'] = (($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle']) / $data[$key]['real_cal'])*100;
            }else{
                $data[$key]['rkap_nai'] = ($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle'])*100;
            }
        }

        foreach ($data as $key => $value) {
            $data[$key]['hasil'] = (round($data[$key]['yield'],1) * round($data[$key]['nai'],1)) / 100;
        }

        $list_kode_plant = [];
        $datahasil = [];
        $i=0;

        foreach ($data as $key => $value) {
            $i++;
            $data_hasil = array_key_exists('hasil', $value)?(float)$value['hasil']:0;
            $datahasil[$key] = round($data_hasil,1);
        }

        asort($datahasil);
        $dataSort = [];
        $i=0;
        foreach ($datahasil as $key => $value) {
            $i++;
            if ($value > 85) {
                $dataSort[$i]['key'] = $key;
                $dataSort[$i]['data'] = $value;
                $dataSort[$i]['color'] = '#B4C7DA';
            } elseif ($value > 70) {
                $dataSort[$i]['key'] = $key;
                $dataSort[$i]['data'] = $value;
                $dataSort[$i]['color'] = '#FBD677';
            } else {
                $dataSort[$i]['key'] = $key;
                $dataSort[$i]['data'] = $value;
                $dataSort[$i]['color'] = '#E87D79';
            }
        }

        return [
            'message' => 'Succes',
            'dataSort' => $dataSort
        ];
    }
}
