<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use App\Models\RoleHasMenu;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Models\ItemInspection;

class EquipHealthyController extends Controller
{
    public function index(Request $request)
    {
        $data = [
            'title' => 'Plant Equipment Healthy',
            'breadcrumb' => [
                [
                    'title'=>'Plant Equipment Healthy',
                    'url'=>'',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();

        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i = 2019; $i <= $yearNow; $i++) {
            if ($i == $yearNow) {
                $tahun[] = [
                    'tahun' => $i,
                    'selected' => true
                ];
            } else {
                $tahun[] = [
                    'tahun' => $i,
                    'selected' => false
                ];
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);
        $optTahun = "";
        foreach ($tahun as $value) {
            $tahun = $value['tahun'];
            $selected = $value['selected'];
            if ($selected == true) {
                $optTahun .= "<option value=$tahun selected>$tahun</option>";
            } else {
                $optTahun .= "<option value=$tahun>$tahun</option>";
            }
        }
        $data['tahun'] = $optTahun;


        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $optBulan = "";
        for ($i = 0; $i < count($months); $i++) {
            $noBulan = strval($i + 1);
            if ($i + 1 == date('m')) {
                $optBulan .= "<option value=$noBulan>$months[$i]</option>";
            } else {
                $optBulan .= "<option value=$noBulan>$months[$i]</option>";
            }
        }
        $data['bulan'] = $optBulan;
        return view('plantEquipment', $data);
    }

    public function getSummaryEquipHealth(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
            'bulan' => 'nullable|numeric',
            'tahun' => 'nullable|numeric',
        ]);
        $data_count = ItemInspection::where('no_inspection', $request->no_inspection)->count();

        $arr_bind = [];
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $val_opco = $request->opco;
            $filter_opco = "AND kode_opco = ?";
            $arr_bind[] = $val_opco;
        }

        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $val_plant = $request->plant;
            $filter_plant = "AND kode_plant = ?";
            $arr_bind[] = $val_plant;
        }

        if ($request->bulan == "empty" || $request->bulan == "") {
            $filter_bulan = '';
        } else {
            $val_bulan = $request->bulan;
            $filter_bulan = "AND DATE_PART('month', t_item_inspection.create_date::date) = ?";
            $arr_bind[] = $val_bulan;
        }

        if ($request->tahun == "empty" || $request->tahun == "") {
            $filter_tahun = '';
        } else {
            $val_tahun = $request->tahun;
            $filter_tahun = "AND DATE_PART('year', t_item_inspection.create_date::date) = ?";
            $arr_bind[] = $val_tahun;
        }



        // $data_risk = DB::select("select nm_area,
        // COUNT(CASE WHEN m_kondisi.nm_kondisi = 'GOOD' THEN 1 END) AS GOOD,
        // COUNT(CASE WHEN m_kondisi.nm_kondisi = 'LOW RISK' THEN 1 END) AS LOW_RISK,
        // COUNT(CASE WHEN m_kondisi.nm_kondisi = 'MED RISK' THEN 1 END) AS MED_RISK,
        // COUNT(CASE WHEN m_kondisi.nm_kondisi = 'HIGH RISK' THEN 1 END) AS HIGH_RISK
        // FROM t_item_inspection
        // join m_kondisi on t_item_inspection.id_kondisi = m_kondisi.id_kondisi
        // join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection
        // WHERE   " . $filter_plant . "  and " . $filter_opco . "
        // GROUP BY t_item_inspection.nm_area");

        $data_risk = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE t_plant_inspection.status = 'Approve'
                        " . $filter_plant . "  " . $filter_opco . " " . $filter_tahun . " " . $filter_bulan . " 
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area", $arr_bind);



        return [
            'message' => 'Succes Get Data',
            'data_risk' => $data_risk,
            'data_count' => $data_count
        ];
    }

    public function getTooltipEquipHealth(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
            'bulan' => 'nullable|numeric',
            'tahun' => 'nullable|numeric',
        ]);
        $arr_bind = [];
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $val_opco = $request->opco;
            $filter_opco = "AND t_plant_inspection.kode_opco = ?";
            $arr_bind[] = $val_opco;
        }

        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $val_plant = $request->plant;
            $filter_plant = "AND t_plant_inspection.kode_plant = ?";
            $arr_bind[] = $val_plant;
        }

        if ($request->bulan == "empty" || $request->bulan == "") {
            $filter_bulan = '';
        } else {
            $val_bulan = $request->bulan;
            $filter_bulan = "AND DATE_PART('month', t_item_inspection.create_date::date) = ?";
            $arr_bind[] = $val_bulan;
        }

        if ($request->tahun == "empty" || $request->tahun == "") {
            $filter_tahun = '';
        } else {
            $val_tahun = $request->tahun;
            $filter_tahun = "AND DATE_PART('year', t_item_inspection.create_date::date) = ?";
            $arr_bind[] = $val_tahun;
        }


        $data_crusher = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve' and nm_area =  'CRUSHER')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE t_plant_inspection.status = 'Approve'
                        " . $filter_plant . " " . $filter_opco . "   and nm_area =  'CRUSHER' " . $filter_tahun . " " . $filter_bulan . " 
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area", $arr_bind);


        $last_date_crusher = ItemInspection::where('nm_area', 'CRUSHER')
            ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
            ->where('status', 'Approve')
            ->orderBy('t_item_inspection.create_date', 'desc')
            ->first();

        $last_date_rawmill = ItemInspection::where('nm_area', 'RAWMILL')
            ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
            ->where('status', 'Approve')
            ->orderBy('t_item_inspection.create_date', 'desc')
            ->first();

        $last_date_coalmill = ItemInspection::where('nm_area', 'COAL MILL')
            ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
            ->where('status', 'Approve')
            ->orderBy('t_item_inspection.create_date', 'desc')
            ->first();

        $last_date_kiln = ItemInspection::where('nm_area', 'KILN')
            ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
            ->where('status', 'Approve')
            ->orderBy('t_item_inspection.create_date', 'desc')
            ->first();

        $last_date_cementmill = ItemInspection::where('nm_area', 'CEMENT MILL')
            ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
            ->where('status', 'Approve')
            ->orderBy('t_item_inspection.create_date', 'desc')
            ->first();

        $last_date_packer = ItemInspection::where('nm_area', 'PACKER')
            ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
            ->where('status', 'Approve')
            ->orderBy('t_item_inspection.create_date', 'desc')
            ->first();

        $last_date_whrpg = ItemInspection::where('nm_area', 'WHRPG')
            ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
            ->where('status', 'Approve')
            ->orderBy('t_item_inspection.create_date', 'desc')
            ->first();

        $last_date_supporting = ItemInspection::where('nm_area', 'SUPPORTING')
            ->join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
            ->where('status', 'Approve')
            ->orderBy('t_item_inspection.create_date', 'desc')
            ->first();

        $data_ramwill = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve' and nm_area =  'RAWMILL')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE t_plant_inspection.status = 'Approve'
                        " . $filter_plant . " " . $filter_opco . "  and nm_area =  'RAWMILL' " . $filter_tahun . " " . $filter_bulan . " 
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area", $arr_bind);
 
       
        $data_kiln = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve' and nm_area =  'KILN')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE t_plant_inspection.status = 'Approve'
                        " . $filter_plant . " " . $filter_opco . " and nm_area =  'KILN' " . $filter_tahun . " " . $filter_bulan . " 
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area", $arr_bind);
 

        $data_finish_mill = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve' and nm_area =  'FINISH MILL')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE t_plant_inspection.status = 'Approve'
                        " . $filter_plant . " " . $filter_opco . "  and nm_area =  'FINISH MILL' " . $filter_tahun . " " . $filter_bulan . " 
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area", $arr_bind);


        $data_packer = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve' and nm_area =  'PACKER')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE t_plant_inspection.status = 'Approve'
                        " . $filter_plant . "  " . $filter_opco . "  and nm_area =  'PACKER' " . $filter_tahun . " " . $filter_bulan . " 
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area", $arr_bind);


        $data_coal_mill = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve' and nm_area =  'COAL MILL')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE t_plant_inspection.status = 'Approve'
                        " . $filter_plant . "  " . $filter_opco . "  and nm_area =  'COAL MILL' " . $filter_tahun . " " . $filter_bulan . " 
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area", $arr_bind);
       
        $data_supporting = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve' and nm_area =  'SUPPORTING')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE t_plant_inspection.status = 'Approve'
                        " . $filter_plant . "  " . $filter_opco . "  and nm_area =  'SUPPORTING' " . $filter_tahun . " " . $filter_bulan . " 
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area", $arr_bind);

        $data_whrpg = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve' and nm_area =  'WHRPG')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE t_plant_inspection.status = 'Approve'
                        " . $filter_plant . "  " . $filter_opco . "  and nm_area =  'WHRPG' " . $filter_tahun . " " . $filter_bulan . " 
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area", $arr_bind);

        return [
            'message' => 'Succes Get Data',
            'data_ramwill' => $data_ramwill,
            'data_crusher' => $data_crusher,
            'data_kiln' => $data_kiln,
            'data_finish_mill' => $data_finish_mill,
            'data_packer' => $data_packer,
            'data_supporting' => $data_supporting,
            'data_coal_mill' => $data_coal_mill,
            'data_whrpg' => $data_whrpg,
            'last_date_crusher' => $last_date_crusher,
            'last_date_rawmill' => $last_date_rawmill,
            'last_date_coalmill' => $last_date_coalmill,
            'last_date_kiln' => $last_date_kiln,
            'last_date_cementmill' => $last_date_cementmill,
            'last_date_packer' => $last_date_packer,
            'last_date_whrpg' => $last_date_whrpg,
            'last_date_supporting' => $last_date_supporting
        ];
    }

    public function getGraphEquipHealth(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
            'bulan' => 'nullable|numeric',
            'tahun' => 'nullable|numeric',
        ]);
        $data_count = ItemInspection::where('no_inspection', $request->no_inspection)->count();

        $arr_bind = [];
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $val_opco = $request->opco;
            $filter_opco = "AND t_plant_inspection.kode_opco = ?";
            $arr_bind[] = $val_opco;
        }

        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $val_plant = $request->plant;
            $filter_plant = "AND t_plant_inspection.kode_plant = ?";
            $arr_bind[] = $val_plant;
        }

        if ($request->bulan == "empty" || $request->bulan == "") {
            $filter_bulan = '';
        } else {
            $val_bulan = $request->bulan;
            $filter_bulan = "AND DATE_PART('month', t_item_inspection.create_date::date) = ?";
            $arr_bind[] = $val_bulan;
        }

        if ($request->tahun == "empty" || $request->tahun == "") {
            $filter_tahun = '';
        } else {
            $val_tahun = $request->tahun;
            $filter_tahun = "AND DATE_PART('year', t_item_inspection.create_date::date) = ?";
            $arr_bind[] = $val_tahun;
        }

        // $data_risk = DB::select("select
        // COUNT(CASE WHEN m_kondisi.nm_kondisi = 'GOOD' THEN 1 END) AS GOOD,
        // COUNT(CASE WHEN m_kondisi.nm_kondisi = 'LOW RISK' THEN 1 END) AS LOW_RISK,
        // COUNT(CASE WHEN m_kondisi.nm_kondisi = 'MED RISK' THEN 1 END) AS MED_RISK,
        // COUNT(CASE WHEN m_kondisi.nm_kondisi = 'HIGH RISK' THEN 1 END) AS HIGH_RISK
        // FROM t_item_inspection
        // join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection
        // join m_kondisi on t_item_inspection.id_kondisi = m_kondisi.id_kondisi
        // WHERE   " . $filter_plant . "  and " . $filter_opco . "
        // ");

        $data_risk = DB::select("select nm_area,
        COUNT(CASE WHEN result = 1 THEN 1 END) AS GOOD,
        COUNT(CASE WHEN result = 2 THEN 1 END) AS LOW_RISK,
        COUNT(CASE WHEN result = 3 THEN 1 END) AS MED_RISK,
        COUNT(CASE WHEN result = 4 THEN 1 END) AS HIGH_RISK
        FROM (
            select nm_area, id_equipment, t_item_inspection.create_date, max(id_kondisi) kondisi_max_per_bulan
				, max(case when id_kondisi = 0 then (select max(tii.id_kondisi) from t_item_inspection tii 
								join t_plant_inspection tpi on tii.no_inspection = tpi.no_inspection
								where tii.nm_area = t_item_inspection.nm_area 
								and tii.id_equipment = t_item_inspection.id_equipment
								and tpi.status = 'Approve')
				  else id_kondisi
					end) result
            FROM t_item_inspection
            join t_plant_inspection on t_item_inspection.no_inspection = t_plant_inspection.no_inspection 
						WHERE t_plant_inspection.status = 'Approve'
                        " . $filter_plant . " " . $filter_opco . " " . $filter_tahun . "  " . $filter_bulan . "
						group by nm_area, id_equipment, t_item_inspection.create_date
            ORDER BY nm_area, create_date
        ) x
        GROUP BY nm_area", $arr_bind);

        return [
            'message' => 'Succes Get Data',
            'data_risk' => $data_risk,
            'data_count' => $data_count
        ];
    }

    public function getEquipmentHealth(Request $request)
    {
        
        $highrisk = ItemInspection::join('t_plant_inspection', 't_plant_inspection.no_inspection', '=', 't_item_inspection.no_inspection')
            ->where('status', 'Approve')
            ->where('id_kondisi', 4);

        if($request->bulan){
            $highrisk = $highrisk -> where(DB::raw("TO_CHAR(t_item_inspection.create_date::timestamp, 'MM')"), $request->bulan);
        }
            $highrisk = $highrisk ->orderBy('t_item_inspection.create_date', 'desc')->get();

        return [
            'message' => 'Succes Get Data',
            'data' => $highrisk
        ];
    }
}
