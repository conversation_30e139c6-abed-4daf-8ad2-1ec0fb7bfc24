<?php

namespace App\Http\Controllers;

use App\Models\Opco;
use DataTables;
use Illuminate\Http\Request;
use App\Models\Project;
use Exception;
use Illuminate\Support\Facades\Auth;

class ProjectController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Project',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'/project',
                ],
                [
                    'title'=>'Project',
                    'url'=>'',
                ]
            ],
        ];
        $data['opcos'] = Opco::get();
        return view('master.project', $data);
    }

    public function datatables(Request $request)
    {
        $query    = Project::get();

        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function store(Request $request)
    {
        $username = Auth::user()->username;
        $request->validate([
            'no_project' => 'required|max:30|unique:m_project,no_project,NULL,no_project,deleted_at,NULL',
            'kode_opco' => 'required|exists:m_opco,reference_sap',
            'description' => 'nullable|max:100'
        ]);

        try {
            $model = Project::withTrashed()->find($request->no_project);
            if ($model) {
                $model->update(array_merge($request->all(), ['update_by' => $username]));
                $model->restore();
            } else {
                $model = Project::create(array_merge($request->all(), ['create_by' => $username]));
            }

            $response = responseSuccess(trans('messages.create-success'), $model);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function show($company)
    {
        $query   = Project::findOrFail($company);
        $response = responseSuccess(trans('messages.read-success'), $query);
        return response()->json($response, 200);
    }

    public function edit($id)
    {
        $query   = Project::findOrFail($id);
        $response = responseSuccess(trans('messages.read-success'), $query);
        return response()->json($response, 200);
    }

    public function update($id, Request $request)
    {
        $model = Project::findOrFail($id);

        $request->validate([
            'no_project' => 'required|max:30|unique:m_project,no_project,' . $id . ',no_project',
            'kode_opco' => 'required|exists:m_opco,reference_sap',
            'description' => 'nullable|max:100'
        ]);

        try {
            $model->update($request->all());

            $response = responseSuccess(trans('messages.update-success'), $model);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.update-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }


    public function destroy($id)
    {
        $model = Project::findOrFail($id);

        try {
            $model->delete();
            $response = responseSuccess(trans('messages.delete-success'), $model);
            return response()->json($response, 200);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.delete-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }
}
