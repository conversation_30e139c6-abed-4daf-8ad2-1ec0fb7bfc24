<?php

namespace App\Http\Controllers;
use Exception;
use App\Exports\ExportKoreksiProduksi;
use App\Imports\ImportKoreksiProduksi;
use App\Models\KilnOutput;
use App\Models\KilnPlant;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Opco;
use App\Models\RealisasiProduksi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class KoreksiProduksiSatuController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    private $listPlant;

    public function __construct(){
        $Plant = KilnPlant::select('kode_plant')->orderBy('no_pbi')->get()->pluck('kode_plant');
        $this->listPlant = $Plant;
    }

    public function index()
    {
        $data = [
            'title' => 'Koreksi Data Produksi',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/data-koreksi-satu-produksi',
                ],
                [
                    'title'=>'Koreksi Data Produksi',
                    'url'=>'',
                ]
            ],
        ];
        return view('koreksiLevel1.koreksiProduksi', $data);
    }

    public function importKoreksiProduksi()
    {
        $data = [
            'title' => 'Koreksi Data Produksi',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/data-koreksi-satu-produksi',
                ],
                [
                    'title'=>'Koreksi Data Produksi',
                    'url'=>'/koreksi-data-produksi/import',
                ],
                [
                    'title'=>'Import Data',
                    'url'=>'',
                ]
            ],
        ];
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();
        $opt_opco = "";
        foreach ($opco as $value) {
          $kode_opco = $value['kode_opco'];
          $nama_opco = $value['nama_opco'];
          $opt_opco .= "<option value='$kode_opco'>$nama_opco</option>";
        }
        $data['opco'] = $opt_opco;

        $plant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
        $opt_plant = "";
        foreach ($plant as $value) {
          $kode_plant = $value['kode_plant'];
          $nama_plant = $value['name_plant'];
          $opt_plant .= "<option value='$kode_plant'>$nama_plant</option>";
        }
        $data['plant'] = $opt_plant;
        return view('koreksiLevel1.koreksiProduksiImport', $data);
    }

    public function datatables(Request $request)
    {
        $request->validate([
            'filter_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
        ]);

        $dataKilnStop = KilnOutput::select('t_kiln_output.kode_plant','tanggal','produksi_output')
        ->join('m_kiln_plant','t_kiln_output.kode_plant','=','m_kiln_plant.kode_plant')->orderBy('tanggal','DESC');

        if ($request->filter_opco) {
            $dataKilnStop = $dataKilnStop->where('m_kiln_plant.kode_opco', $request->filter_opco);
        }
        if ($request->filter_plant) {
            $dataKilnStop = $dataKilnStop->where('t_kiln_output.kode_plant', $request->filter_plant);
        }
        if($request->filter_tahun){
            $dataKilnStop = $dataKilnStop -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $dataKilnStop = $dataKilnStop -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $request->filter_bulan);
        }
        $dataKilnStop = $dataKilnStop->get();
        $data     = DataTables::of($dataKilnStop)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function exportTemplate(Request $request)
    {
        $request->validate([
            'filter_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
        'filter_tahun' => 'nullable|numeric',
        'filter_bulan' => 'nullable|numeric',
        ]);
        $data =  KilnOutput::select([
            't_kiln_output.kode_plant',
            'tanggal',
            'produksi_output'])
        ->join('m_kiln_plant','t_kiln_output.kode_plant','=','m_kiln_plant.kode_plant');
        if ($request->filter_plant) {
            $data = $data->where('t_kiln_output.kode_plant', $request->filter_plant);
        }
        if($request->filter_tahun){
            $data = $data -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $data = $data -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $request->filter_bulan);
        }
        $data = $data->orderby('tanggal')->get();

        return Excel::download(new ExportKoreksiProduksi($data), 'Template Koreksi Produksi.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
    }

    public function import(Request $request)
    {
        // validasi
        $this->validate($request, [
        'excel' => 'required|mimes:csv,xls,xlsx', //spekati satu file, validasi filename -> header -> coba cek dimeta data author
        'excel' => 'max:5000'
        ]);

        try{

        // menangkap file excel
        $file = $request->file('excel');

        // import data
        $import = new ImportKoreksiProduksi;
        Excel::import($import, $file);

        $datas = ($import->data);
        //mapping data from import excel
        // dd($datas);
        $newData = [];
        foreach ($datas as $value) {
            $temp = [];
            $temp['kode_plant'] = $value['kode_plant'];
            $temp['tanggal'] = $value['tanggal'];
            $temp['produksi_output'] = $value['produksi_output'] == null ? 0 : $value['produksi_output'];
            array_push($newData, $temp);
        }
        //validation
        $result = [];
        foreach ($newData as $value) {
            $format = $value;
            if(is_double($format['tanggal']) || is_float($format['tanggal']) || is_int($format['tanggal'])){
                $format['tanggal'] = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($format['tanggal'])->format('Y-m-d H:i:s');
                $format['tanggal'] = date_format(date_create($format['tanggal']),"Y-m-d H:i:s");
                $validator = Validator::make($value,[
                    'kode_plant' => ['required',Rule::in($this->listPlant)],
                    'tanggal' => ['required'],
                    'produksi_output' => ['required', 'between:0,999.99'],
                ]);
            }else{
            $validator = Validator::make($value,[
                'kode_plant' => ['required',Rule::in($this->listPlant)],
                'tanggal' => ['required', 'date_format:Y-m-d H:i:s'],
                'produksi_output' => ['required', 'between:0,999.99'],
            ]);
            }

            $createdate = date_format(date_create($format['tanggal']),"Y-m-d");
            $date_nows = date("Y-m-d");
            $format['is_valid'] = 'Valid';
            $format['note'] = array_values($validator->errors()->messages());
            $tempMessage = '';
            foreach ($format['note'] as $message) {
                $tempMessage .= $message[0];
            }
            $format['note'] = $tempMessage;
            $format['note'] == '' ? $format['is_valid'] = 'Valid' : $format['is_valid'] = 'Invalid';
            if ($createdate == $date_nows) {
                $format['is_valid'] = 'Invalid';
                $format['note'] = "Dates cannot be the same";
            }
            array_push($result, $format);
        }

        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);

        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function insertData(Request $request)
    {
        $excel = json_decode($request->excel);
        $listCol = ['kode_plant', 'tanggal', 'produksi_output'];
        $result = [];
        foreach ($excel as $data) {
            $oldData = $data;
            foreach ($listCol as $index => $col) {
                $data[$col] = $data[$index];
            }
            $data = array_diff_key($data, $oldData);
            $format = $data;
            $validator = Validator::make($data,[
                'kode_plant' => ['required',Rule::in($this->listPlant)],
                'tanggal' => ['required', 'date_format:Y-m-d H:i:s'],
                'produksi_output' => ['required', 'between:0,999.99'],
            ]);
            $createdate = date_format(date_create($format['tanggal']),"Y-m-d");
            $date_nows = date("Y-m-d");
            $format['is_valid'] = 'Valid';
            $format['note'] = array_values($validator->errors()->messages());
            $tempMessage = '';
            foreach ($format['note'] as $message) {
                $tempMessage .= $message[0];
            }
            $format['note'] = $tempMessage;
            $format['note'] == '' ? $format['is_valid'] = 'Valid' : $format['is_valid'] = 'Invalid';
            if ($createdate == $date_nows) {
                $format['is_valid'] = 'Invalid';
                $format['note'] = "Dates cannot be the same";
            }
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function saveDataKoreksi(Request $request)
    {
        $excel = json_decode($request->excel);
        foreach ($excel as $key => $value) {
            $plant = KilnPlant::select('kode_opco')->where('kode_plant',$excel[$key][0])->first();
            $excel[$key][5] = $plant->kode_opco;
            if ($plant->kode_opco == 'GHOPO') {
                $excel[$key][6] = "OPC";
                $excel[$key][7] = "OPC_".$excel[$key][0];
            }elseif ($plant->kode_opco == 'SG') {
                $excel[$key][6] = "OPC";
                $excel[$key][7] = "OPC_".$excel[$key][0];
            }elseif ($plant->kode_opco == 'SP') {
                $excel[$key][6] = "OPC";
                $excel[$key][7] = "OPC_".$excel[$key][0];
            }elseif ($plant->kode_opco == 'ST') {
                $excel[$key][6] = "OPC";
                $excel[$key][7] = "OPC_".$excel[$key][0];
            }else{
                $excel[$key][6] = "-";
                $excel[$key][7] = "-";
            }

        }
        foreach ($excel as $key => $value) {

            DB::table('t_kiln_output')
            ->where('tanggal',$excel[$key][1])
            ->where('kode_plant',$excel[$key][0])
            ->delete();

            KilnOutput::insert([
                'kode_plant' => $excel[$key][0],
                'tanggal' => $excel[$key][1],
                'produksi_output' => $excel[$key][2],
                'source_system' => $excel[$key][6],
                'create_by' => $excel[$key][7],
                'kode_opco' => $excel[$key][5]
            ]);

        }
        //update data to table staging(ts_realisasi_performance)
        $dataDate = date('Y-m', strtotime($excel[0][1]));
        $kodePlant = $excel[0][0];
        $dataMonthRealPerform = DB::table('vw_month_real_perform')->where('tanggal',$dataDate)->where('kode_plant',$kodePlant)->first();
        $dataRealisasi = DB::table('ts_realisasi_performance')->where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY-MM')"), $dataDate)->where('kode_plant',$kodePlant)->update([
            'act_prod'  =>$dataMonthRealPerform->act_prod,
        ]);

        //update data to table staging(ts_realisasi_produksi)
        $dataDate = date('Y-m', strtotime($excel[0][1]));
        $kodePlant = $excel[0][0];
        $kodeOpco = DB::table('m_kiln_plant')->select('kode_opco')->where('kode_plant',$kodePlant)->first();
        $sumKilnOutput = KilnOutput::where('kode_opco', $kodeOpco->kode_opco)->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $dataDate)
        ->select('kode_opco', DB::raw('SUM(produksi_output) as prod_output'))
        ->groupBy('kode_opco')
        ->first();
        $dataRealisasi = RealisasiProduksi::where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY-MM')"), $dataDate)->where('kode_opco',$kodeOpco->kode_opco)->update([
            'prod_output'  =>$sumKilnOutput->prod_output,
        ]);

        $response = responseSuccess('Data added successfully');
        return response()->json($response, 200);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
