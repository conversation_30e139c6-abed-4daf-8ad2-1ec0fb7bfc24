<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRealCostTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('real_cost', function (Blueprint $table) {
            $table->bigIncrements('id_realisasi_cost');
            $table->string('kode_opco', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->integer('bahan_bakar');
            $table->integer('bahan_baku');
            $table->integer('listrik');
            $table->integer('tenaga_kerja');
            $table->integer('pemeliharaan');
            $table->integer('penyusutan');
            $table->integer('administrasi_umum');
            $table->integer('pajak_asuransi');
            $table->integer('elim_bb');
            $table->integer('elim_penyusutan');
            $table->integer('elim_administrasi');
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('real_cost');
    }
}
