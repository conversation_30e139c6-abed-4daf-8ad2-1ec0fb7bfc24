<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMEquipmentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_equipment', function (Blueprint $table) {
            $table->bigIncrements('id_equipment');
            $table->integer('id_area');
            $table->integer('id_kategori');
            $table->string('kode_equipment', 50);
            $table->string('nm_equipment', 50);
            $table->string('kode_opco', 50);
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('m_equipment');
    }
}
