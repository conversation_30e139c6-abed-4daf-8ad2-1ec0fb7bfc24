<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOpcoOpcConfigTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sync_opc_configs', function (Blueprint $table) {
            // Only add kode_opco if it doesn't exist
            if (!Schema::hasColumn('sync_opc_configs', 'kode_opco')) {
                $table->string('kode_opco', 30)->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sync_opc_configs', function (Blueprint $table) {
            if (Schema::hasColumn('sync_opc_configs', 'kode_opco')) {
                $table->dropColumn('kode_opco');
            }
        });
    }
}
