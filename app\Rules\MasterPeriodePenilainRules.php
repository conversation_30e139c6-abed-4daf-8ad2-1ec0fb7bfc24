<?php

namespace App\Rules;

use App\Models\DmmPeriodePenilaian;
use Illuminate\Contracts\Validation\Rule;

class MasterPeriodePenilainRules implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    protected $isUpdate = false;
    public function __construct($isUpdate = false)
    {
        //
        $this->isUpdate = $isUpdate;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        //
        $data = DmmPeriodePenilaian::where('tahun', $value['tahun'])
            ->where('periode', $value['periode'])
            ->where('start_date', '<=', $value['start_date'])
            ->where('end_date', '>=', $value['start_date'])
            ->where('status', 'y');
            if($this->isUpdate){
                $data->where('uuid','<>',$value['uuid']);
            }
            $data = $data->first();
        if ($data) {
            return false;
        }
        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Periode Penilaian Sudah Tersedia, Silahkan Pilih Periode Lain';
    }
}
