<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTDetailProduksi extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('t_detail_produksi', function (Blueprint $table) {
            $table->bigIncrements('id_detail_produksi');
            $table->string('no_dokument', 30);
            $table->string('kode_plant', 30);
            $table->string('kode_material', 30)->nullable();
            $table->date('tanggal')->nullable()->default(NULL);
            $table->float('produksi', 8, 5);
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->softDeletes();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('t_detail_produksi');
    }
}
