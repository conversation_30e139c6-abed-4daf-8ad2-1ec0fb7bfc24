<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddHoldingToMOpco extends Migration
{
    public function up()
    {
        Schema::table('m_opco', function (Blueprint $table) {
            if (!Schema::hasColumn('m_opco', 'holding')) {
                $table->string('holding', 50)->nullable();
            }
        });
    }

    public function down()
    {
        Schema::table('m_opco', function (Blueprint $table) {
            if (Schema::hasColumn('m_opco', 'holding')) {
                $table->dropColumn('holding');
            }
        });
    }
}