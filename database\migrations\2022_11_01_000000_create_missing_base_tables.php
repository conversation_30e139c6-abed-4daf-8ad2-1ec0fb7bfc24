<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMissingBaseTables extends Migration
{
    public function up()
    {
        // Create t_realisasi_penjualan if missing
        if (!Schema::hasTable('t_realisasi_penjualan')) {
            Schema::create('t_realisasi_penjualan', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('kode_opco', 30)->nullable();
                $table->date('tanggal')->nullable();
                $table->decimal('volume_penjualan', 15, 2)->nullable();
                $table->decimal('harga_jual', 15, 2)->nullable();
                $table->decimal('total_penjualan', 15, 2)->nullable();
                $table->string('created_by', 30)->nullable();
                $table->string('updated_by', 30)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }

        // Create sync_opc_configs if missing
        if (!Schema::hasTable('sync_opc_configs')) {
            Schema::create('sync_opc_configs', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('config_name', 100)->nullable();
                $table->text('config_value')->nullable();
                $table->boolean('is_active')->default(true);
                $table->string('created_by', 30)->nullable();
                $table->string('updated_by', 30)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }

        // Create m_plant_ics if missing
        if (!Schema::hasTable('m_plant_ics')) {
            Schema::create('m_plant_ics', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('kode_plant', 30)->nullable();
                $table->string('name_plant', 100)->nullable();
                $table->string('created_by', 30)->nullable();
                $table->string('updated_by', 30)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }

        // Create ts_realisasi_performance_daily if missing
        if (!Schema::hasTable('ts_realisasi_performance_daily')) {
            Schema::create('ts_realisasi_performance_daily', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('kode_plant', 30);
                $table->date('tanggal')->nullable();
                $table->float('oph', 8, 5);
                $table->float('updt', 8, 5);
                $table->float('pdt', 8, 5);
                $table->float('stop_idle', 8, 5);
                $table->float('fy_stop', 8, 5);
                $table->float('frek_updt', 8, 5);
                $table->string('problem', 200);
                $table->string('source_system', 30)->nullable();
                $table->string('create_by', 30)->nullable();
                $table->string('update_by', 30)->nullable();
                $table->timestamps();
                $table->softDeletes();
                $table->float('cal', 52, 0)->nullable();
                $table->float('net_avail', 52, 0)->nullable();
                $table->float('koreksi', 52, 0)->nullable();
                $table->float('act_prod', 52, 0)->nullable();
                $table->float('act_idle_prod', 52, 0)->nullable();
                $table->string('kode_opco', 30)->nullable();
                $table->float('rate_gross', 52, 0)->nullable();
                $table->float('rate_netto', 52, 0)->nullable();
            });
        }

        // Add other commonly missing tables
        $this->createOtherMissingTables();
    }

    private function createOtherMissingTables()
    {
        // Create users table if missing (for SSO login migration)
        if (!Schema::hasTable('users')) {
            Schema::create('users', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('name');
                $table->string('email')->unique();
                $table->timestamp('email_verified_at')->nullable();
                $table->string('password');
                $table->boolean('is_sso_login')->default(false);
                $table->rememberToken();
                $table->timestamps();
            });
        }

        // Create other base tables as needed
        if (!Schema::hasTable('m_opco')) {
            Schema::create('m_opco', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->integer('no_pbi')->nullable();
                $table->string('holding', 50)->nullable();
                $table->string('kode_opco', 30)->unique();
                $table->string('nama_opco', 100)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }

        if (!Schema::hasTable('rkap_capex')) {
            Schema::create('rkap_capex', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('kode_opco', 30);
                $table->integer('tahun');
                $table->integer('bulan');
                $table->decimal('rkap_capex', 15, 2)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }

        if (!Schema::hasTable('ts_realisasi_capex')) {
            Schema::create('ts_realisasi_capex', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('kode_opco', 30);
                $table->date('tanggal');
                $table->float('real_capex', 52, 0)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('ts_realisasi_capex');
        Schema::dropIfExists('rkap_capex');
        Schema::dropIfExists('m_opco');
        Schema::dropIfExists('ts_realisasi_performance_daily');
        Schema::dropIfExists('m_plant_ics');
        Schema::dropIfExists('sync_opc_configs');
        Schema::dropIfExists('t_realisasi_penjualan');
    }
}