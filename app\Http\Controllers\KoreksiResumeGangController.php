<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use Illuminate\Http\Request;
use App\Models\RealPerformance;
use App\Models\KilnPlant;
use App\Models\Opco;
use App\Models\KoreksiCapex;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportKoreksiResume;
use App\Exports\ExportDataKoreksiResumeGang;
use App\Imports\ImportKoreksiResume;

class KoreksiResumeGangController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = [
            'title' => 'Koreksi Resume Gang',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/koreksi-resumegang',
                ],
                [
                    'title'=>'Koreksi Resume Gang',
                    'url'=>'',
                ]
            ],
        ];
        return view('koreksiResumeGang', $data);
    }

    public function saveDataKoreksi(Request $request)
    {
        $excel = json_decode($request->excel);
        foreach ($excel as $item) {
            $time = strtotime($item[1]);
            $newformat = date('Y-m-d', $time);

            $cek_similiarity = DB::table('tk_resume')
            ->where(DB::raw("date(tanggal)"), $newformat)
            ->where('kode_plant', $item[0])
            ->count();
    
            if ($cek_similiarity != 0) {
                DB::table('tk_resume')
                ->where(DB::raw("date(tanggal)"), $newformat)
                ->where('kode_plant', $item[0])
                ->delete();
            } 

            $plant = DB::table('m_kiln_plant')->select(
                'kode_opco'
            );
         
            $plant = $plant->where('kode_plant', $item[0]);
            $plant = $plant->orderBy('no_pbi', 'ASC')->first();

        

            DB::table('tk_resume')->insert([

                'kode_opco' => $plant->kode_opco,
                'kode_plant' => $item[0],
                'tanggal' => $item[1],
                'problem' => $item[2],
                'oph' => $item[3],
                'updt' => $item[4],
                'pdt' => $item[5],
                'stop_idle' => $item[6],
                'frek_updt' => $item[7]
            ]);
        }
        $response = responseSuccess('Data added successfully');
        return response()->json($response, 200);
    }

    public function insertData(Request $request)
    {
        $excel = json_decode($request->excel);
        $result = [];
        $success = true;
        //list opco
        $plant = KilnPlant::select('kode_plant')->get()->toArray();
        $arrPlant = [];
        foreach ($plant as $value) {
            array_push($arrPlant, $value['kode_plant']);
        }

        foreach ($excel as $data) {
            $oldData = $data;
            $data['kode_plant'] = $data[0];
            $data['tanggal'] = $data[1];
            $data['problem'] = $data[2];
            $data['oph'] = $data[3];
            $data['updt'] = $data[4];
            $data['pdt'] = $data[5];
            $data['stop_idle'] = $data[6];
            $data['frek_updt'] = $data[7];
            $data['is_valid'] = $data[8];
            $data = array_diff_key($data, $oldData);
            $format = $data;
            $message = "";
            $status = "Valid";
            
            if ($data['kode_plant'] == NULL) {
                $status = 'Invalid';
                $message = $message . "Kolom Plant tidak boleh kosong ";
            } else if (gettype($data['kode_plant']) != 'string') {
                $status = 'Invalid';
                $message = $message . "Kolom Plant hanya berisi huruf ";
            } else if (!in_array($data['kode_plant'], $arrPlant)) {
                $status = "Invalid";
                $message = $message . "Kolom Plant tidak ditemukan";
            }


            if ($data['tanggal'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom Tanggal tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom Tanggal tidak boleh kosong";
                }
            }

            if ($data['problem'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom problem tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom problem tidak boleh kosong";
                }
            }

            if ($data['oph'] == NULL) {
                $format['oph'] = 0;
            } else if (gettype($data['oph']) != 'integer' and gettype($data['oph']) != 'float'  and gettype($data['oph']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom oph  hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom oph  hanya berisi angka";
                }
            }

            
            if ($data['updt'] == NULL) {
                $format['updt'] = 0;
            } else if (gettype($data['updt']) != 'integer' and gettype($data['updt']) != 'float'  and gettype($data['updt']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom updt  hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom updt  hanya berisi angka";
                }
            }

            if ($data['pdt'] == NULL) {
                $format['pdt'] = 0;
            } else if (gettype($data['pdt']) != 'integer' and gettype($data['pdt']) != 'float'  and gettype($data['pdt']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom pdt  hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom pdt  hanya berisi angka";
                }
            }

            if ($data['stop_idle'] == NULL) {
                $format['stop_idle'] = 0;
            } else if (gettype($data['stop_idle']) != 'integer' and gettype($data['stop_idle']) != 'float'  and gettype($data['stop_idle']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom stop idle  hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom stop idle  hanya berisi angka";
                }
            }

            if ($data['frek_updt'] == NULL) {
                $format['frek_updt'] = 0;
            } else if (gettype($data['frek_updt']) != 'integer' and gettype($data['frek_updt']) != 'float' and gettype($data['frek_updt']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom frek updt  hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom frek updt  hanya berisi angka";
                }
            }
       
            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function importKoreksiResume()
    {
        $data = [
            'title' => 'Import Koreksi Resume Gang',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/koreksi-resumegang',
                ],
                [
                    'title'=>'Koreksi Data Resume Gang',
                    'url'=>'/import-koreksi-resume-gang',
                ],
                [
                    'title'=>'Import Koreksi Resume Gang',
                    'url'=>'',
                ]
            ],
        ];
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();
        $opt_opco = "";
        foreach ($opco as $value) {
            $kode_opco = $value['kode_opco'];
            $nama_opco = $value['nama_opco'];
            $opt_opco .= "<option value='$kode_opco'>$nama_opco</option>";
        }
        $data['opco'] = $opt_opco;

        $plant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
        $opt_plant = "";
        foreach ($plant as $value) {
            $kode_plant = $value['kode_plant'];
            $nama_plant = $value['name_plant'];
            $opt_plant .= "<option value='$kode_plant'>$nama_plant</option>";
        }
        $data['plant'] = $opt_plant;
        return view('koreksiResumeGangImport', $data);
    }

    public function exportTemplate(Request $request)
    {
        $request->validate([
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
        ]);

        $arr_bind = [];
        if ($request->filter_bulan == "empty" || $request->filter_bulan == "") {
            $filter_bulan = '';
        } else {
            $val_bulan = $request->filter_bulan;
            $filter_bulan = "AND TO_CHAR(tanggal::timestamp, 'MM') = ?";
            $arr_bind[] = $val_bulan;
        }
        if ($request->filter_tahun == "empty" || $request->filter_tahun == "") {
            $filter_tahun = '';
        } else {
            $val_tahun = $request->filter_tahun;
            $filter_tahun = "AND TO_CHAR(tanggal::timestamp, 'YYYY') = ?";
            $arr_bind[] = $val_tahun;
        }

            $data_koreksi =  DB::select("select
                plant as kode_plant,
                TO_CHAR(tanggal::timestamp, 'DD/MM/YYYY') AS tanggal,
                problem,
                oph,
                updt,
                pdt,
                stop_idle,
                frek as frek_updt
                from vw_tk_resume
                WHERE 1=1 ".$filter_bulan." ".$filter_tahun.""
                , $arr_bind);

        return Excel::download(new ExportKoreksiResume($data_koreksi), 'Template Koreksi Resume.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
    }

    public function import(Request $request)
    {
        // validasi
        $this->validate($request, [
            'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author 
        ]);

        // menangkap file excel
        $file = $request->file('excel');

        // import data
        $import = new ImportKoreksiResume;
        Excel::import($import, $file);
        //list opco
        $plant = KilnPlant::select('kode_plant')->get()->toArray();
        $arrPlant = [];
        foreach ($plant as $value) {
            array_push($arrPlant, $value['kode_plant']);
        }

        
        $datas = ($import->data);
        $result = [];
        foreach ($datas as $data) {
            $format = $data;
            $message = "";
            $status = "Valid";


            if ($data['kode_plant'] == NULL) {
                $status = 'Invalid';
                $message = $message . "Kolom Plant tidak boleh kosong ";
            } else if (gettype($data['kode_plant']) != 'string') {
                $status = 'Invalid';
                $message = $message . "Kolom Plant hanya berisi huruf ";
            } else if (!in_array($data['kode_plant'], $arrPlant)) {
                $status = "Invalid";
                $message = $message . "Kolom Plant tidak ditemukan";
            }


            if ($data['tanggal'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom Tanggal tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom Tanggal tidak boleh kosong";
                }
            }

            if ($data['problem'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom problem tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom problem tidak boleh kosong";
                }
            }

            if ($data['oph'] == NULL) {
                $format['oph'] = 0;
            } else if (gettype($data['oph']) != 'integer' and gettype($data['oph']) != 'float'  and gettype($data['oph']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom oph  hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom oph  hanya berisi angka";
                }
            }

            
            if ($data['updt'] == NULL) {
                $format['updt'] = 0;
            } else if (gettype($data['updt']) != 'integer' and gettype($data['updt']) != 'float'  and gettype($data['updt']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom updt  hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom updt  hanya berisi angka";
                }
            }

            if ($data['pdt'] == NULL) {
                $format['pdt'] = 0;
            } else if (gettype($data['pdt']) != 'integer' and gettype($data['pdt']) != 'float'  and gettype($data['pdt']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom pdt  hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom pdt  hanya berisi angka";
                }
            }

            if ($data['stop_idle'] == NULL) {
                $format['stop_idle'] = 0;
            } else if (gettype($data['stop_idle']) != 'integer' and gettype($data['stop_idle']) != 'float'  and gettype($data['stop_idle']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom stop idle  hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom stop idle  hanya berisi angka";
                }
            }

            if ($data['frek_updt'] == NULL) {
                $format['frek_updt'] = 0;
            } else if (gettype($data['frek_updt']) != 'integer' and gettype($data['frek_updt']) != 'float'  and gettype($data['frek_updt']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom frek updt  hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom frek updt  hanya berisi angka";
                }
            }

            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function getDataResumeGang(Request $request)
    {
        try {
            $request->validate([
            'filter_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
        ]);

        $arr_bind = [];
        if ($request->filter_opco == "empty" || $request->filter_opco == "") {
            $filter_opco = '';
            } else {
                $val_opco = $request->filter_opco;
                $filter_opco = "AND opco = ?";
                $arr_bind[] = $val_opco;
            }

        if ($request->filter_plant == "empty" || $request->filter_plant == "") {
            $filter_plant = '';
            } else {
                $val_plant = $request->filter_plant;
                $filter_plant = "AND plant = ?";
                $arr_bind[] = $val_plant;
            }

        if ($request->filter_bulan == "empty" || $request->filter_bulan == "") {
            $filter_bulan = '';
            } else {
                $val_bulan = $request->filter_bulan;
                $filter_bulan = "AND TO_CHAR(tanggal::timestamp, 'MM') = ?";
                $arr_bind[] = $val_bulan;
            }

        if ($request->filter_tahun == "empty" || $request->filter_tahun == "") {
            $filter_tahun = '';
            } else {
                $val_tahun = $request->filter_tahun;
                $filter_tahun = "AND TO_CHAR(tanggal::timestamp, 'YYYY') = ?";
                $arr_bind[] = $val_tahun;
            }

            $data_resume = DB::select("select
                opco as kode_opco,
                plant as kode_plant,
                tanggal,
                problem,
                to_char(oph, '999G999G999G999G990D9') as oph,
                to_char(updt, '999G999G999G999G990D9') as updt,
                to_char(pdt, '999G999G999G999G990D9') as pdt,
                to_char(stop_idle, '999G999G999G999G990D9') as stop_idle,
                to_char(frek, '999G999G999G999G990D9') as frek_updt
            from vw_tk_resume
            WHERE 1=1 " . $filter_opco . "  " . $filter_plant . " ".$filter_bulan." ".$filter_tahun.""
            , $arr_bind);

            return DataTables::of($data_resume)->make();
            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data' => $data_resume,
            ];
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }

    public function export(Request $request)
    {

        $request->validate([
            'filter_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
        ]);

        $arr_bind = [];
        if ($request->filter_opco == "empty" || $request->filter_opco == "") {
            $filter_opco = '';
      } else {
                $val_opco = $request->filter_opco;
                $filter_opco = "AND opco = ?";
                $arr_bind[] = $val_opco;
      }

        if ($request->filter_plant == "empty" || $request->filter_plant == "") {
            $filter_plant = '';
      } else {
                $val_plant = $request->filter_plant;
                $filter_plant = "AND plant = ?";
                $arr_bind[] = $val_plant;
      }

        if ($request->filter_bulan == "empty" || $request->filter_bulan == "") {
            $filter_bulan = '';
      } else {
                $val_bulan = $request->filter_bulan;
                $filter_bulan = "AND TO_CHAR(tanggal::timestamp, 'MM') = ?";
                $arr_bind[] = $val_bulan;
      }

        if ($request->filter_tahun == "empty" || $request->filter_tahun == "") {
            $filter_tahun = '';
      } else {
                $val_tahun = $request->filter_tahun;
                $filter_tahun = "AND TO_CHAR(tanggal::timestamp, 'YYYY') = ?";
                $arr_bind[] = $val_tahun;
      }

    $data_resume = DB::select("select
                plant as kode_plant,
                TO_CHAR(tanggal::timestamp, 'DD/MM/YYYY') AS tanggal,
                problem,
                oph,
                updt,
                pdt,
                stop_idle,
                frek as frek_updt
            from vw_tk_resume
            WHERE 1=1 " . $filter_opco . "  " . $filter_plant . " ".$filter_bulan." ".$filter_tahun.""
            , $arr_bind);

      return Excel::download(new ExportDataKoreksiResumeGang($data_resume), 'Export Data Koreksi Resume Gang.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
    }
}
