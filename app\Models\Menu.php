<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Menu extends Model
{
    //
    protected $table    = 'menu';
    protected $fillable = ['id', 'uuid', 'name', 'url', 'order_no', 'permission' ,'iframe_url' ,'icon', 'parent_id', 'status', 'created_by', 'updated_by'];
    protected $primary_key = 'id';
    public $incrementing = false;
    protected $keyType = 'string';
    public function menuChilds()
    {
        return $this->hasMany("App\Models\Menu", "parent_id", "id");
    }

    public function permissionMenu()
    {
        return $this->hasMany("App\Models\Permission","menu_id","id")->orderby('id', 'asc');
    }

}
