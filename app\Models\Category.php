<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    const SPACER = '&nbsp; &nbsp; &nbsp; &nbsp;';

    protected $table = 'm_category';   // nama tabel di DB
    protected $fillable = [
        'name',
        'parent_id',
    ];

    public static function getTree()
    {
        $rows = static::where('parent_id', null)->orderBy('name')->get();
        $result = [];
        foreach ($rows as $row) {
            $result[] = ['id' => $row->id, 'name' => $row->name];
            self::treeRecursive($row->id, self::SPACER, $result);
        }
        return $result;
    }

    protected static function treeRecursive($parent_id, $prefix, &$result)
    {
        $rows = static::where('parent_id', $parent_id)->orderBy('name')->get();
        foreach ($rows as $row) {
            $result[] = ['id' => $row->id, 'name' => $prefix . $row->name];
            self::treeRecursive($row->id, $prefix . self::SPACER, $result);
        }
    }
}
