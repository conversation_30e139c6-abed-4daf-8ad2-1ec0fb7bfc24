<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\DetailKriteriaPenilaian;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class DetailKriteriaPenilaianController extends Controller
{
    //
   //
    public function index()
    {
        $data = [
            'title' => 'Detail Kriteria Penilaian',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'#',
                ],
                [
                    'title'=>'Detail Kriteria Penilaian',
                    'url'=>'kriteria-penilaian',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        return view('master.kriteria-penilaian', $data);
    }
    public function datatables(Request $request)
    {
        $query = DetailKriteriaPenilaian::list();

        $columns = [
            'dmm_detail_kriteria_penilaian.kode'=>'kode',
            'dmm_detail_kriteria_penilaian.deskripsi'=>'deskripsi',
            'dmm_detail_kriteria_penilaian.bobot'=>'bobot',
            'dmm_detail_kriteria_penilaian.status'=>'status'
        ];
        
        $data  = DataTables::of($query)
            ->filter(function ($query) use ($request, $columns) {
                $this->filterColumn($columns, $request, $query);
            })
            ->order(function ($query) use ($request, $columns) {
                $this->orderColumn($columns, $request, $query);
            })
            ->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }
    public function show($uuid)
    {
        $attributes['uuid'] = $uuid;

        $roles = [  
            'uuid' => 'required|exists:dmm_detail_kriteria_penilaian',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);
        $data     = $this->findDataWhere(DetailKriteriaPenilaian::class, ['uuid' => $uuid]);
        $response = responseSuccess(trans("messages.read-success"), $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }
    public function store(Request $request)
    {
        $attributes =  $request->only(['code','deskripsi','bobot','jenis','status']);
        $roles = [
            'code' => 'required|unique:dmm_detail_kriteria_penilaian',
            'deskripsi' => 'required',
            'bobot' => 'required|max:100|min:0',
            'jenis' => 'required|in:yn,abcd',
            'status' => 'required|in:y,n',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'date'   => trans('messages.date'),
        ];
        $this->validators($attributes, $roles, $messages);

        $attributes['created_by'] = Auth()->user()->id;

        DB::beginTransaction();
        try {
            //code...
            $data = DetailKriteriaPenilaian::create($attributes);
            DB::commit();
            $response = responseSuccess(trans("messages.create-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.create-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
    public function update($uuid,Request $request)
    {
        $attributes =  $request->only(['code','deskripsi','bobot','jenis','status']);
        $roles = [
            'code' => 'required|unique:dmm_detail_kriteria_penilaian,code,'.$uuid.',uuid',
            'deskripsi' => 'required',
            'bobot' => 'required|max:100|min:0',
            'jenis' => 'required|in:yn,abcd',
            'status' => 'required|in:y,n',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'date'   => trans('messages.date'),
        ];
        $this->validators($attributes, $roles, $messages);

        $attributes['updated_by'] = Auth()->user()->id;
        $data = $this->findDataWhere(DetailKriteriaPenilaian::class, ['uuid' => $uuid]);
        DB::beginTransaction();
        try {
            //code...
            $data->update($attributes);
            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
    public function destroy($uuid)
    {
        $attributes['uuid'] = $uuid;
        $roles = [  
            'uuid' => 'required|exists:dmm_detail_kriteria_penilaian',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);
        DB::beginTransaction();
        try {
            //code...
            DetailKriteriaPenilaian::where('uuid', $uuid)->delete();
            DB::commit();
            $response = responseSuccess(trans("messages.delete-success"), []);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.delete-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
}
