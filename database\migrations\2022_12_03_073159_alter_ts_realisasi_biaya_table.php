<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTsRealisasiBiayaTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //Delete View relate first
        // create view again
        Artisan::call('migrate:rollback', [
            '--path' => 'database/migrations/2022_11_30_072630_create_vw_fr_all.php',
        ]);
        
        Schema::table('ts_realisasi_biaya', function (Blueprint $table) {
            $table->float('bahan_bakar',8,5)->change();
            $table->float('bahan_baku',8,5)->change();
            $table->float('listrik',8,5)->change();
            $table->float('tenaga_kerja',8,5)->change();
            $table->float('pemeliharaan',8,5)->change();
            $table->float('penyusutan',8,5)->change();
            $table->float('administrasi_umum',8,5)->change();
            $table->float('pajak_asuransi',8,5)->change();
            $table->float('elim_bb',8,5)->change();
            $table->float('elim_penyusutan',8,5)->change();
            $table->float('elim_administrasi',8,5)->change();

        });

        // create view again
        Artisan::call('migrate:refresh', [
        '--path' => 'database/migrations/2022_11_30_072630_create_vw_fr_all.php',
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
