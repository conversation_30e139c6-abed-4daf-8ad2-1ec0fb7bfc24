<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use App\Models\RealisasiCapex;
use App\Models\Opco;
use Illuminate\Http\Request;
use App\Exports\RealCapexExport;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class RealCapexController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Realisasi Capex',
            'breadcrumb' => [
                [
                    'title'=>'Data Realisasi',
                    'url'=>'/real-capex',
                ],
                [
                    'title'=>'Realisasi Capex',
                    'url'=>'',
                ]
            ],
        ];
        return view('realCapex', $data);
    }

    public function getDatatables(Request $request)
    {
        $realCapex = RealisasiCapex::select(['id_realisasi_capex','kode_opco',
        DB::raw("to_char(tanggal,'DD Month YYYY') as tanggal"),
        DB::raw("to_char(real_capex, '999G999G999G999G999') as new_real_capex")]);
        if($request->filter_opco){
            $realCapex = $realCapex -> where('kode_opco', $request->filter_opco);
        }
        if($request->filter_tahun){
            $realCapex = $realCapex -> where(DB::raw("extract(year from tanggal)"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $realCapex = $realCapex -> where(DB::raw("extract(month from tanggal)"), $request->filter_bulan);
        }
        if($request->filter_search){
            $filter = $request->filter_search;
            $realCapex = $realCapex -> where('kode_opco', 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(tanggal,'DD Month YYYY')"),'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("extract(year from tanggal)"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("extract(month from tanggal)"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(real_capex, '999G999G999G999G990D99')"), 'ilike','%'.$filter.'%');
        }
        $data     = DataTables::of($realCapex)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function getFilterRealCapex()
    {
        //filter opco
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();

        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow; $i++) { 
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];  
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $bulan = [];
        for ($i=0; $i < count($months); $i++) { 
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => true
                ];
            } 
            else{
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => false
                ];
            }       
        }

        $data = [
            'kode_opco' => $opco,
            'tahun'     => $tahun,
            'bulan'     => $bulan
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function export(Request $request)
    {
        // Expot data with Collection
        $data = RealisasiCapex::select(['kode_opco','tanggal','real_capex',]);
        if($request->filter_opco){
            $data = $data -> where('kode_opco', $request->filter_opco);
        }
        if($request->filter_tahun){
            $data = $data -> where(DB::raw("extract(year from tanggal)"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $data = $data -> where(DB::raw("extract(month from tanggal)"), $request->filter_bulan);
        }
        $data = $data->orderBy('id_realisasi_capex')->get();
        ob_end_clean();
        ob_start();
        return Excel::download(new RealCapexExport($data), 'Realisasi Capex.xlsx');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\RealCapexController  $realCapexController
     * @return \Illuminate\Http\Response
     */
    public function show(RealCapexController $realCapexController)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\RealCapexController  $realCapexController
     * @return \Illuminate\Http\Response
     */
    public function edit(RealCapexController $realCapexController)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\RealCapexController  $realCapexController
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, RealCapexController $realCapexController)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\RealCapexController  $realCapexController
     * @return \Illuminate\Http\Response
     */
    public function destroy(RealCapexController $realCapexController)
    {
        //
    }
}
