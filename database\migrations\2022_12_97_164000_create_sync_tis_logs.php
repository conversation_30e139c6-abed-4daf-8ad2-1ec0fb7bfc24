<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSyncTisLogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sync_tis_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('sync_tis_config_id');
            $table->integer('year');
            $table->integer('month');
            $table->enum('status', ['success', 'process', 'fail']);
            $table->string('url');
            $table->text('parameter');
            $table->string('note')->nullable();
            $table->string('config_name', 255)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sync_tis_logs');
    }
}
