<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePostingJurnalDetail extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('posting_jurnal_detail', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('posting_jurnal_id')->nullable();
            $table->string('posting_key')->nullable();
            $table->string('gl_account')->nullable();
            $table->date('gl_value_date')->nullable();
            $table->string('gl_costcenter')->nullable();
            $table->string('gl_fund')->nullable();
            $table->string('gl_funds_ctr')->nullable();
            $table->string('gl_cmmt_item_long')->nullable();
            $table->string('gl_alloc_nmbr')->nullable();
            $table->string('customer_code')->nullable();
            $table->string('vendor_code')->nullable();
            $table->string('pmnttrms')->nullable();
            $table->date('bline_date')->nullable();
            $table->string('pmnt_block')->nullable();
            $table->string('dsct_days1')->nullable();
            $table->string('dsct_days2')->nullable();
            $table->string('netterms')->nullable();
            $table->string('dsct_pct1')->nullable();
            $table->string('dsct_pct2')->nullable();
            $table->string('pymt_meth')->nullable();
            $table->string('pmtmthsupl')->nullable();
            $table->string('paymt_ref')->nullable();
            $table->string('bank_id')->nullable();
            $table->string('housebankacctid')->nullable();
            $table->string('sp_gl_ind')->nullable();
            $table->string('partner_bk')->nullable();
            $table->string('ref_key_1')->nullable();
            $table->string('ref_key_2')->nullable();
            $table->string('ref_key_3')->nullable();
            $table->string('amount')->nullable();
            $table->string('ket_jurnal')->nullable();
            $table->string('tambahan_1')->nullable();
            $table->string('tambahan_2')->nullable();
            $table->string('tambahan_3')->nullable();
            $table->integer('flag')->nullable();
            $table->string('contract2')->nullable();
            $table->string('taxcode')->nullable();
            $table->string('flowtype')->nullable();
            $table->string('assigment')->nullable();
            $table->string('fixed')->nullable();
            $table->string('discamount')->nullable();
            $table->string('invoice1')->nullable();
            $table->string('invoice2')->nullable();
            $table->string('invoice3')->nullable();
            $table->string('calculatetax')->nullable();
            $table->string('wbs')->nullable();
            $table->string('network')->nullable();
            $table->string('asset')->nullable();
            $table->string('order')->nullable();
            $table->string('profit_segment')->nullable();
            $table->string('reb')->nullable();
            $table->string('salesorder')->nullable();
            $table->string('qty')->nullable();
            $table->string('discbase')->nullable();
            $table->string('bus_area')->nullable();
            $table->integer('create_by')->nullable();
            $table->integer('update_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('posting_jurnal_detail');
    }
}
