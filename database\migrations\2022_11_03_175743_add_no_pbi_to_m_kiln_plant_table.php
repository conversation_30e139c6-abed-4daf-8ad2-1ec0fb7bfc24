<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNoPbiToMKilnPlantTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('m_kiln_plant', function (Blueprint $table) {
            $table->integer('no_pbi')->nullable()->after('reference_opc_output');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('m_kiln_plant', function (Blueprint $table) {
            $table->dropColumn('no_pbi');
        });
    }
}
