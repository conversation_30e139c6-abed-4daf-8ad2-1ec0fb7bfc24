<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMBulanTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_bulan', function (Blueprint $table) {
            $table->bigIncrements('kode_bulan');
            $table->string('bln_indo',30);
            $table->string('short_bln_indo',30);
            $table->string('bln_english',30);
            $table->string('short_bln_english',30);
            $table->timestamps();
        });

        DB::table('m_bulan')->insert([
            ['kode_bulan' => '1', 'bln_indo' => 'Januari', 'short_bln_indo' => 'Jan', 'bln_english' => 'January', 'short_bln_english' => 'Jan'],
            ['kode_bulan' => '2', 'bln_indo' => 'Februari', 'short_bln_indo' => 'Feb', 'bln_english' => 'February', 'short_bln_english' => 'Feb'],
            ['kode_bulan' => '3', 'bln_indo' => 'Maret', 'short_bln_indo' => 'Mar', 'bln_english' => 'March', 'short_bln_english' => 'Mar'],
            ['kode_bulan' => '4', 'bln_indo' => 'April', 'short_bln_indo' => 'Apr', 'bln_english' => 'April', 'short_bln_english' => 'Apr'],
            ['kode_bulan' => '5', 'bln_indo' => 'Mei', 'short_bln_indo' => 'Mei', 'bln_english' => 'May', 'short_bln_english' => 'May'],
            ['kode_bulan' => '6', 'bln_indo' => 'Juni', 'short_bln_indo' => 'Jun', 'bln_english' => 'June', 'short_bln_english' => 'Jun'],
            ['kode_bulan' => '7', 'bln_indo' => 'Juli', 'short_bln_indo' => 'Jul', 'bln_english' => 'July', 'short_bln_english' => 'Jul'],            
            ['kode_bulan' => '8', 'bln_indo' => 'Agustus', 'short_bln_indo' => 'Agu', 'bln_english' => 'August', 'short_bln_english' => 'Aug'],
            ['kode_bulan' => '9', 'bln_indo' => 'September', 'short_bln_indo' => 'Sep', 'bln_english' => 'September', 'short_bln_english' => 'Sep'],
            ['kode_bulan' => '10', 'bln_indo' => 'Oktober', 'short_bln_indo' => 'Okt', 'bln_english' => 'October', 'short_bln_english' => 'Oct'],
            ['kode_bulan' => '11', 'bln_indo' => 'November', 'short_bln_indo' => 'Nov', 'bln_english' => 'November', 'short_bln_english' => 'Nov'],
            ['kode_bulan' => '12', 'bln_indo' => 'Desember', 'short_bln_indo' => 'Des', 'bln_english' => 'December', 'short_bln_english' => 'Dec'],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('m_bulan');
    }
}
