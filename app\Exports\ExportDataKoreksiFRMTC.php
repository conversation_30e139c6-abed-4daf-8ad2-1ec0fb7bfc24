<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Illuminate\Support\Arr;

class ExportDataKoreksiFRMTC implements WithMultipleSheets
{
  use Exportable;

  public function __construct($data, $tahun)
  {
    $this->data = $data;
    $this->tahun = $tahun;
  }

  /**
   * @return array
   */
  public function sheets(): array
  {
    $sheets = [];
    $kategori = ['COST','RUPIAH/TON','USD/TON'];

    $sheet_name = ['SIG'];

    foreach ($sheet_name as $key => $name) {
      foreach ($kategori as $key => $kat) {
        if ($name == 'SIG') {
          $dt[$kat]['SIG'] = $this->data[$kat]['SIG'];
        }
      }
      $sheets[] = new ExportDataKoreksiFRMTCCost($dt, $name, $this->tahun);
    }
    return $sheets;
  }
}
