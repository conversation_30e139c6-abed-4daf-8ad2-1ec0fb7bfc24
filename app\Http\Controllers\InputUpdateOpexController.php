<?php

namespace App\Http\Controllers;

use App\Http\Requests\InputUpdateOpexRequest;
use App\Models\InputUpdateOpex;
use App\Models\CostcenterStructure;
use App\Models\Material;
use App\Models\KoorBudget;
use App\Models\Gl_account;
use App\Models\Company;

use App\Models\Routes;

use DataTables;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use PDF;

class InputUpdateOpexController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //menu
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();

        //filter
        $data['company'] = Company::select('id', 'company', 'description')->get();

        //input
        $data['costcenter'] = CostcenterStructure::select('id', 'directorat')->get();
        $data['material'] = Material::select('id', 'kodematerial', 'name')->get();
        $data['coorbudget'] = KoorBudget::select('id', 'koor_budget', 'description')->get();
        $data['glaccount'] = Gl_account::select('id', 'gl_account', 'description')->get();


        return view('inputupdateopex', $data);
    }

    public function datatables(Request $request)
    {
        $query    = InputUpdateOpex::get();
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $operasionalcapex = OperasionalCapex::create([
            'costcenter' => $request->costcenter,
            'material' => $request->material,
            'description' => $request->description,
            'coorbudget' => $request->coorbudget,
            'glaccount' => $request->glaccount,
            'fiscal_year_period' => $request->fiscal_year_period,
            'quantity' => $request->quantity,
            'value' => $request->value,
        ]);

        $response = responseSuccess(trans('message.read-success'),$inputupdateopex);
        return response()->json($response,200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($inputupdateopex)
    {
        $query['inputupdateopex']   = InputUpdateOpex::find($inputupdateopex);
        
        $response = responseSuccess(trans('message.read-success'),$query);
        return response()->json($response,200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $query   = InputUpdateOpex::find($id);
        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(InputUpdateOpexRequest $request, $id)
    {
        $data = $this->findDataWhere(InputUpdateOpex::class, ['id' => $id]);
        
        DB::beginTransaction();
        try {
            $data->update([
                'costcenter' => $request->costcenter,
                'material' => $request->material,
                'description' => $request->description,
                'coorbudget' => $request->coorbudget,
                'glaccount' => $request->glaccount,
                'fiscal_year_period' => $request->fiscal_year_period,
                'quantity' => $request->quantity,
                'value' => $request->value,

            ]);

            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        InputUpdateOpex::destroy($id);
        $response = responseSuccess(trans('message.delete-success'));
        return response()->json($response,200);
    }
}
