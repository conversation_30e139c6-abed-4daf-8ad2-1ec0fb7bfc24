<?php

namespace App\Http\Controllers;

// use App\Models\PostJurnal;
use DataTables;
use App\Models\Menu;
use App\Models\Routes;
use App\Models\PostingJurnal;
use App\Models\PostingJurnalDetail;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use App\Services\SAPRFCNEW;

class PostJurnalController extends Controller
{
    public function index()
    {        
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();

        $data['company'] = $this->company();
        $data['account'] = $this->account();
        $data['doc_type'] = $this->doc_type();
        $data['post_key'] = $this->post_key();

        return view('postingjurnal', $data);
    }

    public function datatables(Request $request)
    {
        $query    = PostingJurnal::where('flag', 1)->get();
        $datas = [];      
        foreach($query as $row) {            
            $datas[] = array(
                "id" => $row['id'],
                "company" => $row['company_code'].' - '.$this->company($row['company_code']),
                "document_type" => $row['document_type'].' - '.$this->doc_type($row['document_type']),
                "no_sap" => $row['no_sap'],
                "doc_header" => $row['doc_header'],
                "referance" => $row['referance'],
                "currency" => $row['currency'],
                "posting_date" => date('d', strtotime($row['posting_date'])).' '.$this->name_month(date('m', strtotime($row['posting_date']))).' '.date('Y', strtotime($row['posting_date'])),
                "documet_date" => date('d', strtotime($row['documet_date'])).' '.$this->name_month(date('m', strtotime($row['documet_date']))).' '.date('Y', strtotime($row['documet_date'])),
                "periode_fy" => $row['periode'].'/'.$row['fiscal_year'],
            );
        }
        $data     = DataTables::of($datas)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }
    
    public function datahead($id)
    {
        $query    = PostingJurnal::where('id', $id)->get();  
        foreach($query as $row) {  
            // $row['posting_date'] = date('d-m-Y', strtotime($row['posting_date']));       
            // $row['documet_date'] = date('d-m-Y', strtotime($row['documet_date']));   
            $data[] = $row;
        }   
        $response = responseSuccess(trans('message.read-success'), $data);
        return response()->json($response,200);
    }

    public function detail($id){
        $data['post_key'] = $this->post_key();
        $data['detail'] = array();
        $detail = PostingJurnalDetail::where('posting_jurnal_id', $id)->get();
        foreach($detail as $row){
            $row['account'] = $this->account($row['gl_account']);
            $data['detail'][] = $row;
        }
        $response = responseSuccess(trans('message.read-success'), $data);
        return response()->json($response,200);
    }

    public function store(Request $request)
    {   
        $pj = PostingJurnal::create([
            'company_code' => $request->company_code,
            'doc_header' => $request->doc_header,  
            'referance' => $request->referance,  
            'document_type' => ($request->document_type), 
            'currency' => $request->currency,  
            'periode' => $request->periode,
            'fiscal_year' => $request->fiscal_year,  
            'posting_date' => $request->posting_date,  
            'documet_date' => $request->documet_date,  
        ]);    
        $response = responseSuccess(trans('message.read-success'),$pj);
        return response()->json($response,200);
    }
    public function insertdetail(Request $request)
    { 
        $ba = PostingJurnalDetail::create([
            'posting_jurnal_id' => $request->posting_jurnal_id,
            'posting_key' => $request->posting_key,  
            'gl_account' => $request->gl_account,  
            'amount' => abs($request->amount), 
            'gl_value_date' => $request->gl_value_date, 
            'assigment' => $request->assigment, 
            'ket_jurnal' => $request->ket_jurnal,  
            'taxcode' => $request->taxcode,
            'dsct_pct1' => $request->dsct_pct1,
            'flag' => $request->flag  
        ]);    
        $response = responseSuccess(trans('message.read-success'),$ba);
        return response()->json($response,200);
    }

    public function updatedetail($id, Request $request)
    {
        
        print_r($request);
    }

    public function destroy($id)
    {
        $postjurnal = PostingJurnalDetail::find($id);
        $postjurnal->delete();
        $res = responseSuccess(trans('message.delete-success'), $postjurnal);
        return response()->json($res, 200);
    }

    public function postingsap(Request $request)
    {    
        $dataUpdt = $this->findDataWhere(PostingJurnal::class, ['id' => $request->id]);  
        DB::beginTransaction();
        $username = "";
        
        try {
            $update['company_code'] = $request->company_code;
            $update['doc_header'] = $request->doc_header;
            $update['document_type'] = $request->document_type;
            $update['currency'] = $request->currency;
            $update['documet_date'] = $request->documet_date;
            $update['ref_sap'] = $request->referance == "undefined" ? null : $request->referance;
            $update['periode'] = $request->periode;
            $update['fiscal_year'] = $request->fiscal_year;
            $update['updated_by'] = Auth()->user()->id;
            $dataUpdt->update($update);
            DB::commit();
            
            $data['posting'] = $this->findDataWhere(PostingJurnal::class, ['id' => $request->id]);        
            $data['detail'] = PostingJurnalDetail::where('posting_jurnal_id', $request->id)->get();        
            $data['post_key'] = $this->post_key();

            $result = (new SAPRFCNEW)->postDataJurnal($data);
            if(isset($result['status_post'])){   
                $update_sap['no_sap'] = $result['doc_no_sap'];
                $update_sap['messages'] = $result['ket_sap'];
                $update_sap['status'] = $result['status_post'];
                if($result['status_post'] == 'berhasil'){
                    $update_sap['flag'] = 1;
                }
                
                $dataUpdt->update($update_sap);
                DB::commit();
            }
            //error connection
            if(isset($result['messages']) && $result['messages']=="Could not open connection"){
                $response = responseFail(trans("messages.update-fail"), $result);
                return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }
            $response = responseSuccess(trans("messages.update-success"), $result);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            DB::rollback(); 
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    private function name_month($search){
        $monthNames = ["Januari", "Februari", "Maret", "April", "Mei", "Juni", "Juli", "Augustus", "September", "Oktober", "November", "Desember"];
        $month = (int)$search;
        return $monthNames[$month];
    }
    private function company($search = ''){
        $data = [
            ['company_code'=>'2000', 'company_name'=>'PT. Semen Indonesia'],
            ['company_code'=>'4000', 'company_name'=>'PT. Semen Tonasa'],
            ['company_code'=>'5000', 'company_name'=>'PT. Semen Gresik'],
            ['company_code'=>'7000', 'company_name'=>'PT. Semen Indonesia'],
            ['company_code'=>'3000', 'company_name'=>'PT. Semen Padang'],
            ['company_code'=>'7001', 'company_name'=>'Solusi Bangun Indonesia'],
        ];

        if($search){          
            foreach($data as $row){
                if($row['company_code'] == $search){
                    return $row['company_name'];
                }
            }
        }

        return json_decode(json_encode($data), FALSE);
    }

    private function account($search = ''){
        $data = [
            ['account_code'=>'********', 'account_name'=>'BANK MANDIRI SG CABANG GRESIK'],
            ['account_code'=>'********', 'account_name'=>'ACCRUAL PREPAID TAX-ART.22'],
            ['account_code'=>'********', 'account_name'=>'GR/IR ADJUSTMENT'],
            ['account_code'=>'********', 'account_name'=>'POZZOLAND CONSUMPTION'],
            ['account_code'=>'********', 'account_name'=>'COPPER SLAG CONSUMPTION'],
        ];

        if($search){          
            foreach($data as $row){
                if($row['account_code'] == $search){
                    return $row['account_name'];
                }
            }
        }
        
        return json_decode(json_encode($data), FALSE);
    }

    private function doc_type($search = ''){
        $data = [
            ['doctype_code'=>'SA', 'doctype_name'=>'G/L Account Document'],
            ['doctype_code'=>'AA', 'doctype_name'=>'Asset Posting'],
            ['doctype_code'=>'AB', 'doctype_name'=>'Accounting Document'],
            ['doctype_code'=>'CH', 'doctype_name'=>'Contract Settlement'],
            ['doctype_code'=>'DA', 'doctype_name'=>'Customer Document'],
            ['doctype_code'=>'DF', 'doctype_name'=>'Customer Payment'],
            ['doctype_code'=>'DG', 'doctype_name'=>'Customer Credit Memo'],
            ['doctype_code'=>'DJ', 'doctype_name'=>'Pencairan Wanpestasi'],
            ['doctype_code'=>'DR', 'doctype_name'=>'Customer Invoice'],
            ['doctype_code'=>'DZ', 'doctype_name'=>'Customer Payment'],
        ];

        if($search){          
            foreach($data as $row){
                if($row['doctype_code'] == $search){
                    return $row['doctype_name'];
                }
            }
        }

        return json_decode(json_encode($data), FALSE);
    }

    private function post_key($search = ''){
        $data = [
            ['postkey_code'=>'40', 'postkey_type'=>'debit', 'postkey_name'=>'Debit entry'],
            ['postkey_code'=>'50', 'postkey_type'=>'credit', 'postkey_name'=>'Credit entry'],
            ['postkey_code'=>'80', 'postkey_type'=>'debit', 'postkey_name'=>'Stock initial entry'],
            ['postkey_code'=>'81', 'postkey_type'=>'debit', 'postkey_name'=>'Costs'],
            ['postkey_code'=>'83', 'postkey_type'=>'debit', 'postkey_name'=>'Price difference'],
            ['postkey_code'=>'84', 'postkey_type'=>'debit', 'postkey_name'=>'Consumption'],
            ['postkey_code'=>'85', 'postkey_type'=>'debit', 'postkey_name'=>'Change in stock'],
            ['postkey_code'=>'86', 'postkey_type'=>'debit', 'postkey_name'=>'GR/IR debit'],
            ['postkey_code'=>'90', 'postkey_type'=>'credit', 'postkey_name'=>'Stock initial entry'],
            ['postkey_code'=>'91', 'postkey_type'=>'credit', 'postkey_name'=>'Costs'],
            ['postkey_code'=>'93', 'postkey_type'=>'credit', 'postkey_name'=>'Price difference'],
            ['postkey_code'=>'94', 'postkey_type'=>'credit', 'postkey_name'=>'Consumption'],
            ['postkey_code'=>'95', 'postkey_type'=>'credit', 'postkey_name'=>'Change in stock'],
            ['postkey_code'=>'96', 'postkey_type'=>'credit', 'postkey_name'=>'GR/IR debit'],
        ];

        if($search){          
            foreach($data as $row){
                if($row['postkey_code'] == $search){
                    return $row['postkey_name'];
                }
            }
        }

        return json_decode(json_encode($data), FALSE);
    }
    
}
