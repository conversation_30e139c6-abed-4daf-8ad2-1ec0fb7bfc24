<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddKodeOpcoPlantIcsTable extends Migration
{
    public function up()
    {
        // Create table if it doesn't exist
        if (!Schema::hasTable('m_plant_ics')) {
            Schema::create('m_plant_ics', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('kode_plant', 30)->nullable();
                $table->string('name_plant', 100)->nullable();
                $table->string('kode_opco', 30)->nullable();
                $table->string('created_by', 30)->nullable();
                $table->string('updated_by', 30)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        } else {
            // Add column if table exists but column doesn't
            Schema::table('m_plant_ics', function (Blueprint $table) {
                if (!Schema::hasColumn('m_plant_ics', 'kode_opco')) {
                    $table->string('kode_opco', 30)->nullable();
                }
            });
        }
    }

    public function down()
    {
        if (Schema::hasTable('m_plant_ics')) {
            Schema::table('m_plant_ics', function (Blueprint $table) {
                if (Schema::hasColumn('m_plant_ics', 'kode_opco')) {
                    $table->dropColumn('kode_opco');
                }
            });
        }
    }
}