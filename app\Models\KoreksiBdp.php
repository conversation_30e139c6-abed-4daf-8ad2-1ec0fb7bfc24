<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

use DB;

class KoreksiBdp extends Model
{
  protected $table = 'tk_bdp';
  // protected $primaryKey = 'no';
  public $incrementing = false;
  protected $keyType = 'string';

  protected $fillable = [
    'no',
    'kode_plant',
    'tahun',
    'bdp_sig',
    'bdp_ghopo',
    'bdp_sg',
    'bdp_sp',
    'bdp_st',
    'bdp_sbi',
    'bdp_tlcc',
    'create_by',
    'update_by',
    'create_at',
    'update_at',
  ];
}
