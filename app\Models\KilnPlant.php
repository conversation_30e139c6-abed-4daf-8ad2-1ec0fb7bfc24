<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class KilnPlant extends Model
{
    use SoftDeletes;

    protected $table = 'm_kiln_plant';
    protected $primaryKey = 'id_kiln_plant';
    public $incrementing = true;

    protected $fillable = [
        'kode_plant',
        'name_plant',
        'kode_opco',
        'source_system',
        'reference_sap',
        'reference_tis',
        'reference_opc_rate',
        'reference_opc_status',
        'reference_opc_output',
        'no_pbi',
        'factorial'
        // 'create_by',
        // 'update_by',
    ];

    protected $dates = ['deleted_at'];

}
