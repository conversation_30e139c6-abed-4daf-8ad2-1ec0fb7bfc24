<?php

namespace App\Services;

use App\Models\Area;
use App\Models\Equipment;
use App\Models\KilnPlant;
use App\Models\Opco;
use Exception;
use Illuminate\Support\Facades\DB;

use SAPNWRFC\Connection as SapConnection;
use SAPNWRFC\Exception as SapException;

class SyncEquipmentService extends BaseService
{
    private function reqSap($date, $tplnr)
    {
        $sapConfig = $this->getSapConfig();

        try {
            $param = [
                'DATUM' => $date,
                'LEVDO' => '99',
                'LEVUP' => '0',
                'SANIN' => 'X',
                'SELECT_EQUI' => 'X',
                'SELECT_STPO' => 'X',
                'SELMOD' => 'D',
                'TPLNR' => $tplnr,
                'WITH_EQUI' => 'X',
                'WITH_BTYP' => 'X',
                'WITH_EQUI_HIER' => 'X',
                'WITH_IFLO_HIER' => 'X',
                'CAPID' => 'INST',
                'EMENG' => '0.000',
                'AUTHORITY_CHECK' => 'X'
            ];
            $options = [
                'rtrim' => true
            ];
            $sap = new SapConnection($sapConfig);
            $function = $sap->getFunction('ZPM_HIERARCHY_CALL_FM');
            $result = $function->invoke($param, $options);
            $this->store($result);

            return [
                'status' => 'success',
                'message' => 'Successfully synced and saved data',
            ];
        } catch (SapException $e) {
            return [
                'status' => 'fail',
                'message' => $e->getMessage(),
            ];
        } catch (Exception $e) {
            return [
                'status' => 'fail',
                'message' => $e->getMessage(),
            ];
        }
    }

    private function store($result)
    {
        try {
            DB::beginTransaction();
            $kilnPlant = KilnPlant::orderBy('id_kiln_plant', 'asc')->get();
            $codeAreas = collect();
            $areas = collect($result['IFLO_TAB'])->where('EQART', 'AREA')->all();
            foreach ($areas as $area) {
                if (substr_count($area['STRNO'], '-') == 2) {
                    $plant = $kilnPlant->where('reference_sap', $area['SWERK'])->first();
                    $areaModel = Area::updateOrCreate(['kode_area_sap' => $area['STRNO']], ['nm_area' => $area['PLTXT'], 'kode_plant' => $plant->kode_plant]);
                    $codeAreas->push($areaModel);
                }
            }

            $selectedIflos = collect($result['IFLO_TAB'])->filter(function ($value, $key) use ($codeAreas) {
                if (substr_count($value['STRNO'], '-') == 4) {
                    return in_array(substr($value['STRNO'], 0, 10), $codeAreas->pluck('kode_area_sap')->all());
                }
                return false;
            })->pluck('STRNO', 'TPLNR')->all();

            $equipments = collect($result['EQUI_TAB'])->where('TIDNR', '!=', '')->where('HEQUI', '')->all();
            $opcoModel = Opco::get();
            foreach ($equipments as $equipment) {
                $kodeEquipment = substr($equipment['EQUNR'], -8);
                $existIflo = array_key_exists($equipment['TPLNR'], $selectedIflos);
                if ($existIflo) {
                    $kodeArea = substr($selectedIflos[$equipment['TPLNR']], 0, 10);
                    $areaModel = $codeAreas->where('kode_area_sap', $kodeArea)->first();
                    $opco = $opcoModel->where('reference_sap', $equipment['BUKRS'])->first();
                    Equipment::updateOrCreate(['kode_equipment' => $kodeEquipment], [
                        'id_area' => $areaModel['id_area'],
                        'nm_equipment' => $equipment['EQKTX'],
                        'kode_opco' => $opco->kode_opco,
                        'kode_area_sap' => $kodeArea
                    ]);
                }
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw new Exception($e->getMessage());
        }
    }

    public function execute($date, $tplnr)
    {
        return $this->reqSap($date, $tplnr);
    }
}
