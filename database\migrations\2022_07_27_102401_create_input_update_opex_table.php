<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInputUpdateOpexTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('input_update_opex', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('costcenter_structure')->nullable();
            $table->string('material')->nullable();
            $table->string('description')->nullable();
            $table->string('fiscal_year_period')->nullable();
            $table->string('koor_budget')->nullable();
            $table->string('gl_account')->nullable();
            $table->string('quantity')->nullable();
            $table->string('value')->nullable();



            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('input_update_opex');
    }
}
