<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRkapPerformanceTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rkap_performance', function (Blueprint $table) {
            $table->bigIncrements('id_rkap_perfom');
            $table->string('kode_plant', 30);
            $table->string('id_parameter', 30);
            $table->string('tahun', 4);
            $table->string('bulan', 2);
            $table->bigInteger('nilai_rkap');
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rkap_performance');
    }
}
