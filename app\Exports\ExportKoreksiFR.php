<?php

namespace App\Exports;

use App\Models\Area;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\Models\ItemInspection;
use DB;

class ExportKoreksiFR implements WithColumnFormatting, FromCollection, WithEvents, WithHeadings, WithTitle, WithStyles
{

    /**
     * @return \Illuminate\Support\Collection
     */


    public function  __construct($opco, $tahun, $bulan)
    {
        $this->opco = $opco;
        $this->tahun = $tahun;
        $this->bulan = $bulan;
    }


    /**
     * @return string
     */
    public function title(): string
    {
        return 'Data Export Koreksi FR';
    }

    public function headings():array
     {
         return [
            'OPCO',
            'Date',
            'THN',
            'RKAP Bahan Bakar',
            'RKAP Bahan Baku Penolong',
            'RKAP Listrik',
            'RKAP Tenaga Kerja',
            'RKAP Pemeliharaan',
            'RKAP Deplesi, Penyusutan & Amortisasi',
            'RKAP Urusan Umum & Adm. Kantor',
            'RKAP Pajak & Asuransi',
            'RKAP Elim. Bahan Baku & Penolong',
            'RKAP Elim. Deplesi, Penyusutan & Amortisasi',
            'RKAP Elim. Urusan Umum & Adm. Kantor',
            'Rkap Prod. Clinker (ton)',
            'Rkap Prod. Cement (ton)',
            'Rkap Clinker sold (ton)',
            'Rkap prod. Output (ton)',
            'Bahan Bakar',
            'Bahan Baku & Penolong',
            'Listrik',
            'Tenaga Kerja',
            'Pemeliharaan',
            'Deplesi, Penyusutan & Amortisasi',
            'Urusan Umum & Adm. Kantor',
            'Pajak dan Asuransi',
            'Elim. Bahan Baku & Penolong',
            'Elim. Deplesi, Penyusutan & Amortisasi',
            'Elim. Urusan Umum & Adm. Kantor',
            'Real. Prod. Clinker (ton)',
            'Real. Prod. Cement (ton)',
            'Real. Clinker sold (ton)',
            'Real prod. Output (ton)',
            'ICS (ton)',
         ];
     }

    public function collection()
    {
        $result = DB::table('vw_tk_fr')->select(
                'kd_opco',
                'tanggal',
                'THN as thn',
                'rkap_bahan_bakar',
                'rkap_bahan_baku',
                'rkap_listrik',
                'rkap_tenaga_kerja',
                'rkap_pemeliharaan',
                'rkap_penyusutan',
                'rkap_administrasi_umum',
                'rkap_pajak_asuransi',
                'rkap_elim_bb',
                'rkap_elim_penyusutan',
                'rkap_elim_administrasi',
                'rkap_prod_klinker',
                'rkap_prod_semen',
                'rkap_clinker_sold',
                'rkap_prod_output',
                'bahan_bakar',
                'bahan_baku',
                'listrik',
                'tenaga_kerja',
                'pemeliharaan',
                'penyusutan',
                'administrasi_umum',
                'pajak_asuransi',
                'elim_bb',
                'elim_penyusutan',
                'elim_administrasi',
                'prod_klinker',
                'prod_semen',
                'clinker_sold',
                'prod_output',
                'ics',
        );
                if($this->opco){
                    $result = $result->where('kd_opco', $this->opco);
                }
                if($this->tahun){
                    $result = $result->where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $this->tahun);
                }
                if($this->bulan){
                    $result = $result->where(DB::raw("TO_CHAR(tanggal::timestamp, 'FMMM')"), $this->bulan);
                }
                $result = $result->get();

        return collect($result);
    }

    public function columnFormats(): array
    {
        return [
            'B' => NumberFormat::FORMAT_DATE_DDMMYYYY,
        ];
    }

        public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                //set background color for header (A1:D1)
                $event->sheet->getDelegate()
                ->getStyle('A1:AH1')
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('C4D79B');

                //set header (A1:D1) uneditable and set workspace (A2:D1000) editable
                // $event->sheet->protectCells('A1:D1', 'PASSWORD');
                // $event->sheet->getStyle('A2:D1000')->getProtection()->setLocked(\PhpOffice\PhpSpreadsheet\Style\Protection::PROTECTION_UNPROTECTED);
                // $event->sheet->getDelegate()->getProtection()->setSheet(true);

            },
        ];
    }

     //set bold for header
     public function styles(Worksheet $sheet)
     {
         return [
             'A1'    => ['font' => ['bold' => true]],
             'B1'    => ['font' => ['bold' => true]],
             'C1'    => ['font' => ['bold' => true]],
             'D1'    => ['font' => ['bold' => true]],
             'E1'    => ['font' => ['bold' => true]],
             'F1'    => ['font' => ['bold' => true]],
             'G1'    => ['font' => ['bold' => true]],
             'H1'    => ['font' => ['bold' => true]],
             'I1'    => ['font' => ['bold' => true]],
             'J1'    => ['font' => ['bold' => true]],
             'K1'    => ['font' => ['bold' => true]],
             'L1'    => ['font' => ['bold' => true]],
             'M1'    => ['font' => ['bold' => true]],
             'N1'    => ['font' => ['bold' => true]],
             'O1'    => ['font' => ['bold' => true]],
             'P1'    => ['font' => ['bold' => true]],
             'Q1'    => ['font' => ['bold' => true]],
             'R1'    => ['font' => ['bold' => true]],
             'S1'    => ['font' => ['bold' => true]],
             'T1'    => ['font' => ['bold' => true]],
             'U1'    => ['font' => ['bold' => true]],
             'V1'    => ['font' => ['bold' => true]],
             'W1'    => ['font' => ['bold' => true]],
             'X1'    => ['font' => ['bold' => true]],
             'Y1'    => ['font' => ['bold' => true]],
             'Z1'    => ['font' => ['bold' => true]],
             'AA1'    => ['font' => ['bold' => true]],
             'AB1'    => ['font' => ['bold' => true]],
             'AC1'    => ['font' => ['bold' => true]],
             'AD1'    => ['font' => ['bold' => true]],
             'AE1'    => ['font' => ['bold' => true]],
             'AF1'    => ['font' => ['bold' => true]],
             'AG1'    => ['font' => ['bold' => true]],
             'AH1'    => ['font' => ['bold' => true]],
         ];
     }
}
