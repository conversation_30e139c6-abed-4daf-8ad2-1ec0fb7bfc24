<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOpexTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('opex', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('fm_area')->nullable();
            $table->string('fiscal_year')->nullable();
            $table->string('funds_center')->nullable();
            $table->string('commitment_itm')->nullable();
            $table->double('amount')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('opex');
    }
}
