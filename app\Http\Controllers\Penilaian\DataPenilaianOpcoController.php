<?php

namespace App\Http\Controllers\Penilaian;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\DmmDataPenilaianOpco;
use App\Models\DmmPeriodePenilaian;
use App\Models\PenilaianOpco;
use App\Rules\storeAssessmentRules;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class DataPenilaianOpcoController extends Controller
{
    //
    public function index()
    {
        $data = [
            'title' => 'Data Penilaian OPCO',
            'breadcrumb' => [
                [
                    'title'=>'Penilaian',
                    'url'=>'#',
                ],
                [
                    'title'=>'Data Penilaian OPCO',
                    'url'=>'data-penilaian-opco',
                ]
            ],
        ];
        $qCompany =Company::query();
        if(!auth()->user()->hasRole('ADMIN')){
            $qCompany->where('company',auth()->user()->company);
        }
        $data['company'] = $qCompany->get();
        $data['periode_penilaian'] = DmmPeriodePenilaian::select('id','tahun','periode','status')->where('status','y')->get();
        $data['menus'] = $this->getDashboardMenu();
        return view('penilaian.data-penilaian-opco',$data);
    }

    public function storeAssessment(Request $request)
    {
        $attributes =  $request->only(['company_id','periode_penilaian_id']);
        $attributes['penilaian_opco_id'] = $attributes;
        $roles = [
            'company_id' => 'required|exists:m_company,id',
            'periode_penilaian_id' => 'required|exists:dmm_periode_penilaian,id',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'date'   => trans('messages.date'),
            'penilaian_opco_id' => 'Periode Penilaian Opco yang Dipilih Belum Tersedia'
        ];
        $this->validators($attributes, $roles, $messages);
        $rulesLanjutan = [
            'penilaian_opco_id' => ['required',new storeAssessmentRules],
        ];  
        $this->validators($attributes, $rulesLanjutan, $messages);
        $attributes['created_by'] = Auth()->user()->id;
        $penilaianOpco = PenilaianOpco::where('company_id',$attributes['company_id'])
                                    ->where('periode_penilaian_id',$attributes['periode_penilaian_id'])
                                    ->where('status','y')
                                    ->first();
        DB::beginTransaction();
        try {
            //code...
            $penilaianOpco = DmmDataPenilaianOpco::create(
                [
                    'penilaian_opco_id' => $penilaianOpco->id,
                    'created_by' => $attributes['created_by']
                ]
            );
            DB::commit();
            $response = responseSuccess(trans("messages.create-success"), $penilaianOpco);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            //throw $th;
            DB::rollback();
            $response = responseFail(trans("messages.create-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function datatables(Request $request)
    {
        $query = DmmDataPenilaianOpco::list();

        $columns = [
            'mc.description'=>'company_desc',
            'dpp.tahun'=>'tahun',
            'dpp.periode'=>'periode',
            'dpo.end_date'=>'end_date',
            'u.username'=>'creator',
            'u2.username'=>'created_by_name',
            'ddpo.status'=>'status'
        ];
        
        $data  = DataTables::of($query)
            ->filter(function ($query) use ($request, $columns) {
                $this->filterColumn($columns, $request, $query);
            })
            ->order(function ($query) use ($request, $columns) {
                $this->orderColumn($columns, $request, $query);
            })
            ->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function show($uuid)
    {
        $attributes['uuid'] = $uuid;

        $roles = [  
            'uuid' => 'required|exists:dmm_data_penilaian_opco',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages, );
        $data['data'] = DmmDataPenilaianOpco::list()->where('ddpo.uuid', $uuid)->first();
        $data = [
            'title' => 'Data Penilaian OPCO',
            'breadcrumb' => [
                [
                    'title'=>'Penilaian',
                    'url'=>'#',
                ],
                [
                    'title'=>'Data Penilaian OPCO',
                    'url'=>'data-penilaian-opco',
                ]
            ],
        ];
        $qCompany =Company::query();
        if(!auth()->user()->hasRole('ADMIN')){
            $qCompany->where('company',auth()->user()->company);
        }
        $data['company'] = $qCompany->get();
        $data['periode_penilaian'] = DmmPeriodePenilaian::select('id','tahun','periode','status')->where('status','y')->get();
        $data['menus'] = $this->getDashboardMenu();
        return view('penilaian.data-penilaian-opco',$data);
    }
}
