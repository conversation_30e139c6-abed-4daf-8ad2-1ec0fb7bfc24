<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMMaterialTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_material', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('kodematerial')->nullable();
            $table->string('name')->nullable();
            $table->string('description')->nullable();
            $table->string('material_type')->nullable();
            $table->string('parenth')->nullable();
            $table->string('valuation_class')->nullable();
            $table->string('company')->nullable();
            $table->string('plant')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('m_material');
    }
}
