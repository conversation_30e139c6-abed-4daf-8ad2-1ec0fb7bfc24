<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSyncOpcLogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sync_opc_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('sync_opc_config_id');
            $table->integer('year');
            $table->integer('month');
            $table->enum('status', ['success', 'process', 'fail']);
            $table->string('url');
            $table->text('parameter');
            $table->string('note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sync_opc_logs');
    }
}
