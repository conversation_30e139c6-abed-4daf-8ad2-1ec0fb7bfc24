<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use Illuminate\Http\Request;
use App\Models\RealPerformance;
use App\Models\RealisasiPerformance;
use App\Models\KilnPlant;
use App\Models\Opco;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\RealPerformanceExport;

class RealPerformanceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = [
            'title' => 'Realisasi Performance',
            'breadcrumb' => [
                [
                    'title'=>'Data Realisasi',
                    'url'=>'/real-performance',
                ],
                [
                    'title'=>'Realisasi Performance',
                    'url'=>'',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        // $real_capex = RealPerformance::select([
        //     'id_realisasi_performance',
        //     'kode_plant',
        //     'tanggal',
        //     'oph',
        //     'opdt',
        //     'pdt',
        //     'stop_idle',
        //     'fy_stop',
        //     'frek_updt',
        //     'problem',
        //     'created_at',
        //     'updated_at'
        // ]);
        // dd($real_capex);

        return view('realPerformance', $data);
    }

    public function getDatatables(Request $request)
    {
        // if ($request->filter_plant == '') {
        //     $filter_plant = '1=1';
        // } else {
        //     $filter_plant = "kode_plant = '" . $request->filter_plant . "'";
        // }
        // if ($request->filter_opco == '') {
        //     $filter_opco = '1=1';
        // } else {
        //     $filter_opco = "kode_opco = '" . $request->filter_opco . "'";
        // }
        // if ($request->filter_tahun == '') {
        //     $filter_tahun = '1=1';
        // } else {
        //     $filter_tahun = " substring(tanggal from 1 for 4)  = '" . $request->filter_tahun . "'";
        // }
        // if ($request->filter_bulan == '') {
        //     $filter_bulan = "1=1";
        // } else {
        //     $filter_bulan = " substring(tanggal from 6 for 2)  = '" . $request->filter_bulan . "'";
        // }
        $realPerformance =  DB::table('ts_realisasi_performance')->select(
                                    ['ts_realisasi_performance.id_realisasi_performance',
                                    'ts_realisasi_performance.tanggal',
                                    'ts_realisasi_performance.rate_netto',
                                    'ts_realisasi_performance.rate_gross',
                                    'ts_realisasi_performance.koreksi',
                                    'ts_realisasi_performance.act_prod',
                                    'ts_realisasi_performance.act_idle_prod',
                                    'ts_realisasi_performance.net_avail',
                                    'ts_realisasi_performance.kode_opco',
                                    'ts_realisasi_performance.oph',
                                    'ts_realisasi_performance.kode_plant',
                                    'ts_realisasi_performance.updt',
                                    'ts_realisasi_performance.pdt',
                                    'ts_realisasi_performance.stop_idle',
                                    'ts_realisasi_performance.fy_stop',
                                    'ts_realisasi_performance.frek_updt',
                                    'vw_rkap_performance.prod_rate',
                                    'ts_realisasi_performance.bulan',
                                    'ts_realisasi_performance.tahun'])
                                ->join('vw_rkap_performance', function($join)
                                {
                                    $join->on('vw_rkap_performance.kode_plant', '=', 'ts_realisasi_performance.kode_plant');
                                    $join->on(DB::raw("concat(vw_rkap_performance.tahun, '-', vw_rkap_performance.bulan)"),'=', DB::raw("concat(ts_realisasi_performance.tahun, '-', ts_realisasi_performance.bulan)"));
                                });
        if($request->filter_opco){
            $realPerformance = $realPerformance -> where('ts_realisasi_performance.kode_opco', $request->filter_opco);
        }
        if($request->filter_plant){
            $realPerformance = $realPerformance -> where('ts_realisasi_performance.kode_plant', $request->filter_plant);
        }
        if($request->filter_tahun){
            $realPerformance = $realPerformance -> where('ts_realisasi_performance.tahun', $request->filter_tahun);
        }
        if($request->filter_bulan){
            $realPerformance = $realPerformance -> where('ts_realisasi_performance.bulan', $request->filter_bulan);
        }
        $realPerformance = $realPerformance->orderBy('ts_realisasi_performance.tanggal','ASC')->get();
            
        $data     = DataTables::of($realPerformance)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function getFilterRealPerformance()
    {
        $filter_plant = KilnPlant::select('kode_plant')->get();
        $filter_opco = Opco::select('kode_opco')->get();

        $data = [
            'kode_plant' => $filter_plant,
            'filter_opco' => $filter_opco
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function editKoreksi(Request $request)
    {
        DB::beginTransaction();
        try {
            $data = array(
                'koreksi' => $request->value,
            );
            RealisasiPerformance::where('id_realisasi_performance', $request->id_realisasi_performance)->update($data);
            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Success Update Koreksi'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => $th->getMessage()
            ]);
        }
    }

    public function export(Request $request)
    {
        // Expot data with Collection
        $data =  DB::table('ts_realisasi_performance')->select(
                                    [
            'ts_realisasi_performance.kode_opco',
            'ts_realisasi_performance.kode_plant',
                                    'ts_realisasi_performance.tanggal',
                                    'ts_realisasi_performance.oph',
                                    'ts_realisasi_performance.updt',
                                    'ts_realisasi_performance.pdt',
                                    'ts_realisasi_performance.stop_idle',
                                    'ts_realisasi_performance.fy_stop',
                                    'ts_realisasi_performance.frek_updt',
                                    'ts_realisasi_performance.net_avail',
                                    'vw_rkap_performance.prod_rate',
                                    'ts_realisasi_performance.koreksi',
                                    'ts_realisasi_performance.act_prod',
                                    'ts_realisasi_performance.act_idle_prod',
                                    'ts_realisasi_performance.rate_gross',
                                    'ts_realisasi_performance.rate_netto'
                                    ])
                                ->leftjoin('m_kiln_plant','m_kiln_plant.kode_plant','=','ts_realisasi_performance.kode_plant')
                                ->join('vw_rkap_performance', function($join)
                                {
                                    $join->on('vw_rkap_performance.kode_plant', '=', 'ts_realisasi_performance.kode_plant');
                                    $join->on(DB::raw("concat(vw_rkap_performance.tahun, '-', vw_rkap_performance.bulan)"),'=', DB::raw("concat(ts_realisasi_performance.tahun, '-', ts_realisasi_performance.bulan)"));
                                });
        if($request->filter_opco){
            $data = $data -> where('ts_realisasi_performance.kode_opco', $request->filter_opco);
        }
        if($request->filter_plant){
            $data = $data -> where('ts_realisasi_performance.kode_plant', $request->filter_plant);
        }
        if($request->filter_tahun){
            $data = $data -> where('ts_realisasi_performance.tahun', $request->filter_tahun);
        }
        if($request->filter_bulan){
            $data = $data -> where('ts_realisasi_performance.bulan', $request->filter_bulan);
        }
        $data = $data->orderBy('m_kiln_plant.no_pbi','ASC')->orderBy('ts_realisasi_performance.tanggal','ASC')->get();

        return Excel::download(new RealPerformanceExport($data), 'RealisasiPerformance.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);

        // Export data with View
        // return Excel::download(new RealPerformanceExport, 'RealisasiPerformance.xlsx');

        // Expot data with format XLSX
        // return (new RealPerformanceExport)->download('RealisasiPerformance.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }
}
