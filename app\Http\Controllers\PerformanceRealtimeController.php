<?php

namespace App\Http\Controllers;
set_time_limit(0);
use App\Models\Menu;
use App\Models\Opco;
use App\Models\KilnPlant;
use App\Models\PlantPerformRealtime;
use Illuminate\Http\Request;
use App\Models\Kiln_Stop;
use Yajra\DataTables\DataTables;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\KilnRate;
use App\Imports\ImportInspectionItemModel;
use App\Exports\ExportDataInspectionItem;


class PerformanceRealtimeController extends Controller
{

    public function KilnNettoOee(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);
        
        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant',
            'no_pbi'
        );
        if ($request->opco) {
            $no_pbi = $no_pbi->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $no_pbi = $no_pbi->where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
            ->get();

            
        $dataReal = DB::table('ts_realisasi_performance')->select(
            'kode_plant as kode_plant',
            'kode_opco as kode_opco',
            DB::raw('SUM(ROUND(oph::numeric,2)) as oph'),
            DB::raw('SUM(ROUND(stop_idle::numeric,2)) as stop_idle'),
            DB::raw('SUM(ROUND(act_prod::numeric,2)) as act_prod'),
            DB::raw('SUM(ROUND(act_idle_prod::numeric,2)) as act_idle_prod'),
            DB::raw('SUM(ROUND(koreksi::numeric,2)) as koreksi'),
            DB::raw('SUM(ROUND(cal::numeric,2)) as cal'),
        );

        if ($request->opco) {
            $dataReal = $dataReal->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataReal = $dataReal->where('kode_plant', $request->plant);
        }

        $dataReal = $dataReal->whereBetween('tanggal', [$start, $now]);

        $dataReal = $dataReal->groupBy('kode_opco')
            ->groupBy('kode_plant')
            ->orderBy('kode_plant', 'ASC')
            ->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
            'kode_plant as kode_plant',
            'kode_opco as kode_opco',
            DB::raw('SUM(oph) as oph'),
            DB::raw('SUM(stop_idle) as stop_idle'),
            DB::raw('SUM(cal) as cal'),
            DB::raw('AVG(bdp_rate) as bdp'),
            DB::raw('AVG(prod_rate) as prod_rate'),
        );
        if ($request->opco) {
            $dataRKAP = $dataRKAP->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataRKAP = $dataRKAP->where('kode_plant', $request->plant);
        }

        $dataRKAP = $dataRKAP->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])
        ->where('tahun', $now_years);

        $dataRKAP = $dataRKAP->groupBy('kode_opco')
            ->groupBy('kode_plant')
            ->orderBy('kode_plant', 'ASC')
            ->get();

        $data = [];
        foreach ($dataReal as $k => $v) {
            $data[$v->kode_plant]['real_oph'] = $v->oph;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_act_prod'] = $v->act_prod;
            $data[$v->kode_plant]['real_act_idle_prod'] = $v->act_idle_prod;
            $data[$v->kode_plant]['real_koreksi'] = $v->koreksi;
            $data[$v->kode_plant]['real_cal'] = $v->cal;
        }

        foreach ($dataRKAP as $k => $v) {
            $data[$v->kode_plant]['rkap_oph'] = $v->oph;
            $data[$v->kode_plant]['rkap_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['rkap_cal'] = $v->cal;
            $data[$v->kode_plant]['rkap_bdp'] = $v->bdp;
            $data[$v->kode_plant]['rkap_prod_rate'] = $v->prod_rate;
        }

        $dtSort = [];
        foreach ($no_pbi as $k => $v) {
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant, $data) ? $data[$v->kode_plant] : [];
        }

        $data = NULL;
        foreach ($dtSort as $k => $v) {
            $data[$k]['real_oph'] = array_key_exists('real_oph', $v) ? $v['real_oph'] : 0;
            $data[$k]['real_stop_idle'] = array_key_exists('real_stop_idle', $v) ? $v['real_stop_idle'] : 0;
            $data[$k]['real_act_prod'] = array_key_exists('real_act_prod', $v) ? $v['real_act_prod'] : 0;
            $data[$k]['real_act_idle_prod'] = array_key_exists('real_act_idle_prod', $v) ? $v['real_act_idle_prod'] : 0;
            $data[$k]['real_koreksi'] = array_key_exists('real_koreksi', $v) ? $v['real_koreksi'] : 0;
            $data[$k]['real_cal'] = array_key_exists('real_cal', $v) ? $v['real_cal'] : 0;
            $data[$k]['rkap_oph'] = array_key_exists('rkap_oph', $v) ? $v['rkap_oph'] : 0;
            $data[$k]['rkap_stop_idle'] = array_key_exists('rkap_stop_idle', $v) ? $v['rkap_stop_idle'] : 0;
            $data[$k]['rkap_cal'] = array_key_exists('rkap_cal', $v) ? $v['rkap_cal'] : 0;
            $data[$k]['rkap_bdp'] = array_key_exists('rkap_bdp', $v) ? $v['rkap_bdp'] : 0;
            $data[$k]['rkap_prod_rate'] = array_key_exists('rkap_prod_rate', $v) ? $v['rkap_prod_rate'] : 0;
        }

        foreach ($data as $key => $value) {
            if ((($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) / 24) >= 1) {
                $data[$key]['tpd'] = ($data[$key]['real_act_prod'] + $data[$key]['real_act_idle_prod'] + $data[$key]['real_koreksi']) / (($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) / 24);
            } else {
                $data[$key]['tpd'] = $data[$key]['real_act_prod'] + $data[$key]['real_act_idle_prod'] + $data[$key]['real_koreksi'];
            }
        }

        foreach ($data as $key => $value) {
            if ($data[$key]['tpd'] > 1 && $data[$key]['rkap_bdp'] > 0) {
                $data[$key]['yield'] = ($data[$key]['tpd'] / $data[$key]['rkap_bdp']) * 100;
            } else {
                $data[$key]['yield'] = $data[$key]['tpd'] * 100;
            }
        }

        foreach ($data as $key => $value) {
            if (($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) > 1) {
                $data[$key]['nai'] = (($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) / $data[$key]['real_cal']) * 100;
            } else {
                $data[$key]['nai'] = ($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) * 100;
            }
        }

        if ($request->tanggal) {
            foreach ($data as $key => $value) {
                $countDay = $data[$key]['rkap_cal'] / 24; // jumlah hari dalam satu bulan; 24 = 24jam
                $firstDay = (int) substr($request->tanggal, 3, 2);
                $lastDay = (int) substr($request->tanggal, 16, 2);
                $rangeDay = $lastDay - $firstDay + 1;
                if ($countDay != 0) {
                    $data[$key]['rkap_oph'] = (int) $data[$key]['rkap_oph'] * $rangeDay / $countDay;
                    $data[$key]['rkap_stop_idle'] = (int) $data[$key]['rkap_stop_idle'] * $rangeDay / $countDay;
                    $data[$key]['rkap_cal'] = (int) $data[$key]['rkap_cal'] * $rangeDay / $countDay;
                    $data[$key]['rkap_bdp'] = (int) $data[$key]['rkap_bdp'] * $rangeDay / $countDay;
                    $data[$key]['rkap_prod_rate'] = (int) $data[$key]['rkap_prod_rate'] * $rangeDay / $countDay;
                }
            }
        }
        foreach ($data as $key => $value) {
            if ($data[$key]['tpd'] > 1 && $data[$key]['rkap_bdp'] > 0) {
                $data[$key]['rkap_yield'] = ($data[$key]['tpd'] / $data[$key]['rkap_bdp']) * 100;
            } else {
                $data[$key]['rkap_yield'] = $data[$key]['tpd'] * 100;
            }
        }

        foreach ($data as $key => $value) {
            if (($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle']) > 1 && $data[$key]['real_cal'] != 0) {
                $data[$key]['rkap_nai'] = (($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle']) / $data[$key]['real_cal']) * 100;
            } else {
                $data[$key]['rkap_nai'] = ($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle']) * 100;
            }
        }

        foreach ($data as $key => $value) {
            $data[$key]['hasil'] = (round($data[$key]['yield'], 1) * round($data[$key]['nai'], 1)) / 100;
        }

        $list_kode_plant = [];
        $datahasil = [];
        $i = 0;

        foreach ($data as $key => $value) {
            $i++;
            $data_hasil = array_key_exists('hasil', $value) ? (float)$value['hasil'] : 0;
            $datahasil[$key] = round($data_hasil, 1);
        }

        asort($datahasil);
        $dataSort = [];
        $i = 0;
        foreach ($datahasil as $key => $value) {
            $i++;
            if ($value > 85) {
                $dataSort[$i]['key'] = $key;
                $dataSort[$i]['data'] = $value;
                $dataSort[$i]['color'] = '#B4C7DA';
            } elseif ($value > 70) {
                $dataSort[$i]['key'] = $key;
                $dataSort[$i]['data'] = $value;
                $dataSort[$i]['color'] = '#FBD677';
            } else {
                $dataSort[$i]['key'] = $key;
                $dataSort[$i]['data'] = $value;
                $dataSort[$i]['color'] = '#E87D79';
            }
        }

        return [
            'message' => 'Succes',
            'dataSort' => $dataSort
        ];
    }

    public function getKilnGrosOee(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant',
            'no_pbi'
        );
        if ($request->opco) {
            $no_pbi = $no_pbi->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $no_pbi = $no_pbi->where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
            ->get();


        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant',
            'no_pbi'
        );
        if ($request->opco) {
            $no_pbi = $no_pbi->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $no_pbi = $no_pbi->where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
            ->get();


        $dataReal = DB::table('ts_realisasi_performance')->select(
            'kode_plant as kode_plant',
            'kode_opco as kode_opco',
            DB::raw('SUM(oph) as oph'),
            DB::raw('SUM(stop_idle) as stop_idle'),
            DB::raw('SUM(act_prod) as act_prod'),
            DB::raw('SUM(act_idle_prod) as act_idle_prod'),
            DB::raw('SUM(koreksi) as koreksi'),
            DB::raw('SUM(cal) as cal'),
        );

        if ($request->opco) {
            $dataReal = $dataReal->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataReal = $dataReal->where('kode_plant', $request->plant);
        }

        $dataReal = $dataReal->whereBetween('tanggal', [$start, $now]);

        $dataReal = $dataReal->groupBy('kode_opco')
            ->groupBy('kode_plant')
            ->orderBy('kode_plant', 'ASC')
            ->get();
        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
            'kode_plant as kode_plant',
            'kode_opco as kode_opco',
            // DB::raw('SUM(koreksi) as koreksi'),
            // DB::raw('SUM(act_idle_prod) as act_idle_prod'),
            DB::raw('SUM(stop_idle) as stop_idle'),
            DB::raw('SUM(oph) as oph'),
            DB::raw('SUM(cal) as cal'),
            DB::raw('AVG(bdp_rate) as bdp_rate'),
            DB::raw('AVG(prod_rate) as avg_prod_rate'),
            // DB::raw('SUM(act_prod) as act_prod'),
        );
        if ($request->opco) {
            $dataRKAP = $dataRKAP->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataRKAP = $dataRKAP->where('kode_plant', $request->plant);
        }

        $dataRKAP = $dataRKAP->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])->where('tahun', $now_years);

        $dataRKAP = $dataRKAP->groupBy('kode_opco')
            ->groupBy('kode_plant')
            ->orderBy('kode_plant', 'ASC')
            ->get();

        $data = [];
        foreach ($dataReal as $k => $v) {
            $data[$v->kode_plant]['real_oph'] = $v->oph;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_act_prod'] = $v->act_prod;
            $data[$v->kode_plant]['real_act_idle_prod'] = $v->act_idle_prod;
            $data[$v->kode_plant]['real_koreksi'] = $v->koreksi;
            $data[$v->kode_plant]['real_cal'] = $v->cal;
        }

        foreach ($dataRKAP as $k => $v) {
            $data[$v->kode_plant]['rkap_avg_prod_rate'] = $v->avg_prod_rate;
            $data[$v->kode_plant]['rkap_bdp_rate'] = $v->bdp_rate;
            $data[$v->kode_plant]['rkap_oph'] = $v->oph;
            $data[$v->kode_plant]['rkap_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['rkap_cal'] = $v->cal;
            // $data[$v->kode_plant]['rkap_act_prod'] = $v->act_prod;
            // $data[$v->kode_plant]['rkap_act_idle_prod'] = $v->act_idle_prod;
            // $data[$v->kode_plant]['rkap_koreksi'] = $v->koreksi;
        }

        $dtSort = [];
        foreach ($no_pbi as $k => $v) {
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant, $data) ? $data[$v->kode_plant] : [];
        }

        $data = NULL;
        foreach ($dtSort as $k => $v) {
            $list_kode_plant[] = $k;
            $data[$k]['real_oph'] = array_key_exists('real_oph', $v) ? $v['real_oph'] : 0;
            $data[$k]['real_stop_idle'] = array_key_exists('real_stop_idle', $v) ? $v['real_stop_idle'] : 0;
            $data[$k]['real_act_prod'] = array_key_exists('real_act_prod', $v) ? $v['real_act_prod'] : 0;
            $data[$k]['real_act_idle_prod'] = array_key_exists('real_act_idle_prod', $v) ? $v['real_act_idle_prod'] : 0;
            $data[$k]['real_koreksi'] = array_key_exists('real_koreksi', $v) ? $v['real_koreksi'] : 0;
            $data[$k]['real_cal'] = array_key_exists('real_cal', $v) ? $v['real_cal'] : 0;
            $data[$k]['rkap_avg_prod_rate'] = array_key_exists('rkap_avg_prod_rate', $v) ? $v['rkap_avg_prod_rate'] : 0;
            $data[$k]['rkap_bdp_rate'] = array_key_exists('rkap_bdp_rate', $v) ? $v['rkap_bdp_rate'] : 0;
            $data[$k]['rkap_oph'] = array_key_exists('rkap_oph', $v) ? $v['rkap_oph'] : 0;
            $data[$k]['rkap_stop_idle'] = array_key_exists('rkap_stop_idle', $v) ? $v['rkap_stop_idle'] : 0;
            $data[$k]['rkap_cal'] = array_key_exists('rkap_cal', $v) ? $v['rkap_cal'] : 0;
            // $data[$k]['rkap_act_prod'] = array_key_exists('rkap_act_prod', $v) ? $v['rkap_act_prod'] : 0;
            // $data[$k]['rkap_act_idle_prod'] = array_key_exists('rkap_act_idle_prod', $v) ? $v['rkap_act_idle_prod'] : 0;
            // $data[$k]['rkap_koreksi'] = array_key_exists('rkap_koreksi', $v) ? $v['rkap_koreksi'] : 0;
        }



        $data_value = [];

        foreach ($data as $i => $v) {
            // oee_gross_real
            if (($data[$i]['real_oph']  / 24) >= 1) {
                $rate_gross_real =  $data[$i]['real_act_prod'] / ($data[$i]['real_oph'] / 24);
            } else {
                $rate_gross_real =  $data[$i]['real_act_prod'];
            }

            if ($data[$i]['rkap_bdp_rate'] > 1) {
                $yield_gross_real = $rate_gross_real / $data[$i]['rkap_bdp_rate'];
            } else {
                $yield_gross_real = $rate_gross_real;
            }

            if ($data[$i]['real_cal'] > 1) {
                $gai_real = $data[$i]['real_oph'] / $data[$i]['real_cal'];
            } else {
                $gai_real = $data[$i]['real_oph'];
            }
            $data_value[$i]['oee_gross_real'] = round(round(($yield_gross_real * $gai_real),2)*100,1);

            // oee_gross_rkap
            $rate_gross_rkap = $data[$i]['rkap_avg_prod_rate'];


            if ($data[$i]['rkap_bdp_rate'] > 1) {
                $yield_gross_rkap = $rate_gross_rkap / $data[$i]['rkap_bdp_rate'];
            } else {
                $yield_gross_rkap = $rate_gross_rkap;
            }

            if ($data[$i]['rkap_cal'] > 1) {
                $gai_rkap = $data[$i]['rkap_oph'] / $data[$i]['rkap_cal'];
            } else {
                $gai_rkap = $data[$i]['rkap_oph'];
            }

            $data_value[$i]['oee_gross_rkap'] = round(round(($yield_gross_rkap * $gai_rkap),2)*100,1);
        }

        $data = [
            'oee_gross'      => $data_value,
            'list_kode_plant' => $list_kode_plant
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function getKilnRate(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant',
            'no_pbi'
        );
        if ($request->opco) {
            $no_pbi = $no_pbi->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $no_pbi = $no_pbi->where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
            ->get();

        $dataReal = DB::table('ts_realisasi_performance')->select(
            'kode_plant as kode_plant',
            'kode_opco as kode_opco',
            DB::raw('SUM(oph) as oph'),
            DB::raw('SUM(stop_idle) as stop_idle'),
            DB::raw('SUM(act_prod) as act_prod'),
            DB::raw('SUM(act_idle_prod) as act_idle_prod'),
            DB::raw('SUM(koreksi) as koreksi'),
            DB::raw('SUM(cal) as cal'),
        );

        if ($request->opco) {
            $dataReal = $dataReal->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataReal = $dataReal->where('kode_plant', $request->plant);
        }
        $dataReal = $dataReal->whereBetween('tanggal', [$start, $now]);


        $dataReal = $dataReal->groupBy('kode_opco')
            ->groupBy('kode_plant')
            ->orderBy('kode_plant', 'ASC')
            ->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
            'kode_plant as kode_plant',
            'kode_opco as kode_opco',
            DB::raw('avg(prod_rate) as avg_act_prod'),
            // DB::raw('SUM(act_prod) as sum_act_prod'),
            DB::raw('SUM(oph) as oph'),
            DB::raw('SUM(stop_idle) as stop_idle'),
            DB::raw('SUM(cal) as cal'),
            // DB::raw('sum(act_idle_prod) as act_idle_prod'),
            // DB::raw('sum(koreksi) as koreksi'),
        );
        if ($request->opco) {
            $dataRKAP = $dataRKAP->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataRKAP = $dataRKAP->where('kode_plant', $request->plant);
        }

        $dataRKAP = $dataRKAP->whereBetween(DB::raw('bulan::numeric'), [1, $now_month]);
        $dataRKAP = $dataRKAP -> where('tahun', $now_years);
        $dataRKAP = $dataRKAP->groupBy('kode_opco')
            ->groupBy('kode_plant')
            ->orderBy('kode_plant', 'ASC')
            ->get();

        $data = [];
        foreach ($dataReal as $k => $v) {
            $data[$v->kode_plant]['real_oph'] = $v->oph;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_act_prod'] = $v->act_prod;
            $data[$v->kode_plant]['real_act_idle_prod'] = $v->act_idle_prod;
            $data[$v->kode_plant]['real_koreksi'] = $v->koreksi;
            $data[$v->kode_plant]['real_cal'] = $v->cal;
        }

        foreach ($dataRKAP as $k => $v) {
            $data[$v->kode_plant]['rkap_oph'] = $v->oph;
            $data[$v->kode_plant]['rkap_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['rkap_cal'] = $v->cal;
            $data[$v->kode_plant]['rkap_avg_act_prod'] = $v->avg_act_prod;
            // $data[$v->kode_plant]['rkap_sum_act_prod'] = $v->sum_act_prod;
            // $data[$v->kode_plant]['rkap_act_idle_prod'] = $v->act_idle_prod;
            // $data[$v->kode_plant]['rkap_koreksi'] = $v->koreksi;
        }

        $dtSort = [];
        foreach ($no_pbi as $k => $v) {
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant, $data) ? $data[$v->kode_plant] : [];
        }

        $data = NULL;
        foreach ($dtSort as $k => $v) {
            $list_kode_plant[] = $k;
            $data[$k]['real_oph'] = array_key_exists('real_oph', $v) ? $v['real_oph'] : 0;
            $data[$k]['real_stop_idle'] = array_key_exists('real_stop_idle', $v) ? $v['real_stop_idle'] : 0;
            $data[$k]['real_act_prod'] = array_key_exists('real_act_prod', $v) ? $v['real_act_prod'] : 0;
            $data[$k]['real_act_idle_prod'] = array_key_exists('real_act_idle_prod', $v) ? $v['real_act_idle_prod'] : 0;
            $data[$k]['real_koreksi'] = array_key_exists('real_koreksi', $v) ? $v['real_koreksi'] : 0;
            $data[$k]['real_cal'] = array_key_exists('real_cal', $v) ? $v['real_cal'] : 0;
            $data[$k]['rkap_oph'] = array_key_exists('rkap_oph', $v) ? $v['rkap_oph'] : 0;
            $data[$k]['rkap_stop_idle'] = array_key_exists('rkap_stop_idle', $v) ? $v['rkap_stop_idle'] : 0;
            $data[$k]['rkap_cal'] = array_key_exists('rkap_cal', $v) ? $v['rkap_cal'] : 0;
            $data[$k]['rkap_avg_act_prod'] = array_key_exists('rkap_avg_act_prod', $v) ? $v['rkap_avg_act_prod'] : 0;
            // $data[$k]['rkap_koreksi'] = array_key_exists('rkap_koreksi', $v) ? $v['rkap_koreksi'] : 0;
        }


        $data_value = [];

        foreach ($data as $i => $v) {
            if (($data[$i]['real_oph'] + $data[$i]['real_stop_idle'] / 24) >= 1) {
                $data_value[$i]['rate_netto_real'] =  round(($data[$i]['real_act_prod'] + $data[$i]['real_act_idle_prod'] + $data[$i]['real_koreksi']) / (($data[$i]['real_oph'] + $data[$i]['real_stop_idle']) / 24), 1);
            } else {
                $data_value[$i]['rate_netto_real'] =  round($data[$i]['real_act_prod'] + $data[$i]['real_act_idle_prod'] + $data[$i]['real_koreksi'], 1);
            }

            if (($data[$i]['real_oph']  / 24) >= 1) {
                $data_value[$i]['rate_gross_real'] =  round($data[$i]['real_act_prod'] / ($data[$i]['real_oph'] / 24), 1);
            } else {
                $data_value[$i]['rate_gross_real'] =   round($data[$i]['real_act_prod'], 1);
            }

            $data_value[$i]['rate_gross_rkap'] =  round($data[$i]['rkap_avg_act_prod'], 1);
        }

        $data = [
            'kiln_rate'      => $data_value,
            'list_kode_plant' => $list_kode_plant
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function filter()
    {
        $opco = Opco::select(['kode_opco', 'nama_opco'])->where('kode_opco', '!=', 'SI2000')->orderBy('id')->get()->toArray();
        $kilnPlant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i = 2019; $i <= $yearNow; $i++) {
            $tahun[] = [
                'tahun' => $i,
            ];
            $minus++;
        }
        $tahun = array_reverse($tahun);

        $data = [
            'opco'      => $opco,
            'kilnPlant' => $kilnPlant,
            'tahun'     => $tahun,
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function index(){
        $data = [
            'title' => 'Plant Performance Sensing',
            'breadcrumb' => [
                [
                    'title'=>'Dashboard',
                    'url'=>'/',
                ],
                [
                    'title'=>'Plant Performance Sensing',
                    'url'=>'',
                ]
            ],
        ];
        return view('PerformanceRealtime',$data);
    }
    /**
     * The stream source.
     *
     * @return \Illuminate\Http\Response
     */
    public function stream()
    {
        // $active_kiln = PlantPerformRealtime::activeKiln();
        return response()->stream(function () {
            while (true) {
                echo "event: active_kiln\n";
                // $curDate = date(DATE_ISO8601);
                // echo 'data: {"time": "' . $curDate . '"}';
                // echo "\n\n";

                $active_kiln = PlantPerformRealtime::activeKiln();
                echo 'data: {"active_kiln":' . $active_kiln . '}' . "\n\n";

                ob_flush();
                flush();

                // Break the loop if the client aborted the connection (closed the page)
                if (connection_aborted()) {
                    break;
                }
                //Delay
                usleep(100000); // 100ms
            }
        }, 200, [
            'Cache-Control' => 'no-cache',
            'Content-Type' => 'text/event-stream',
        ]);
    }



    public function realSummaryGraph(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $dataReal = DB::table('ts_realisasi_performance')->select(
            DB::raw('COALESCE(SUM(oph),0) as oph'),
            DB::raw('COALESCE(SUM(updt),0) as updt'),
            DB::raw('COALESCE(SUM(pdt),0) as pdt'),
            DB::raw('COALESCE(SUM(stop_idle),0) as stop_idle'),
            DB::raw('COALESCE(SUM(cal),0) as cal'),
        );
        $dataActiveKiln = DB::table('vw_rkap_performance_porsi')->select('kode_plant',DB::raw('SUM(oph) as oph'));
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
            $dataActiveKiln = $dataActiveKiln -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
            $dataActiveKiln = $dataActiveKiln -> where('kode_plant', $request->plant);
        }
        $dataReal = $dataReal->whereBetween('tanggal', [$start, $now]);
        $dataReal = $dataReal->first();

        $dataActiveKiln = $dataActiveKiln->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])->where('tahun', $now_years);
        $dataActiveKiln = $dataActiveKiln->groupBy('kode_plant')->get();

        // $real_summary_graph = DB::select("select COUNT(kode_plant) as count_plant,kode_plant,
        // sum(oph) as oph,sum(updt) as updt,sum(pdt) as pdt,sum(stop_idle) as stop_idle, sum(cal) as cal
        // FROM ts_realisasi_performance WHERE   " . $filter_plant . "  and " . $filter_opco . " group by kode_plant");
        $count= 0;
        foreach ($dataActiveKiln as $key => $item){
            if($item->oph > 1){
                $count++;
            }
        }
        $calTime = $dataReal->cal;
        $total = $dataReal->oph + $dataReal->pdt + $dataReal->updt + $dataReal->stop_idle;
        $dataReal->oph = round($dataReal->oph, 1);
        $dataReal->persen_oph = $total > 0 ? round(($dataReal->oph / $total) * 100, 1): 0;
        $dataReal->pdt = round($dataReal->pdt, 1);
        $dataReal->persen_pdt = $total > 0 ? round(($dataReal->pdt / $total) * 100, 1): 0;
        $dataReal->updt = round($dataReal->updt, 1);
        $dataReal->persen_updt = $total > 0 ? round(($dataReal->updt / $total) * 100, 1): 0;
        $dataReal->stop_idle = round($dataReal->stop_idle, 1);
        $dataReal->persen_stop_idle = $total > 0 ? round(($dataReal->stop_idle / $total) * 100, 1): 0;

        return [
            'message' => 'Succes Get Data',
            'real_summary_graph' => $dataReal,
            'activeKiln'    => $count,
            'calTime'       => $calTime
        ];
    }

    public function rkapSummaryGraph(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
            DB::raw('COALESCE(SUM(oph),0) as oph'),
            DB::raw('COALESCE(SUM(updt),0) as updt'),
            DB::raw('COALESCE(SUM(pdt),0) as pdt'),
            DB::raw('COALESCE(SUM(stop_idle),0) as stop_idle'),
            DB::raw('COALESCE(SUM(cal),0) as cal'),
        );
        $dataActiveKiln = DB::table('vw_rkap_performance_porsi')->select('kode_plant', DB::raw('SUM(oph) as oph'));

        if($request->opco){
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
            $dataActiveKiln = $dataActiveKiln -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
            $dataActiveKiln = $dataActiveKiln -> where('kode_plant', $request->plant);
        }
        $dataRKAP = $dataRKAP->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])->where('tahun', $now_years);
        $dataRKAP = $dataRKAP->first();

        $dataActiveKiln = $dataActiveKiln->groupBy('kode_plant')->get();
        $count= 0;
        foreach ($dataActiveKiln as $key => $item){
            if($item->oph > 1){
                $count++;
            }
        }
        // $rkap_summary_graph = DB::select("select  COUNT(kode_plant)::int as count_plant,kode_plant,sum(oph)::int as oph,sum(updt)::int as updt,sum(pdt)::int as pdt,sum(stop_idle)::int as stop_idle, sum(cal)::int as cal FROM ts_rkap_perfomance WHERE   " . $filter_plant . " and " . $filter_opco . "  group by kode_plant");
        $calTime = $dataRKAP->cal?$dataRKAP->cal:0;
        $total = $dataRKAP->oph + $dataRKAP->pdt + $dataRKAP->updt + $dataRKAP->stop_idle;
        $dataRKAP->oph = round($dataRKAP->oph, 1);
        $dataRKAP->persen_oph = $total > 0 ? round(($dataRKAP->oph / $total) * 100, 1): 0;
        $dataRKAP->pdt = round($dataRKAP->pdt, 1);
        $dataRKAP->persen_pdt = $total > 0 ? round(($dataRKAP->pdt / $total) * 100, 1): 0;
        $dataRKAP->updt = round($dataRKAP->updt, 1);
        $dataRKAP->persen_updt = $total > 0 ? round(($dataRKAP->updt / $total) * 100, 1): 0;
        $dataRKAP->stop_idle = round($dataRKAP->stop_idle, 1);
        $dataRKAP->persen_stop_idle = $total > 0 ? round(($dataRKAP->stop_idle / $total) * 100, 1): 0;

        return [
            'message' => 'Succes Get Data',
            'rkap_summary_graph' => $dataRKAP,
            'activeKiln'    => $count,
            'calTime'       => $calTime        
        ];
    }

    public function getNettoAvailability(Request $request)
    {

        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $arr_bind = [];
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $val_opco = $request->opco;
            $filter_opco = "AND kode_opco = ?";
            $arr_bind[] = $val_opco;
        }

        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $val_plant = $request->plant;
            $filter_plant = "AND kode_plant = ?";
            $arr_bind[] = $val_plant;
        }

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $data_real = DB::select("select
        oph,
        stop_idle,
        cal
        from ts_realisasi_performance
        WHERE 1=1 " . $filter_opco . "  " . $filter_plant . " AND tanggal BETWEEN '" . $start . "' AND '" . $now . "'"
        , $arr_bind);

        $data_rkap = DB::select("select
            oph,
            stop_idle,
            oph_netto,
            cal
            from vw_rkap_performance_porsi
            WHERE 1=1 " . $filter_opco . "  " . $filter_plant . " AND bulan::numeric BETWEEN '" . 1 . "' AND '" . $now_month . "'" . "AND tahun = '" . $now_years . "'"
            , $arr_bind);

        //     DB::enableQueryLog();
        //     $data_real = DB::table('ts_realisasi_performance')->whereRaw($filter_opco)->whereRaw($filter_plant)->whereBetween('tanggal', [$start, $now])->get();
        //    dd(DB::getQueryLog());

            // $data_rkap = DB::table('vw_rkap_performance_porsi')
            //     ->whereBetween(DB::raw("bulan::numeric"), [1, $now_month])
            //     ->where('tahun', $now_years)
            //     ->whereRaw($filter_opco)
            //     ->whereRaw($filter_plant)
            //     ->get();


        $oph = 0;
        $idle = 0;
        $cal = 0;
        foreach ($data_real as $key => $item) {
            $oph = $oph + $item->oph;
            $idle = $idle + $item->stop_idle;
            $cal = $cal + $item->cal;
        }

        $rkap_op = 0;
        $rkap_idle = 0;
        $rkap_cal = 0;
        $rkap_oph_netto = 0;
        foreach ($data_rkap as $key => $value) {
            $rkap_op = $rkap_op + $value->oph;
            $rkap_idle = $rkap_idle + $value->stop_idle;
            $rkap_cal = $rkap_cal + $value->cal;
            $rkap_oph_netto = $rkap_oph_netto + $value->oph_netto;
        }

        if (($oph + $idle) > 1) {
            $atas = (($oph + $idle) / $cal) * 100;
        } else {
            $atas = $oph + $idle;
        }

        if (($rkap_oph_netto) > 1) {
            $bawah = (($rkap_oph_netto) / $rkap_cal) * 100;
        } else {
            $bawah = $rkap_oph_netto;
        }

        $selisih = $atas - $bawah;

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardNettoYield(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $dataReal = DB::table('ts_realisasi_performance')->select([
            DB::raw('COALESCE(SUM(oph),0) as oph'),
            DB::raw('COALESCE(SUM(stop_idle),0) as stop_idle'),
            DB::raw('COALESCE(SUM(act_prod),0) as act_prod'),
            DB::raw('COALESCE(SUM(act_idle_prod),0) as act_idle_prod'),
            DB::raw('COALESCE(SUM(koreksi),0) as koreksi')
        ])->groupBy('kode_plant');

        if ($request->opco) {
            $dataReal = $dataReal->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataReal = $dataReal->where('kode_plant', $request->plant);
        }

        $dataReal = $dataReal->whereBetween('tanggal', [$start, $now]);

        $dataReal = $dataReal->get();

        $tpd = 0;
        foreach ($dataReal as $key => $item) {
            if ((($item->oph + $item->stop_idle) / 24) >= 1) {
                $tpd += ($item->act_prod + $item->act_idle_prod + $item->koreksi) / (($item->oph + $item->stop_idle) / 24);
            } else {
                $tpd += $item->act_prod + $item->act_idle_prod + $item->koreksi;
            }
        }

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select([
            DB::raw('COALESCE(AVG(bdp_rate),0) as bdp_rate'),
            DB::raw('COALESCE(AVG(prod_rate),0) as prod_rate'),
            DB::raw('COALESCE(SUM(cal),0) as cal'),
        ]);
        if ($request->opco) {
            $dataRKAP = $dataRKAP->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataRKAP = $dataRKAP->where('kode_plant', $request->plant);
        }

        $dataRKAP = $dataRKAP->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])
        ->where('tahun', $now_years);

        $dataRKAPRes = $dataRKAP->get();
        $dataRealBDP = $dataRKAP->groupBy('kode_plant')->get();

        $bdp = 0;
        foreach ($dataRealBDP as $key => $value) {
            $bdp = $bdp + $value->bdp_rate;
        }

        $rkap_tpd = 0;
        $rkap_bdp = 0;
        $rkap_cal = 0;
        foreach ($dataRKAPRes as $key => $value) {
            $rkap_tpd = $rkap_tpd + round($value->prod_rate, 1);
            $rkap_bdp = $rkap_bdp + round($value->bdp_rate, 1);
            $rkap_cal = $rkap_cal + $value->cal;
        }

        if ($request->tanggal) {
            $countDay = $rkap_cal / 24; // jumlah hari dalam satu bulan; 24 = 24jam
            $firstDay = (int) substr($request->tanggal, 3, 2);
            $lastDay = (int) substr($request->tanggal, 16, 2);
            $rangeDay = $lastDay - $firstDay + 1;
            $bdp = $bdp * $rangeDay / $countDay;
            $rkap_tpd = $rkap_tpd * $rangeDay / $countDay;
        }
        if ($tpd > 1 && $bdp > 0) {
            $atas = $tpd / $bdp * 100;
        } else {
            $atas = $tpd * 100;
        }

        if ($rkap_tpd > 1 && $bdp > 0) {
            $bawah = $rkap_tpd / $rkap_bdp * 100;
        } else {
            $bawah = $rkap_tpd * 100;
        }
        $selisih = $atas - $bawah;

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardNettoOEE(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $dataReal = DB::table('ts_realisasi_performance')->select([
            DB::raw('COALESCE(SUM(oph),0) as oph'),
            DB::raw('COALESCE(SUM(stop_idle),0) as stop_idle'),
            DB::raw('COALESCE(SUM(act_prod),0) as act_prod'),
            DB::raw('COALESCE(SUM(act_idle_prod),0) as act_idle_prod'),
            DB::raw('COALESCE(SUM(koreksi),0) as koreksi'),
            DB::raw('COALESCE(SUM(cal),0) as cal')
        ]);

        if ($request->opco) {
            $dataReal = $dataReal->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataReal = $dataReal->where('kode_plant', $request->plant);
        }
        $dataReal = $dataReal->whereBetween('tanggal', [$start, $now]);
        $dataRealNai = $dataReal->get();
        $dataReal = $dataReal->groupBy('kode_plant')->get();

        $tpd = 0;
        foreach ($dataReal as $key => $item) {
            if ((($item->oph + $item->stop_idle) / 24) >= 1) {
                $tpd += ($item->act_prod + $item->act_idle_prod + $item->koreksi) / (($item->oph + $item->stop_idle) / 24);
            } else {
                $tpd += $item->act_prod + $item->act_idle_prod + $item->koreksi;
            }
        }
        $nai = 0;
        foreach ($dataRealNai as $key => $value) {
            if (($value->oph + $value->stop_idle) > 1) {
                $nai = (($value->oph + $value->stop_idle) / $value->cal) * 100;
            } else {
                $nai = ($value->oph + $value->stop_idle) * 100;
            }
        }

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select([
            DB::raw('COALESCE(AVG(bdp_rate),0) as bdp_rate'),
            DB::raw('COALESCE(AVG(prod_rate),0) as prod_rate'),
            DB::raw('COALESCE(SUM(cal),0) as cal'),
            DB::raw('COALESCE(SUM(oph),0) as oph'),
            DB::raw('COALESCE(SUM(stop_idle),0) as stop_idle'),
        ]);
        if ($request->opco) {
            $dataRKAP = $dataRKAP->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataRKAP = $dataRKAP->where('kode_plant', $request->plant);
        }
        $dataRKAP = $dataRKAP->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])
        ->where('tahun', $now_years);        
        $dataRKAPRes = $dataRKAP->get();
        $dataRealBDP = $dataRKAP->groupBy('kode_plant')->get();

        //bdp realisasi
        $bdp = 0;
        foreach ($dataRealBDP as $key => $value) {
            $bdp = $bdp + $value->bdp_rate;
        }

        $rkap_tpd = 0;
        $rkap_bdp = 0;
        $rkap_oph = 0;
        $rkap_stop_idle = 0;
        $rkap_cal = 0;
        foreach ($dataRKAPRes as $key => $value) {
            $rkap_tpd       += round($value->prod_rate, 1);
            $rkap_bdp       += round($value->bdp_rate, 1);
            $rkap_oph       += $value->oph;
            $rkap_stop_idle += $value->stop_idle;
            $rkap_cal       += $value->cal;
        }

        if ($request->tanggal) {
            $countDay = $rkap_cal / 24; // jumlah hari dalam satu bulan; 24 = 24jam
            $firstDay = (int) substr($request->tanggal, 3, 2);
            $lastDay = (int) substr($request->tanggal, 16, 2);
            $rangeDay = $lastDay - $firstDay + 1;
            $rkap_oph = $rkap_oph * $rangeDay / $countDay;
            $rkap_stop_idle = $rkap_stop_idle * $rangeDay / $countDay;
            $rkap_cal = $rkap_cal * $rangeDay / $countDay;
            $rkap_bdp = $rkap_bdp * $rangeDay / $countDay;
            $rkap_tpd = $rkap_tpd * $rangeDay / $countDay;
        }

        //realisasi
        if ($tpd > 1 && $bdp > 0) {
            $yield = ($tpd / $bdp) * 100;
        } else {
            $yield = $tpd * 100;
        }

        //RKAP
        if ($rkap_tpd > 1 && $rkap_bdp > 0) {
            $rkap_yield = ($rkap_tpd / $rkap_bdp) * 100;
        } else {
            $rkap_yield = $rkap_tpd * 100;
        }

        if (($rkap_oph + $rkap_stop_idle) > 1) {
            $rkap_nai = (($rkap_oph + $rkap_stop_idle) / $rkap_cal) * 100;
        } else {
            $rkap_nai = ($rkap_oph + $rkap_stop_idle) * 100;
        }

        $atas = $yield * $nai  / 100;
        $bawah = $rkap_yield * $rkap_nai / 100;
        $selisih = $atas - $bawah;

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardMTBF(Request $request)
    {

        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $arr_bind = [];
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $val_opco = $request->opco;
            $filter_opco = "AND kode_opco = ?";
            $arr_bind[] = $val_opco;
        }

        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $val_plant = $request->plant;
            $filter_plant = "AND kode_plant = ?";
            $arr_bind[] = $val_plant;
        }

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $data = DB::select("select
            frek_updt,
            oph
            from ts_realisasi_performance
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND tanggal BETWEEN '" . $start . "' AND '" . $now . "'"
            , $arr_bind);

        $data_performance = DB::select("select
            frek_updt,
            oph
            from vw_rkap_performance_porsi
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND bulan::numeric BETWEEN '" . 1 . "' AND '" . $now_month . "'" . "AND tahun = '" . $now_years . "'"
            , $arr_bind);

        // $data = DB::table('ts_realisasi_performance')
        //     ->whereBetween('tanggal', [$start, $now])
        //     ->whereRaw($filter_plant)
        //     ->whereRaw($filter_opco)
        //     ->get();

        // $data_performance = DB::table('vw_rkap_performance_porsi')
        //     ->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])
        //     ->where('tahun', $now_years)
        //     ->whereRaw($filter_plant)
        //     ->whereRaw($filter_opco)
        //     ->get();


        $frek_updt = 0;
        $oph = 0;
        foreach ($data as $key => $item) {
            $frek_updt = $frek_updt + $item->frek_updt;
            $oph = $oph + $item->oph;
        }

        $rkap_frek_updt = 0;
        $rkap_oph = 0;
        foreach ($data_performance as $key => $item_perform) {
            $rkap_frek_updt = $rkap_frek_updt + $item_perform->frek_updt;
            $rkap_oph = $rkap_oph + $item_perform->oph;
        }

        if ($frek_updt > 1) {
            $atas = $oph / $frek_updt;
        } else {
            $atas = $oph;
        }

        if ($rkap_frek_updt > 1) {
            $bawah = $rkap_oph / $rkap_frek_updt;
        } else {
            $bawah = $rkap_oph;
        }

        if ($atas > 0 && $bawah > 0) {
            $selisih = (($atas - $bawah) / $bawah) * 100;
        } else {
            $selisih = 0;
        }

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardMTTR(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $arr_bind = [];
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $val_opco = $request->opco;
            $filter_opco = "AND kode_opco = ?";
            $arr_bind[] = $val_opco;
        }

        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $val_plant = $request->plant;
            $filter_plant = "AND kode_plant = ?";
            $arr_bind[] = $val_plant;
        }

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $data = DB::select("select
            frek_updt,
            updt
            from ts_realisasi_performance
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND tanggal BETWEEN '" . $start . "' AND '" . $now . "'"
            , $arr_bind);

        $data_performance = DB::select("select
            frek_updt,
            updt
            from vw_rkap_performance_porsi
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND bulan::numeric BETWEEN '" . 1 . "' AND '" . $now_month . "'" . "AND tahun = '" . $now_years . "'"
            , $arr_bind);

        // $data = DB::table('ts_realisasi_performance')
        //     ->whereBetween('tanggal', [$start, $now])
        //     ->whereRaw($filter_plant)
        //     ->whereRaw($filter_opco)
        //     ->get();

        // $data_performance = DB::table('vw_rkap_performance_porsi')
        //     ->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])
        //     ->where('tahun', $now_years)
        //     ->whereRaw($filter_plant)
        //     ->whereRaw($filter_opco)
        //     ->get();

        $frek_updt = 0;
        $updt = 0;
        foreach ($data as $key => $item) {
            $frek_updt = $frek_updt + $item->frek_updt;
            $updt = $updt + $item->updt;
        }

        $rkap_frek_updt = 0;
        $rkap_updt = 0;
        foreach ($data_performance as $key => $item_perform) {
            $rkap_frek_updt = $rkap_frek_updt + $item_perform->frek_updt;
            $rkap_updt = $rkap_updt + $item_perform->updt;
        }

        if ($frek_updt > 1) {
            $atas = $updt / $frek_updt;
        } else {
            $atas = $updt;
        }

        if ($rkap_frek_updt > 1) {
            $bawah = $rkap_updt / $rkap_frek_updt;
        } else {
            $bawah = $rkap_updt;
        }

        if ($atas > 0 && $bawah > 0) {
            $selisih = (($bawah - $atas) / $bawah) * 100;
        } else {
            $selisih = 0;
        }

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardRatioUpdt(Request $request)
    {

        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $arr_bind = [];
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $val_opco = $request->opco;
            $filter_opco = "AND kode_opco = ?";
            $arr_bind[] = $val_opco;
        }

        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $val_plant = $request->plant;
            $filter_plant = "AND kode_plant = ?";
            $arr_bind[] = $val_plant;
        }

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $data = DB::select("select
            updt,
            oph,
            stop_idle
            from ts_realisasi_performance
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND tanggal BETWEEN '" . $start . "' AND '" . $now . "'"
            , $arr_bind);

        $data_performance = DB::select("select
            updt,
            oph,
            stop_idle
            from vw_rkap_performance_porsi
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND bulan::numeric BETWEEN '" . 1 . "' AND '" . $now_month . "'" . "AND tahun = '" . $now_years . "'"
            , $arr_bind);

        // $data = DB::table('ts_realisasi_performance')
        //     ->whereBetween('tanggal', [$start, $now])
        //     ->whereRaw($filter_opco)
        //     ->whereRaw($filter_plant)
        //     ->get();

        // $data_performance = DB::table('vw_rkap_performance_porsi')
        //     ->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])
        //     ->where('tahun', $now_years)
        //     ->whereRaw($filter_opco)
        //     ->whereRaw($filter_plant)
        //     ->get();


        $updt = 0;
        $oph = 0;
        $idle = 0;
        foreach ($data as $key => $item) {
            $updt = $updt + $item->updt;
            $oph = $oph + $item->oph;
            $idle = $idle + $item->stop_idle;
        }

        $rkap_updt = 0;
        $rkap_oph = 0;
        $rkap_idle = 0;
        foreach ($data_performance as $key => $item_perform) {
            $rkap_updt = $rkap_updt + $item_perform->updt;
            $rkap_oph = $rkap_oph + $item_perform->oph;
            $rkap_idle = $rkap_idle + $item_perform->stop_idle;
        }
        if(($oph + $idle) < 1){
            $atas = $updt * 100;

        }else{
            $atas = $updt / ($oph + $idle) * 100;
        }
        if(($rkap_oph + $rkap_idle) < 1){
            $bawah = $rkap_updt * 100;

        }else{
            $bawah = $rkap_updt / ($rkap_oph + $rkap_idle) * 100;
        }
        if ($bawah > 0) {
            $selisih = (($bawah - $atas)/$bawah) * 100;
        }
        else{
            $selisih = $bawah;
        }

        return [
            'message' => 'Succes',
            'data' => sprintf("%.1f",$atas).'#'.sprintf("%.1f",$bawah).'#'.sprintf("%.1f",$selisih)
        ];
    }

    public function cardActiveKiln(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);
        // DB::enableQueryLog();
        // $dataReal = DB::table(DB::raw('(select kode_plant,max(tanggal) as tanggal from t_opc_tis WHERE status=1 and (
        // to_char("t_opc_tis"."tanggal",\'YYYY-MM-DD HH24:MI:SS\') >= \''.$now.'\' or to_char("t_opc_tis"."updated_at",\'YYYY-MM-DD HH24:MI:SS\') >= \''.$now.'\') group by kode_plant ) tot1'))->select(DB::raw("coalesce(sum(tot.status),0) as countplantactive"))
        // ->leftJoin(DB::raw(' (select kode_plant,tanggal,status from t_opc_tis WHERE status=1 and (
            // to_char("t_opc_tis"."tanggal",\'YYYY-MM-DD HH24:MI:SS\') >= \''.$now.'\' or to_char("t_opc_tis"."updated_at",\'YYYY-MM-DD HH24:MI:SS\') >= \''.$now.'\')) tot '), function($join)
            // {
                //     $join->on('tot.kode_plant', '=', 'tot1.kode_plant');
        //     $join->on('tot.tanggal', '=', 'tot1.tanggal');
        // })->leftJoin(DB::raw('m_kiln_plant mp'),'mp.kode_plant','=','tot1.kode_plant');
        $dataReal = DB::table('t_opc_tis')->select('t_opc_tis.kode_plant','m_kiln_plant.kode_opco','t_opc_tis.tanggal','t_opc_tis.status')
        ->leftJoin('m_kiln_plant','t_opc_tis.kode_plant','=','m_kiln_plant.kode_plant')
        ->where( function($query){
            $now = Carbon::now()->format('Y-m-d 00:00:00');
            return $query
            ->where(DB::raw("to_char(t_opc_tis.tanggal,'YYYY-MM-DD HH24:MI:SS')"),'>=',$now)
            ->orWhere(DB::raw("to_char(t_opc_tis.updated_at,'YYYY-MM-DD HH24:MI:SS')"),'>=',$now);
        });
        if ($request->opco) {
            $dataReal = $dataReal->where('m_kiln_plant.kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataReal = $dataReal->where('t_opc_tis.kode_plant', $request->plant);
        }
        $dataReal = $dataReal->orderBy('t_opc_tis.tanggal')->get();
        $data =[];
        foreach ($dataReal as $key => $value) {
            $data[$value->kode_plant] = $value->status;
        }
        $count = 0;
        foreach ($data as $key => $value) {
            if($value === '1'){
                $count += $value;
            }
        }
    //    dd(DB::getQueryLog());
        return [
            'message' => 'Succes',
            'data' => $count
        ];
    }

    public function cardFrekUpdt(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $arr_bind = [];
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $val_opco = $request->opco;
            $filter_opco = "AND kode_opco = ?";
            $arr_bind[] = $val_opco;
        }

        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $val_plant = $request->plant;
            $filter_plant = "AND kode_plant = ?";
            $arr_bind[] = $val_plant;
        }

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $start_month = Carbon::now()->startOfYear()->format('n');
        $now_month = Carbon::now()->format('n');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $data = DB::select("select
            frek_updt
            from ts_realisasi_performance
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND tanggal BETWEEN '" . $start . "' AND '" . $now . "'"
            , $arr_bind);

        $dataRKAP = DB::select("select
            frek_updt
            from vw_rkap_performance_porsi
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND bulan::numeric BETWEEN '" . 1 . "' AND '" . $now_month . "'" . "AND tahun = '" . $now_years . "'"
            , $arr_bind);

        // $data = DB::table('ts_realisasi_performance')
        //     ->whereBetween('tanggal', [$start, $now])
        //     ->whereRaw($filter_plant)
        //     ->whereRaw($filter_opco)
        //     ->get();

        // $dataRKAP = DB::table('vw_rkap_performance_porsi')
        //     ->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])
        //     ->where('tahun', $now_years)
        //     ->whereRaw($filter_plant)
        //     ->whereRaw($filter_opco)
        //     ->get();

        $frek_updt = 0;
        foreach ($data as $key => $item) {
            $frek_updt = $frek_updt + $item->frek_updt;
        }

        $rkap_frek_updt = 0;
        foreach ($dataRKAP as $key => $item) {
            $rkap_frek_updt = $rkap_frek_updt + $item->frek_updt;
        }

        if($rkap_frek_updt > 0){
            $selisih = $rkap_frek_updt - $frek_updt;
        }else{
            $selisih = $rkap_frek_updt;
        }

        return [
            'message' => 'Succes',
            'data' => $frek_updt.'#'.$rkap_frek_updt.'#'.$selisih
        ];
    }

    public function lineRealTimeChart(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);
        $nowMonth = Carbon::now()->format('Y-m');

        $kilnrate = DB::table('t_kiln_rate')
            ->select('t_kiln_rate.tanggal','kiln_rate','t_kiln_rate.kode_plant','factorial')
            ->leftjoin('m_kiln_plant','t_kiln_rate.kode_plant',"=","m_kiln_plant.kode_plant")
            ->where('t_kiln_rate.kode_plant', $request->plant)
            ->where(DB::raw("to_char(tanggal,'YYYY-MM')"),$nowMonth)
            ->orderBy('tanggal', 'DESC')
            ->get();

        $kilnrate = $kilnrate->sortBy('tanggal');
        $dt = "Time, Kiln Rate" . "\n";
        date_default_timezone_set('UTC');
        $datanew = [];
        foreach ($kilnrate as $k => $v) {
            if($v->factorial > 0){
                $v->kiln_rate = (($v->kiln_rate/$v->factorial) * 24);
            }
            else{
                $v->kiln_rate = (($v->kiln_rate) * 24);
            }
            $date = date_create($v->tanggal);
            $date_epoch = date_format($date, 'U')*1000;
            $datanew[] = [
                    $date_epoch, $v->kiln_rate
                ];
            // $dt .= $date_epoch . ", " . $v->kiln_rate . "\n";
        }
        return response()->json($datanew, 200, [], JSON_PRETTY_PRINT);
        // return [
        //     'message' => 'Succes',
        //     'data' => $kilnrate
        // ];
    }

    public function kilnRateRealtime(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);
        $arr_bind = [];
        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $filter_plant = " AND kr.kode_plant = ?";
            $arr_bind[] = $request->plant;
        }
        
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $filter_opco = " AND kode_opco = ?";
            $arr_bind[] = $request->opco;
        }
        // DB::enableQueryLog();
        $now = Carbon::now()->format('Y-m-d H:i:00');
        $nowMonth = Carbon::now()->format('Y-m');
        $data = DB::select('SELECT
        tanggal,
           m_kiln_plant.kode_plant
       FROM
           m_kiln_plant
           LEFT JOIN (select max( tanggal ) AS tanggal,kode_plant from t_kiln_rate where to_char( tanggal, \'YYYY-MM\' ) >= \''.$nowMonth.'\'  GROUP BY
           kode_plant) kr ON kr.kode_plant = m_kiln_plant.kode_plant
        where  1=1 ' . $filter_plant . ' ' . $filter_opco . '  	ORDER BY no_pbi
        ',$arr_bind);
        $lastUpdate = collect($data)->pluck('tanggal')->sortByDesc(function($col) {
            return $col;
        })->first();
        // dd(DB::getQueryLog());
        $result = [];
        if(count($data)==0){
            $no_pbi = DB::table('m_kiln_plant')->select(
                'kode_plant',
                'no_pbi'
            );
            $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
                ->get();
            foreach($no_pbi as $comp){
                $result[] = array(
                    'id'            => 0,
                    'kode_plant'    => $comp->kode_plant,
                    'tanggal'       => $now,
                    'kiln_rate'     => 0
                );
            }
        }
        $activeKiln = DB::table('vw_active_kiln')->select('status','kode_plant')->get()->pluck('status', 'kode_plant')->toArray();
        foreach ($data as $key => $item) {
            if($item->tanggal){
                $get = KilnRate::where('t_kiln_rate.kode_plant', $item->kode_plant)
                ->leftjoin('m_kiln_plant','t_kiln_rate.kode_plant',"=","m_kiln_plant.kode_plant")
                ->select('t_kiln_rate.tanggal','kiln_rate','t_kiln_rate.kode_plant','factorial')
                ->where('t_kiln_rate.tanggal', $item->tanggal)
                ->first();
                $kilnRate = 0;
                if($get->factorial > 0){
                    $kilnRate = ($get->kiln_rate/$get->factorial) * 24;
                }
                $kilnRate = $kilnRate*(array_key_exists($item->kode_plant, $activeKiln)?$activeKiln[$item->kode_plant]:0);

                $result[] = array(
                    'id'            => $get->id_kiln_rate,
                    'kode_plant'    => $item->kode_plant,
                    'tanggal'       => $item->tanggal,
                    'kiln_rate'     => $get->kiln_rate<0 ? 0: round($kilnRate,1) 
                );
            }else{
                $result[] = array(
                    'id'            => 0,
                    'kode_plant'    => $item->kode_plant,
                    'tanggal'       => $now,
                    'kiln_rate'     => 0
                );
            }

        }


        return [
            'message' => 'Succes',
            'data' => $result,
            'last_update' => $lastUpdate
        ];
    }

    public function getPlantEvent(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $data = DB::table('vw_plant_event')->select('m_kiln_plant.kode_opco','vw_plant_event.kode_plant', 'tanggal_mulai', 'jenis_downtime', 'alasan', 'spent')
        ->whereRaw("(jenis_downtime is null or jenis_downtime != 'FY STOP' and tanggal_selesai is null)")
        ->where(DB::raw("to_char(tanggal_mulai,'YYYY')"),date('Y'))
        ->join('m_kiln_plant','m_kiln_plant.kode_plant','=','vw_plant_event.kode_plant');
        if($request->opco){
            $data = $data -> where('m_kiln_plant.kode_opco', $request->opco);
        }
        if($request->plant){
            $data = $data -> where('vw_plant_event.kode_plant', $request->plant);
        }
        $data = $data->orderBy('tanggal_mulai','DESC')->get();
        
        $result = [];
        foreach ($data as $key => $item) {
            $result[] = array(
                'kode_plant'        => $item->kode_plant,
                'tanggal_mulai'     => $item->tanggal_mulai,
                'kategori'          => $item->jenis_downtime == NULL ? '':$item->jenis_downtime,
                'deskripsi'         => $item->alasan == NULL ? '':$item->alasan,
                'spent'             => round($item->spent,1),
                'time'              => date('H:i', strtotime($item->tanggal_mulai))
            );
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function rateSummaryGraph(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        // if ($request->plant == "empty" || $request->plant == "") {
        //     $filter_plant = '1=1';
        // } else {

        //     $filter_plant = "kode_plant = '" . $request->plant . "'";
        // }


        // if ($request->opco == "empty" || $request->opco == "") {
        //     $filter_opco = '1=1';
        // } else {
        //     $filter_opco = "kode_opco = '" . $request->opco . "'";
        // }

        // $data = DB::table('ts_realisasi_performance')
        //     ->whereRaw($filter_plant)
        //     ->whereRaw($filter_opco)
        //     ->whereBetween('tanggal', [$start, $now])
        //     ->get();

        // $dataRKAP = DB::table('ts_rkap_perfomance')
        //     ->whereRaw($filter_plant)
        //     ->whereRaw($filter_opco)
        //     ->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])
        //     ->where('tahun', $now_years)
        //     ->select(DB::raw('AVG(prod_rate) as rkap_ton_day'))
        //     ->get();
        $dataReal = DB::table('ts_realisasi_performance')->select('kode_plant',
            DB::raw('COALESCE(SUM(oph),0)  as oph'),
            DB::raw('COALESCE(SUM(stop_idle),0)  as stop_idle'),
            DB::raw('COALESCE(SUM(act_prod),0)  as act_prod'),
            DB::raw('COALESCE(SUM(act_idle_prod),0)  as act_idle_prod'),
            DB::raw('COALESCE(SUM(koreksi),0)  as koreksi'),
        );
        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select('kode_plant',
            DB::raw('COALESCE(AVG(prod_rate),0) as rkap_ton_day'),
        );
        if($request->opco){
            $dataReal = $dataReal -> where('kode_opco', $request->opco);
            $dataRKAP = $dataRKAP -> where('kode_opco', $request->opco);
        }
        if($request->plant){
            $dataReal = $dataReal -> where('kode_plant', $request->plant);
            $dataRKAP = $dataRKAP -> where('kode_plant', $request->plant);
        }

        $dataReal = $dataReal->whereBetween('tanggal', [$start, $now]);
        $dataReal = $dataReal->groupBy('kode_plant')->get();
        $dataRKAP = $dataRKAP->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])->where('tahun', $now_years);
        $dataRKAP = $dataRKAP->groupBy('kode_plant')->get();

        $gross_real = 0;
        foreach ($dataReal as $key => $item) {
            if(($item->oph / 24) >=1){
                $gross_real += $item->act_prod / ($item->oph / 24);
            }else{
                $gross_real += $item->act_prod;
            }
        }

        $gross_rkap = 0;
        foreach ($dataRKAP as $key => $value) {
            $gross_rkap += $value->rkap_ton_day;
        }

        $netto_real = 0;
        foreach ($dataReal as $key => $item) {
            if((($item->oph + $item->stop_idle) / 24) >=1){
                $netto_real += ($item->act_prod + $item->act_idle_prod + $item->koreksi) / (($item->oph + $item->stop_idle) / 24);
            }else{
                $netto_real += $item->act_prod + $item->act_idle_prod + $item->koreksi;
            }
        }
        $act_rate_result = $gross_real;
        $rate_updt_result = ($gross_rkap - $netto_real) > 0 ? ($gross_rkap - $netto_real):0;
        $rate_idle_result = ($netto_real - $gross_real) > 0 ? ($netto_real - $gross_real):0;
        $total = $act_rate_result + $rate_updt_result + $rate_idle_result;
        $act_rate = $total>0?$act_rate_result / $total * 100:0;
        $rate_updt =  $total>0?$rate_updt_result / $total * 100:0;
        $rate_idle =  $total>0?$rate_idle_result / $total * 100:0;

        $rate_rkap = round($gross_rkap,1);
        return [
            'message'   => 'Succes',
            'data'      => round($total, 1) . '#' . round($act_rate, 2) . '#' . round($rate_updt, 2) . '#' . round($rate_idle, 2)
                            . '#' . round($act_rate_result, 1). '#' . round($rate_updt_result, 1). '#' . round($rate_idle_result, 1),
            'rateRKAP'  => $rate_rkap
        ];
        // $oph = 0;
        // $idle = 0;
        // $act_prod = 0;
        // $act_idle_prod = 0;
        // $koreksi = 0;
        // foreach ($data as $key => $item) {
        //     $oph = $oph + $item->oph;
        //     $idle = $idle + $item->stop_idle;
        //     $act_prod = $act_prod + $item->act_prod;
        //     $act_idle_prod = $act_idle_prod + $item->act_idle_prod;
        //     $koreksi = $koreksi + $item->koreksi;
        // }

        // if (($oph / 24) >= 1) {
        //     $gross_real = $act_prod / ($oph / 24);
        // } else {
        //     $gross_real = $act_prod;
        // }

        // if ((($oph + $idle) / 24) >= 1) {
        //     $netto_real = ($act_prod + $act_idle_prod + $koreksi) / (($oph + $idle) / 24);
        // } else {
        //     $netto_real = $act_prod + $act_idle_prod + $koreksi;
        // }

        // $act_rate_result = $gross_real;
        // $rate_gros_rkap = 0;
        // if ($dataRKAP[0]->rkap_ton_day)
        //     $rate_gros_rkap = $dataRKAP[0]->rkap_ton_day;

        // if (($rate_gros_rkap - $netto_real) > 0) {
        //     $rate_updt_result = $rate_gros_rkap - $netto_real;
        // } else {
        //     $rate_updt_result = 0;
        // }

        // $rate_idle_result = $netto_real - $gross_real;

        // $total = $act_rate_result + $rate_updt_result + $rate_idle_result;

        // $act_rate = (round($act_rate_result, 1) / round($total, 1)) * 100;
        // $rate_updt = (round($rate_updt_result, 1) / round($total, 1)) * 100;
        // $rate_idle = (round($rate_idle_result, 1) / round($total, 1)) * 100;

        // $rate_rkap = round($rate_gros_rkap,1);
        // return [
        //     'message'   => 'Succes',
        //     'data'      => round($total, 1) . '#' . round($act_rate, 2) . '#' . round($rate_updt, 2) . '#' . round($rate_idle, 2)
        //         . '#' . round($act_rate_result, 1) . '#' . round($rate_updt_result, 1) . '#' . round($rate_idle_result, 1),
        //     'rateRKAP'  =>$rate_rkap
        // ];
    }


    public function KilnOperationDetailsNai(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $no_pbi = DB::table('m_kiln_plant')->select(
            'kode_plant',
            'no_pbi'
        );
        if ($request->opco) {
            $no_pbi = $no_pbi->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $no_pbi = $no_pbi->where('kode_plant', $request->plant);
        }
        $no_pbi = $no_pbi->orderBy('no_pbi', 'ASC')
            ->get();


        $dataReal = DB::table('ts_realisasi_performance')->select(
            'kode_plant as kode_plant',
            'kode_opco as kode_opco',
            DB::raw('SUM(oph) as oph'),
            DB::raw('SUM(stop_idle) as stop_idle'),
            DB::raw('SUM(cal) as cal'),
        );

        if ($request->opco) {
            $dataReal = $dataReal->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataReal = $dataReal->where('kode_plant', $request->plant);
        }

        $dataReal = $dataReal->whereBetween('tanggal', [$start, $now]);

        $dataReal = $dataReal->orderBy('kode_plant', 'ASC')
            ->groupBy('kode_opco')
            ->groupBy('kode_plant')
            ->get();

        $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
            'kode_plant as kode_plant',
            'kode_opco as kode_opco',
            DB::raw('SUM(oph) as oph'),
            DB::raw('SUM(stop_idle) as stop_idle'),
            DB::raw('SUM(cal) as cal'),
        );
        if ($request->opco) {
            $dataRKAP = $dataRKAP->where('kode_opco', $request->opco);
        }
        if ($request->plant) {
            $dataRKAP = $dataRKAP->where('kode_plant', $request->plant);
        }

        $dataRKAP = $dataRKAP->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])->where('tahun', $now_years);

        $dataRKAP = $dataRKAP->orderBy('kode_plant', 'ASC')
            ->groupBy('kode_opco')
            ->groupBy('kode_plant')
            ->get();

        $data = [];
        foreach ($dataReal as $k => $v) {
            $data[$v->kode_plant]['real_oph'] = $v->oph;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_cal'] = $v->cal;
        }

        foreach ($dataRKAP as $k => $v) {
            $data[$v->kode_plant]['rkap_oph'] = $v->oph;
            $data[$v->kode_plant]['rkap_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['rkap_cal'] = $v->cal;
        }

        $dtSort = [];
        foreach ($no_pbi as $k => $v) {
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant, $data) ? $data[$v->kode_plant] : [];
        }

        $data = NULL;
        foreach ($dtSort as $k => $v) {
            $list_kode_plant[] = $k;
            $data[$k]['real_oph'] = array_key_exists('real_oph', $v) ? $v['real_oph'] : 0;
            $data[$k]['real_stop_idle'] = array_key_exists('real_stop_idle', $v) ? $v['real_stop_idle'] : 0;
            $data[$k]['real_cal'] = array_key_exists('real_cal', $v) ? $v['real_cal'] : 0;
            $data[$k]['rkap_oph'] = array_key_exists('rkap_oph', $v) ? $v['rkap_oph'] : 0;
            $data[$k]['rkap_stop_idle'] = array_key_exists('rkap_stop_idle', $v) ? $v['rkap_stop_idle'] : 0;
            $data[$k]['rkap_cal'] = array_key_exists('rkap_cal', $v) ? $v['rkap_cal'] : 0;
        }

        if ($request->tanggal) {
            foreach ($data as $key => $value) {
                $countDay = $data[$key]['rkap_cal'] / 24; // jumlah hari dalam satu bulan; 24 = 24jam
                $firstDay = (int) substr($request->tanggal, 3, 2);
                $lastDay = (int) substr($request->tanggal, 16, 2);
                $rangeDay = $lastDay - $firstDay + 1;
                if ($countDay != 0) {
                    $data[$key]['rkap_oph'] = $data[$key]['rkap_oph'] * $rangeDay / $countDay;
                    $data[$key]['rkap_stop_idle'] = $data[$key]['rkap_stop_idle'] * $rangeDay / $countDay;
                    $data[$key]['rkap_cal'] = $data[$key]['rkap_cal'] * $rangeDay / $countDay;
                }
            }
        }

        foreach ($data as $key => $value) {
            if (($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) > 1) {
                $data[$key]['nai_real'] = round((($data[$key]['real_oph'] + $data[$key]['real_stop_idle']) / $data[$key]['real_cal']) * 100, 1);
            } else {
                $data[$key]['nai_real'] = round($data[$key]['real_oph'] + $data[$key]['real_stop_idle'], 1);
            }
        }

        foreach ($data as $key => $value) {
            if (($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle']) > 1 && $data[$key]['rkap_cal'] > 0) {
                $data[$key]['nai_rkap'] = round((($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle']) / $data[$key]['rkap_cal']) * 100, 1);
            } else {
                $data[$key]['nai_rkap'] = round($data[$key]['rkap_oph'] + $data[$key]['rkap_stop_idle'], 1);
            }
        }

        return [
            'message' => 'Succes',
            'data_nai' => $data,
            'list_kode_plant' => $list_kode_plant
        ];
    }

    public function FrekUpdtChart(Request $request)
    {
        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $arr_bind = [];
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $val_opco = $request->opco;
            $filter_opco = "AND kode_opco = ?";
            $arr_bind[] = $val_opco;
        }

        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $val_plant = $request->plant;
            $filter_plant = "AND kode_plant = ?";
            $arr_bind[] = $val_plant;
        }

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('m');
        $now_years = Carbon::now()->format('Y');

        $no_pbi = DB::select("select
            kode_plant,
            no_pbi
            from m_kiln_plant
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " ORDER BY no_pbi ASC"
            , $arr_bind);

        $dataReal = DB::select("select
            kode_plant as kode_plant,
            kode_opco as kode_opco,
            SUM(frek_updt) as frek_updt
            from ts_realisasi_performance
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND tanggal BETWEEN '" . $start . "' AND '" . $now . "' group by kode_opco, kode_plant ORDER BY kode_plant ASC"
            , $arr_bind);

        $dataRKAP = DB::select("select
            kode_plant as kode_plant,
            kode_opco as kode_opco,
            SUM(frek_updt) as frek_updt
            from vw_rkap_performance_porsi
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND bulan::numeric BETWEEN '" . 1 . "' AND '" . $now_month . "'" . "AND tahun = '" .  $now_years . "'
            group by kode_opco, kode_plant ORDER BY kode_plant ASC"
            , $arr_bind);

        // $no_pbi = DB::table('m_kiln_plant')->select(
        //     'kode_plant',
        //     'no_pbi'
        // )
        //     ->whereRaw($filter_opco)
        //     ->whereRaw($filter_plant)
        //     ->orderBy('no_pbi', 'ASC')
        //     ->get();

        // $dataReal = DB::table('ts_realisasi_performance')->select(
        //     'kode_plant as kode_plant',
        //     'kode_opco as kode_opco',
        //     DB::raw('SUM(frek_updt) as frek_updt'),
        // )   ->whereBetween('tanggal', [$start, $now])
        //     ->whereRaw($filter_opco)
        //     ->whereRaw($filter_plant)
        //     ->orderBy('kode_plant', 'ASC')
        //     ->groupBy('kode_opco')
        //     ->groupBy('kode_plant')
        //     ->get();

        // $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
        //     'kode_plant as kode_plant',
        //     'kode_opco as kode_opco',
        //     DB::raw('SUM(frek_updt) as frek_updt'),
        // )   ->whereBetween(DB::raw('bulan::numeric'), [1, $now_month])
        //     ->where('tahun', $now_years)
        //     ->whereRaw($filter_opco)
        //     ->whereRaw($filter_plant)
        //     ->orderBy('kode_plant', 'ASC')
        //     ->groupBy('kode_opco')
        //     ->groupBy('kode_plant')
        //     ->get();
        $data = [];
        foreach ($dataReal as $k => $v) {
            $data[$v->kode_plant]['real'] = $v->frek_updt;
        }
        foreach ($dataRKAP as $k => $v) {
            $data[$v->kode_plant]['rkap'] = $v->frek_updt;
        }
        //sort by NO_PBI
        $dtSort = [];
        foreach ($no_pbi as $k => $v) {
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant, $data) ? $data[$v->kode_plant] : [];
        }
        $list_kode_plant = [];
        $rkapdata = [];
        $realdata = [];
        foreach ($dtSort as $k => $v) {
            $list_kode_plant[] =  $k;
            $rkapdata[] = array_key_exists('rkap', $v) ? (float)$v['rkap'] : 0;
            $realdata[] = array_key_exists('real', $v) ? (float)$v['real'] : 0;
        }

        return [
            'message' => 'Succes',
            'frek_rkap' => $rkapdata,
            'frek_real' => $realdata,
            'kode_plant' => $list_kode_plant
        ];
    }

    function KilnOpDetailStackChart(Request $request)
    {

        $request->validate([
            'plant' => 'nullable|exists:m_kiln_plant,kode_plant',
            'opco' => 'nullable|exists:m_opco,kode_opco',
        ]);

        $arr_bind = [];
        if ($request->opco == "empty" || $request->opco == "") {
            $filter_opco = '';
        } else {
            $val_opco = $request->opco;
            $filter_opco = "AND kode_opco = ?";
            $arr_bind[] = $val_opco;
        }

        if ($request->plant == "empty" || $request->plant == "") {
            $filter_plant = '';
        } else {
            $val_plant = $request->plant;
            $filter_plant = "AND kode_plant = ?";
            $arr_bind[] = $val_plant;
        }

        $start = Carbon::now()->startOfYear()->format('Y-m-d');
        $now = Carbon::now()->format('Y-m-d');

        $now_month = Carbon::now()->format('n');
        $now_years = Carbon::now()->format('Y');

        $no_pbi = DB::select("select
            kode_plant,
            no_pbi
            from m_kiln_plant
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " ORDER BY no_pbi ASC"
            , $arr_bind);

        $dataReal = DB::select("select
            kode_plant as kode_plant,
            kode_opco as kode_opco,
            SUM(fy_stop) as fy_stop,
            SUM(pdt) as pdt,
            SUM(updt) as updt,
            SUM(stop_idle) as stop_idle,
            SUM(oph) as oph
            from ts_realisasi_performance
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND tanggal BETWEEN '" . $start . "' AND '" . $now . "' group by kode_opco, kode_plant ORDER BY kode_plant ASC"
            , $arr_bind);

        $dataRKAP = DB::select("select
            kode_plant as kode_plant,
            kode_opco as kode_opco,
            SUM(oph) as oph
            from vw_rkap_performance_porsi
            WHERE 1=1 " . $filter_opco . " " . $filter_plant . " AND bulan::numeric BETWEEN '" . 1 . "' AND '" . $now_month . "'" . "AND tahun = '" .  $now_years . "'
            group by kode_opco, kode_plant ORDER BY kode_plant ASC"
            , $arr_bind);

        // $no_pbi = DB::table('m_kiln_plant')->select(
        //     'kode_plant',
        //     'no_pbi'
        // )
        //     ->whereRaw($filter_opco)
        //     ->whereRaw($filter_plant)
        //     ->orderBy('no_pbi', 'ASC')
        //     ->get();
        // $dataReal = DB::table('ts_realisasi_performance')->select(
        //     'kode_plant as kode_plant',
        //     'kode_opco as kode_opco',
        //     DB::raw('SUM(fy_stop) as fy_stop'),
        //     DB::raw('SUM(pdt) as pdt'),
        //     DB::raw('SUM(updt) as updt'),
        //     DB::raw('SUM(stop_idle) as stop_idle'),
        //     DB::raw('SUM(oph) as oph'),
        // )
        //     ->whereBetween('tanggal',[$start,$now])
        //     ->whereRaw($filter_opco)
        //     ->whereRaw($filter_plant)
        //     ->orderBy('kode_plant', 'ASC')
        //     ->groupBy('kode_opco')
        //     ->groupBy('kode_plant')
        //     ->get();

        // $dataRKAP = DB::table('vw_rkap_performance_porsi')->select(
        //     'kode_plant as kode_plant',
        //     'kode_opco as kode_opco',
        //     DB::raw('SUM(oph) as oph'),
        // )
        //     ->where('tahun',$now_years)
        //     ->whereRaw('bulan::numeric between 1 and ' .$now_month)
        //     ->whereRaw($filter_opco)
        //     ->whereRaw($filter_opco)
        //     ->whereRaw($filter_plant)
        //     ->orderBy('kode_plant', 'ASC')
        //     ->groupBy('kode_opco')
        //     ->groupBy('kode_plant')
        //     ->get();
        $data = [];
        foreach ($dataReal as $k => $v) {
            $data[$v->kode_plant]['real_fy_stop'] = $v->fy_stop;
            $data[$v->kode_plant]['real_pdt'] = $v->pdt;
            $data[$v->kode_plant]['real_updt'] = $v->updt;
            $data[$v->kode_plant]['real_stop_idle'] = $v->stop_idle;
            $data[$v->kode_plant]['real_oph'] = $v->oph;
        }
        foreach ($dataRKAP as $k => $v) {
            $data[$v->kode_plant]['rkap_oph'] = $v->oph;
        }

        //sort by NO_PBI
        $dtSort = [];
        foreach ($no_pbi as $k => $v) {
            $dtSort[$v->kode_plant] = array_key_exists($v->kode_plant, $data) ? $data[$v->kode_plant] : [];
        }
        $list_kode_plant = [];
        $real_fy_stop = [];
        $real_pdt = [];
        $real_updt = [];
        $real_stop_idle = [];
        $real_oph = [];
        $rkap_oph = [];
        foreach ($dtSort as $k => $v) {
            $list_kode_plant[] =  $k;
            $real_fy_stop[] = array_key_exists('real_fy_stop', $v) ? (float)round($v['real_fy_stop'], 2) : 0;
            $real_pdt[] = array_key_exists('real_pdt', $v) ? (float)round($v['real_pdt'], 2) : 0;
            $real_updt[] = array_key_exists('real_updt', $v) ? (float)round($v['real_updt'], 2) : 0;
            $real_stop_idle[] = array_key_exists('real_stop_idle', $v) ? (float)round($v['real_stop_idle'], 2) : 0;
            $real_oph[] = array_key_exists('real_oph', $v) ? (float)round($v['real_oph'], 2) : 0;
            $rkap_oph[] = array_key_exists('rkap_oph', $v) ? (float)round($v['rkap_oph'], 2) : 0;
        }


        return [
            'message' => 'Succes',
            'real_fy_stop' => $real_fy_stop,
            'real_pdt' => $real_pdt,
            'real_updt' => $real_updt,
            'real_stop_idle' => $real_stop_idle,
            'real_oph' => $real_oph,
            'rkap_oph' => $rkap_oph,
            'kode_plant' => $list_kode_plant
        ];
    }
}
