<?php

namespace App\Http\Controllers;

use App\Exports\ExportKoreksiBdp;
use Illuminate\Http\Request;
use App\Models\Opco;
use App\Models\KilnPlant;
use App\Models\KoreksiBdp;

use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportTempKoreksiBdp;
use App\Imports\ImportKoreksiBdp;

class KoreksiBdpController extends Controller
{
  /**
   * Display a listing of the resource.
   *
   * @return \Illuminate\Http\Response
   */
  public function index()
  {
    $data = [
            'title' => 'Koreksi BDP',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/koreksi-bdp',
                ],
                [
                    'title'=>'Koreksi BDP',
                    'url'=>'',
                ]
            ],
        ];
    //filter tahun
    $yearNow = now()->year;
    $tahun = [];
    $minus = 0;
    for ($i = 2019; $i <= $yearNow; $i++) {
      if ($i == $yearNow) {
        $tahun[] = [
          'tahun' => $i,
          'selected' => true
        ];
      } else {
        $tahun[] = [
          'tahun' => $i,
          'selected' => false
        ];
      }
      $minus++;
    }
    $tahun = array_reverse($tahun);
    $optTahun = "";
    foreach ($tahun as $value) {
      $tahun = $value['tahun'];
      $selected = $value['selected'];
      if ($selected == true) {
        $optTahun .= "<option value=$tahun selected>$tahun</option>";
      } else {
        $optTahun .= "<option value=$tahun>$tahun</option>";
      }
    }
    $data['tahun'] = $optTahun;

    //filter bulan
    $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
    // $bulan = DB::table('m_bulan')->select('kode_bulan', 'bln_english')->orderBy('kode_bulan')->get();
    $optBulan = "";
    for ($i = 0; $i < count($months); $i++) {
      $noBulan = strval($i + 1);
      if ($i + 1 == date('m')) {
        $optBulan .= "<option value=$noBulan selected>$months[$i]</option>";
      } else {
        $optBulan .= "<option value=$noBulan>$months[$i]</option>";
      }
    }
    $data['bulan'] = $optBulan;
    return view('koreksiBdp', $data);
  }

  public function viewImport()
  {
    $data = [
        'title' => 'Import Koreksi BDP',
        'breadcrumb' => [
            [
                'title'=>'Data Koreksi',
                'url'=>'/koreksi-bdp',
            ],
            [
                'title'=>'Koreksi Data BDP',
                'url'=>'/koreksi-bdp/view-import',
            ],
            [
                'title'=>'Import Koreksi BDP',
                'url'=>'',
            ]
        ],
    ];
    $plant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
    $opt_plant = "";
    foreach ($plant as $value) {
      $kode_plant = $value['kode_plant'];
      $name_plant = $value['name_plant'];
      $opt_plant .= "<option value='$kode_plant'>$name_plant</option>";
    }
    $data['plant'] = $opt_plant;
    return view('koreksiBdpImport', $data);
  }

  public function datatables(Request $request)
  {
    $request->validate([
      'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
      'filter_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
      'filter_tahun' => 'nullable|numeric',
    ]);
    $rkap =  DB::table('vw_tk_bdp')
      ->select([
        'no_plant',
        'kode_plant',
        'tahun',
        DB::raw("to_char(bdp_sig, '999G999G999G999G990D9') as bdp_sig"),
        DB::raw("to_char(bdp_ghopo, '999G999G999G999G990D9') as bdp_ghopo"),
        DB::raw("to_char(bdp_sg, '999G999G999G999G990D9') as bdp_sg"),
        DB::raw("to_char(bdp_sp, '999G999G999G999G990D9') as bdp_sp"),
        DB::raw("to_char(bdp_st, '999G999G999G999G990D9') as bdp_st"),
        DB::raw("to_char(bdp_sbi, '999G999G999G999G990D9') as bdp_sbi"),
        DB::raw("to_char(bdp_tlcc, '999G999G999G999G990D9') as bdp_tlcc"),
        ])
      ->orderBy('tahun', 'DESC')
      ->orderBy('no_plant', 'ASC');

    if ($request->filter_opco) {
      $rkap = $rkap -> where("kode_opco", $request->filter_opco);
    }
    if ($request->filter_tahun) {
      $rkap = $rkap -> where("tahun", $request->filter_tahun);
    }
    if ($request->filter_plant) {
      $rkap = $rkap -> where("kode_plant", $request->filter_plant);
    }
    if ($request->filter_search) {
      $filter = $request->filter_search;
      $rkap = $rkap->where('kode_opco', 'ilike', '%' . $filter . '%')
        ->orWhere("tahun", 'ilike', '%' . $filter . '%')
        ->orWhere("kode_plant", 'ilike', '%' . $filter . '%');
    }

    $data     = DataTables::of($rkap)->make(true);
    $response = $data->getData(true);

    return response()->json($response, 200, [], JSON_PRETTY_PRINT);
  }

  public function export(Request $request)
  {
    $request->validate([
      'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
      'filter_plant' => 'nullable|exists:m_kiln_plant,kode_plant',
      'filter_tahun' => 'nullable|numeric',
    ]);
    $plant = $request->filter_plant;
    $opco = $request->filter_opco;
    $tahun = $request->filter_tahun;
    $search = $request->filter_search;

    $rkap =  DB::table('vw_tk_bdp')
      ->select(['no_plant', 'kode_plant', 'tahun', 'bdp_sig', 'bdp_ghopo', 'bdp_sg', 'bdp_sp', 'bdp_st', 'bdp_sbi', 'bdp_tlcc'])
      ->orderBy('tahun', 'DESC')
      ->orderBy('no_plant', 'ASC');

    if ($opco) {
      $rkap = $rkap -> where("kode_opco", $opco);
    }
    if ($tahun) {
      $rkap = $rkap -> where("tahun", $tahun); 
    }
    if ($plant) {
      $rkap = $rkap -> where("kode_plant", $plant);
    }

    if ($search) {
      $filter = $search;
      $rkap = $rkap->where('kode_opco', 'ilike', '%' . $filter . '%')
        ->orWhere("tahun", 'ilike', '%' . $filter . '%')
        ->orWhere("kode_plant", 'ilike', '%' . $filter . '%');
    }

    return Excel::download(new ExportKoreksiBdp($rkap->get()), 'Data Koreksi BDP.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
  }

  public function import(Request $request)
  {
    // validasi
    $this->validate($request, [
      'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author
    ]);

    // menangkap file excel
    $file = $request->file('excel');

    // import data
    $import = new ImportKoreksiBdp;
    Excel::import($import, $file);
    //list opco
    $plant = KilnPlant::select('kode_plant')->get()->toArray();
    $arrPlant = [];
    foreach ($plant as $value) {
      array_push($arrPlant, $value['kode_plant']);
    }
    $datas = ($import->data);
    $result = [];

    $bdp_tonday = ['bdp_sig_tonday', 'bdp_ghopo_tonday', 'bdp_sg_tonday', 'bdp_sp_tonday', 'bdp_st_tonday', 'bdp_sbi_tonday', 'bdp_tlcc_tonday'];
    $message_bdp = ['BDP SIG (TON/DAY)', 'BDP GHOPO (TON/DAY)', 'BDP SG (TON/DAY)', 'BDP SP (TON/DAY)', 'BDP ST (TON/DAY)', 'BDP SBI (TON/DAY)', 'BDP TLCC (TON/DAY)'];

    foreach ($datas as $data) {
      $format = $data;
      $message = "";
      $status = "Valid";
      if ($data['plant'] == NULL) {
        $status = 'Invalid';
        $message = $message . "Kolom plant tidak boleh kosong ";
      } else if (gettype($data['plant']) != 'string') {
        $status = 'Invalid';
        $message = $message . "Kolom plant hanya berisi huruf ";
      } else if (!in_array($data['plant'], $arrPlant)) {
        $status = "Invalid";
        $message = $message . "Kolom plant tidak ditemukan";
      }
      if ($data['tahun'] == NULL) {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom tahun tidak boleh kosong ";
        } else {
          $message = $message . ", Kolom tahun tidak boleh kosong";
        }
      } else if (gettype($data['tahun']) != 'integer') {
        $status = 'Invalid';
        if ($message == "") {
          $message = $message . "Kolom tahun hanya berisi angka ";
        } else {
          $message = $message . ", Kolom tahun hanya berisi angka";
        }
      }

      foreach ($bdp_tonday as $key => $value) {
        if ($data[$value] == NULL) {
          $format[$value] = 0;
        } else if (!in_array(gettype($data[$value]), ['integer', 'double'])) {
          $status = 'Invalid';
          if ($message == "") {
            $message = $message . "Kolom BDP " . $message_bdp[$key] . " hanya berisi angka ";
          } else {
            $message = $message . ", Kolom BDP " . $message_bdp[$key] . " hanya berisi angka ";
          }
        }
      }

      $format['is_valid'] = $status;
      $format['note'] = $message;
      array_push($result, $format);
    }
    $data     = DataTables::of($result)->make(true);
    $response = $data->getData(true);
    return response()->json($response, 200, [], JSON_PRETTY_PRINT);
  }

  public function insertData(Request $request)
  {
    $excel = json_decode($request->excel);
    $result = [];
    $success = true;
    //list opco
    $plant = KilnPlant::select('kode_plant')->get()->toArray();
    $arrPlant = [];
    foreach ($plant as $value) {
      array_push($arrPlant, $value['kode_plant']);
    }

    $bdp_tonday = ['bdp_sig_tonday', 'bdp_ghopo_tonday', 'bdp_sg_tonday', 'bdp_sp_tonday', 'bdp_st_tonday', 'bdp_sbi_tonday', 'bdp_tlcc_tonday'];
    $message_bdp = ['BDP SIG (TON/DAY)', 'BDP GHOPO (TON/DAY)', 'BDP SG (TON/DAY)', 'BDP SP (TON/DAY)', 'BDP ST (TON/DAY)', 'BDP SBI (TON/DAY)', 'BDP TLCC (TON/DAY)'];
    // dd($excel);die;
    foreach ($excel as $key => $data) {
      $oldData = $data;
      $data['plant'] = $data[0];
      $data['tahun'] = $data[1];
      foreach ($bdp_tonday as $k_bdp => $value) {
        $data[$value] = $data[($k_bdp + 2)];
      }
      $data['is_valid'] = $data[9];
      $data = array_diff_key($data, $oldData);
      $format = $data;
      $message = "";
      $status = "Valid";
      if ($data['plant'] == NULL) {
        $status = 'Invalid';
        $success = false;
        $message = $message . "Kolom plant tidak boleh kosong ";
      } else if (gettype($data['plant']) != 'string') {
        $status = 'Invalid';
        $success = false;
        $message = $message . "Kolom plant hanya berisi huruf ";
      } else if (!in_array($data['plant'], $arrPlant)) {
        $status = "Invalid";
        $success = false;
        $message = $message . "Kolom plant tidak ditemukan";
      }
      if ($data['tahun'] == NULL) {
        $status = 'Invalid';
        $success = false;
        if ($message == "") {
          $message = $message . "Kolom tahun tidak boleh kosong ";
        } else {
          $message = $message . ", Kolom tahun tidak boleh kosong";
        }
      } else if (gettype($data['tahun']) != 'integer') {
        $status = 'Invalid';
        $success = false;
        if ($message == "") {
          $message = $message . "Kolom tahun hanya berisi angka ";
        } else {
          $message = $message . ", Kolom tahun hanya berisi angka";
        }
      }
      foreach ($bdp_tonday as $key => $value) {
        if ($data[$value] == NULL) {
          $format[$value] = 0;
        } else if (!in_array(gettype($data[$value]), ['integer', 'double'])) {
          $status = 'Invalid';
          if ($message == "") {
            $message = $message . "Kolom BDP " . $message_bdp[$key] . " hanya berisi angka ";
          } else {
            $message = $message . ", Kolom BDP " . $message_bdp[$key] . " hanya berisi angka ";
          }
        }
      }
      $format['is_valid'] = $status;
      $format['note'] = $message;
      array_push($result, $format);
    }
    $data     = DataTables::of($result)->make(true);
    $response = $data->getData(true);
    return response()->json($response, 200, [], JSON_PRETTY_PRINT);
  }

  public function saveData(Request $request)
  {
    $excel = json_decode($request->excel);

    $tahun = $excel[0][1];
    $cek_tanggal = KoreksiBdp::where("tahun",$tahun)->count();

    if ($cek_tanggal != 0) {
      KoreksiBdp::where("tahun",$tahun)->delete();
    }

    $no_pbi = KilnPlant::pluck('no_pbi','kode_plant');
    foreach ($excel as $item) {
      KoreksiBdp::create([
        'no' => array_key_exists($item[0],$no_pbi)?$no_pbi[$item[1]]:0,
        'kode_plant' => $item[0],
        'tahun' => $item[1],
        'bdp_sig' => $item[2],
        'bdp_ghopo' => $item[3],
        'bdp_sg' => $item[4],
        'bdp_sp' => $item[5],
        'bdp_st' => $item[6],
        'bdp_sbi' => $item[7],
        'bdp_tlcc' => $item[8],
      ]);
    }
    $response = responseSuccess('Data added successfully');
    return response()->json($response, 200);
  }

  public function temp(Request $request)
  {
    $request->validate([
      'filter_tahun' => 'nullable|numeric',
    ]);

    $data_koreksi =  DB::table('vw_tk_bdp')->select(
        'kode_plant',
        'tahun',
        'bdp_sig',
        'bdp_ghopo',
        'bdp_sg',
        'bdp_sp',
        'bdp_st',
        'bdp_sbi',
        'bdp_tlcc',
    );
    if($request->filter_tahun){
      $data_koreksi = $data_koreksi->where('tahun',$request->filter_tahun);
    }
    $data_koreksi = $data_koreksi->get();
    return Excel::download(new ExportTempKoreksiBdp($data_koreksi), 'Template Koreksi BDP.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
  }

  public function filter()
  {
    //filter opco
    $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();
    $kilnPlant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();

    //filter tahun
    $yearNow = now()->year;
    $tahun = [];
    $minus = 0;
    for ($i = 2019; $i <= $yearNow; $i++) {
      if ($i == $yearNow) {
        $tahun[] = [
          'tahun' => $i,
          'selected' => true
        ];
      } else {
        $tahun[] = [
          'tahun' => $i,
          'selected' => false
        ];
      }
      $minus++;
    }
    $tahun = array_reverse($tahun);

    $data = [
      'opco' => $opco,
      'kilnPlant' => $kilnPlant,
      'tahun' => $tahun
    ];
    return response()->json($data, 200, [], JSON_PRETTY_PRINT);
  }

  public function getPlant(Request $request)
  {
    $id = $request->query('id');
    if ($id) {
      $kilnPlant = KilnPlant::select(['kode_plant', 'name_plant'])->where('kode_opco', $id)->orderBy('id_kiln_plant')->get()->toArray();
      $data = ['plant' => $kilnPlant];
      return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    $kilnPlant = KilnPlant::select(['kode_plant', 'name_plant'])->orderBy('id_kiln_plant')->get()->toArray();
    $data = ['plant' => $kilnPlant];
    return response()->json($data, 200, [], JSON_PRETTY_PRINT);
  }
}
