<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNoPbiToMOpcoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('m_opco', function (Blueprint $table) {
            // Only add no_pbi if it doesn't exist
            if (!Schema::hasColumn('m_opco', 'no_pbi')) {
                $table->integer('no_pbi')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('m_opco', function (Blueprint $table) {
            if (Schema::hasColumn('m_opco', 'no_pbi')) {
                $table->dropColumn('no_pbi');
            }
        });
    }
}
