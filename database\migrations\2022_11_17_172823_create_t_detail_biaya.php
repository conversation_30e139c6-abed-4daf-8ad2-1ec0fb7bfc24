<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTDetailBiaya extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('t_detail_biaya', function (Blueprint $table) {
            $table->bigIncrements('id_detail_biaya');
            $table->string('no_dokement', 30)->unique();
            $table->string('kode_opco', 30);
            $table->string('cost_element', 30)->nullable();
            $table->string('cost_element_name', 100)->nullable();
            $table->string('cost_element_group', 30)->nullable();
            $table->string('cost_element_group_name', 100)->nullable();
            $table->date('tanggal')->nullable()->default(NULL);
            $table->float('biaya', 8, 5);
            $table->softDeletes();  
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('t_detail_biaya');
    }
}
