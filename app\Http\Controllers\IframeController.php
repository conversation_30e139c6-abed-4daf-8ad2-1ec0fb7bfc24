<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Menu;
use Auth;
use DataTables;
use Illuminate\Support\Facades\Route;   

class IframeController extends Controller
{
    public function index()
    {
        $data['menus'] = $this->getDashboardMenu();
        $collection = collect($data['menus']);
        $current = request()->path();
        $dtMenu = Menu::where('url',$current)->first();
        $data['iframe_url'] = $dtMenu['iframe_url'] ;
        $data['title'] = $dtMenu['name'] ;
        // $data['kode_opcos'] = RKAPCapex::distinct()->get(['kode_opco']);
        // $data['tahuns'] = RKAPCapex::distinct()->get(['tahun']);

        return view('iframeView', $data);      
    }

    public function store(Request $request)
    {
    }

   
  
    public function create()
    {
        //
    }
    
    public function edit($id)
    {
        //
    }

}
