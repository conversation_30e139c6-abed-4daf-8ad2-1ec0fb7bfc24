<?php

namespace App\Http\Controllers;

use App\Http\Requests\CompanyRequest;
use App\Models\Sppd;
use App\Services\SAPRFCClass;
use DataTables;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\Routes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class SppdController extends Controller
{
    public function index()
    {
        // $query = new Company();
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        // dd("hre");
        // $parenth1 = Sppd::whereNull('parenth1')->get();
        // $parenth2 = Sppd::whereNotNull('parenth1')->whereNull('parenth2')->get();
        // $data['parenth1'] = $parenth1;
        // $data['parenth2'] = $parenth2;
        // dd($data);
        return view('sppd', $data);
    }

    public function datatables(Request $request)
    {
        // $query    = Sppd::get();
        // dd($request);
        // if($request->SPPDNO !="" && $request->SPPDNO!="")
        if ($request->start_date && $request->end_date) {
            $response = (new SAPRFCClass)->getData_SPPD($request);
        }
        else{
            $response = array('message'=>'Start Date and End Date is Required.');
            return response()->json($response, 400, [], JSON_PRETTY_PRINT);
        }

        // dd($listSPPD);   
        // $data     = DataTables::of($query)->make(true);
        // $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // if ($request->file('logo')) {
        //     $file= $request->file('logo');
        //     $filename= date('YmdHi').$file->getClientOriginalName();
        //     $file-> move(public_path('assets/img/logo'), $filename);
        // }else{
        //     $filename = null;
        // }

        // $company = Sppd::create([
        //     'company' => $request->company,
        //     'description' => $request->description,
        //     'parenth1' => $request->parenth1,
        //     'parenth2' => $request->parenth2,
        //     'status' => $request->status,
        //     'short_description' => $request->short_description,
        //     'short_desc_inventory' => $request->short_desc_inventory,
        //     'dirut_name' => $request->dirut_name,
        //     'logo' => $filename,
        // ]);

        // $response = responseSuccess(trans('message.read-success'),$company);
        // return response()->json($response,200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($company)
    {

        // $query   = Sppd::find($company);
        // $response = responseSuccess(trans('message.read-success'),$query);
        // return response()->json($response,200);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        // $query   = Sppd::find($id);
        // $response = responseSuccess(trans("messages.read-success"), $query);
        // return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update($id, CompanyRequest $request)
    {
        // $company = Sppd::find($id);
        //   $data = $this->findDataWhere(Sppd::class, ['id' => $id]);
        //     if ($request->file('logo')) {
        //         $file= $request->file('logo');
        //         $filename= date('YmdHi').$file->getClientOriginalName();
        //         $file-> move(public_path('assets/img/logo'), $filename);
        //     } else {
        //         $filename = $company->logo;
        //     }

        // //   dd($data);exit();
        //   DB::beginTransaction();
        //   try {
        //       $data->update([
        //             'company' => $request->company,
        //             'description' => $request->description,
        //             'parenth1' => $request->parenth1,
        //             'parenth2' => $request->parenth2,
        //             'status' => $request->status,
        //             'short_description' => $request->short_description,
        //             'short_desc_inventory' => $request->short_desc_inventory,
        //             'dirut_name' => $request->dirut_name,
        //             'logo' => $filename,
        //       ]);
        //       DB::commit();
        //       $response = responseSuccess(trans("messages.update-success"), $data);
        //       return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        //   } catch (Exception $e) {
        //       DB::rollback();
        //       $response = responseFail(trans("messages.update-fail"), $e->getMessage());
        //       return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        //     }

    }


    public function destroy($id)
    {

        // Sppd::destroy($id);
        // $response = responseSuccess(trans('message.delete-success'));
        // return response()->json($response,200);
    }
}
