<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\MasterKuisioner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class MasterKuisionerController extends Controller
{
    //
    public function index()
    {
        $data = [
            'title' => 'Kuisioner',
            'breadcrumb' => [
                [
                    'title'=>'Master Data ',
                    'url'=>'#',
                ],
                [
                    'title'=>' Master Kuisioner',
                    'url'=>'master-kuisioner',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        return view('master.master_kuisioner', $data);
    }

     public function datatables(Request $request)
    {
        $query    = MasterKuisioner::select('uuid','kuisioner','status','urutan');
        if($request->has('kuisioner') && $request->get('kuisioner') != '') {
            $query = $query->where('kuisioner', 'ilike', '%'.$request->get('kuisioner').'%');
        }
        if($request->has('status') && $request->get('status') != '') {
            $query = $query->where('status', $request->get('status'));
        }
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);

        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function store(Request $request) {
        $attributes = $request->only(['kuisioner','urutan','status']);
        $roles = [
            'kuisioner' => 'required',
            'urutan' => 'required|unique:master_kuisioner',
            'status' => 'required|in:y,n',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
        ];
        $this->validators($attributes, $roles, $messages);
        $attributes['created_by'] = Auth()->user()->id;
        $attributes['updated_by'] = Auth()->user()->id;
        
        DB::beginTransaction();
        try {
            //code...
            $data = MasterKuisioner::create($attributes);
            DB::commit();
            $response = responseSuccess(trans("messages.create-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.create-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function show($uuid)
    {
        $attributes['uuid'] = $uuid;

        $roles = [  
            'uuid' => 'required|exists:master_kuisioner',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);
        $data     = $this->findDataWhere(MasterKuisioner::class, ['uuid' => $uuid]);
        $response = responseSuccess(trans("messages.read-success"), $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function update($uuid, Request $request) {
        $attributes = $request->only(['kuisioner','urutan','status']);
        $roles = [
            'kuisioner' => 'required',
            'urutan' => 'required|unique:master_kuisioner,urutan,'.$uuid.',uuid',
            'status' => 'required|in:y,n',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'in'   => trans('messages.in'),
        ];
        $this->validators($attributes, $roles, $messages);
        $attributes['updated_by'] = Auth()->user()->id;
        $attributes['urutan'] = intval($attributes['urutan']);
        $data = $this->findDataWhere(MasterKuisioner::class, ['uuid' => $uuid]);
        DB::beginTransaction();
        try {
            //code...
            $data->update($attributes);
            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function destroy($uuid)
    {
        $attributes['uuid'] = $uuid;
        $roles = [  
            'uuid' => 'required|exists:master_kuisioner',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);
        $data = $this->findDataWhere(MasterKuisioner::class, ['uuid' => $uuid]);

        if($data->sub_master_kuisioner()->count() > 0){
            $response = responseFail(trans("messages.delete-fail"), ['sub_master_kuisioner' => ["Data Ini masih digunakan dalam transaction"]]);
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
        
        DB::beginTransaction();
        try {
            //code...
            $data = MasterKuisioner::where('uuid', $uuid)->delete();
            DB::commit();
            $response = responseSuccess(trans("messages.delete-success"), []);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.delete-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
}
