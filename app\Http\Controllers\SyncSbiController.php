<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use App\Models\SampleSbi;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use App\Http\Controllers\Controller;
use Session;
use App\Models\KilnOutput;
use App\Models\KilnRate;
use App\Models\Tis;

use Illuminate\Support\Facades\DB;
use App\Models\RealisasiProduksi;
use Exception;


class SyncSbiController extends Controller
{
    public function index(Request $request)
    {
        $data = [
            'title' => 'DATA SBI',
            'breadcrumb' => [
                [
                    'title'=>'Sync Data',
                    'url'=>'/sync-sbi',
                ],
                [
                    'title'=>'Data SBI',
                    'url'=>'',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        return view('syncSbi', $data);
    }

    public function getDataSyncSbi(Request $request)
    {
        if ($request->filter_data_sbi_bulan == '') {
            $filter_data_sbi_bulan = '1=1';
        } else {
            $filter_data_sbi_bulan = 'bulan = ' . $request->filter_data_sbi_bulan . '';
        }
        if ($request->filter_data_sbi_tahun == '') {
            $filter_data_sbi_tahun = '1=1';
        } else {
            $filter_data_sbi_tahun = 'tahun = ' . $request->filter_data_sbi_tahun . '';
        }
        if ($request->filter_data_sbi == '') {
            $filter_data_sbi = "1=1";
        } else {
            $filter_data_sbi = "rfc = '" . $request->filter_data_sbi . "'";
        }

        $SampleSbi = SampleSbi::select(['id_rfc', 'tahun', 'bulan', 'created_at', 'status', 'updated_at', 'rfc'])
            ->whereRaw($filter_data_sbi_bulan)
            ->whereRaw($filter_data_sbi_tahun)
            ->whereRaw($filter_data_sbi);
        return DataTables::of($SampleSbi)->make();
    }

    public function getFilterRfcSbi(Request $request)
    {
        $filter_sbi = SampleSbi::select('id_rfc', 'rfc')->distinct('rfc')->get();
        return [
            'message' => 'Succes Get Data Filter Rfc',
            'data' => $filter_sbi
        ];
    }

    public function insertStatus(Request $request,$a)
    {
        // $a = '{"kode_plant":"NAR1",
        //     "tanggal": "2022-10-12 10:10:05",
        //     "status": "1",
        //     "source_system": "TIS",
        //     "create_by": "TIS_Narogong"
        // }';
        $this->secure($request, $a);

        if ($a && $a != "") {
            $data = json_decode($a, true);
            unset($data['token']);
            $res = Tis::insert($data);
            if ($res)
                return "sukses";
            else
                return "gagal";
        } else {
            return "error";
        }
    }

    public function insertKilnRate(Request $request,$a)
    {
        // $a = '{"kode_plant":"NAR1",
        //         "tanggal": "2022-10-12 10:10:05",
        //         "kiln_rate": "200",
        //         "source_system": "TIS",
        //         "create_by": "TIS_Narogong"
        //     }';
        
        $this->secure($request, $a);

        if ($a && $a != "") {
            $data = json_decode($a, true);
            unset($data['token']);
            $res = KilnRate::insert($data);
            if ($res)
                return "sukses";
            else
                return "gagal";
        } else {
            return "error";
        }
    }

    public function insertKilnOutput(Request $request, $a)
    {
        // $a = '{"kode_plant":"NAR1",
        //         "tanggal": "2022-10-12 10:10:05",
        //         "produksi_output": "7200",
        //         "source_system": "TIS",
        //         "create_by": "TIS_Narogong"
        //     }';
    
        $this->secure($request, $a);

        if ($a && $a != "") {
            $data = json_decode($a, true);
            unset($data['token']);
            $data['kode_opco'] = 'SBI';
            $res = $this->setOutpout($data);
            if ($res)
                return "sukses";
            else
                return "gagal";
        } else {
            return "error";
        }
    }

    private function secure(Request $request, $data)
    {
       try{
            if ($request->getHttpHost() != config('app.local_ip')) {
                    echo 'Access Not Allowed';
                    exit(1);
            } else {
                // $ip = request()->ip();
                    $param = json_decode($data, true);
                    $token="";
                    if(array_key_exists('token',$param))
                    $token = $param['token'];
                    else{
                        echo 'Unknown Token';
                        exit(1);
                    }

                // $ip_whitelist = explode(';', env('WHITELIST_IP'));
                    $token_whitelist = explode(';', env('TOKEN'));


                    if (!in_array($token, $token_whitelist)) {
                        echo 'Token Missmatch';
                        exit(1);
                    }

                    // if (!in_array($ip, $ip_whitelist)) {
                    //     echo 'IP Not Allowed';
                    //     exit(1);
                    // } else {
                    
                    // }
            }
        }catch(Exception $e){
            echo 'Error Was Happen';
            exit(1);
        }
    }

    private function setOutpout($data){
        try{
            $plant = $data['kode_plant'];
            $date = $data['tanggal'];
            $dateD = date_format(date_create($date),"Y-m-d");
            $dateM = date_format(date_create($date),"Y-m");
            $opco = $data['kode_opco'];
            $output = $data['produksi_output'];
            $lastKilnOutput = KilnOutput::where('kode_plant', $plant)->where(DB::raw("to_char(tanggal, 'YYYY-MM-DD')"), $dateD)
                            ->where('source_system', 'TIS')
                            ->orderBy('id_kiln_output', 'desc')
                            ->first();
            if (!$lastKilnOutput) {
                KilnOutput::create([
                    'kode_plant' => $plant,
                    'tanggal' => $date,
                    'produksi_output' => intval($output),
                    'source_system' => 'TIS',
                    'create_by' => "TIS_" . $plant,
                    'kode_opco' => $opco
                ]);
            } else {
                $lastKilnOutput->update(['produksi_output' => intval($output), 'update_by' => "TIS_" . $plant, 'kode_opco' => $opco]);
            }
            $sumKilnOutput = KilnOutput::where('kode_opco', $opco)->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $dateM)
            ->select('kode_opco', DB::raw('SUM(produksi_output) as prod_output'))
            ->groupBy('kode_opco')
            ->first();
            
            $lastRealProd = RealisasiProduksi::where('kode_opco', $opco)->where(DB::raw("to_char(tanggal, 'YYYY-MM')"), $dateM)->first();
            $prod_output = 0;
            if($sumKilnOutput)
            $prod_output = $sumKilnOutput->prod_output;
            if (!$lastRealProd) {
                RealisasiProduksi::create([
                    'tanggal' => $dateD,
                    'prod_klinker' => 0,
                    'prod_semen' => 0,
                    'clinker_sold' => 0,
                    'prod_output' => $prod_output,
                    'ics' => 0,
                    'kode_opco' => $opco
                ]);
            } else {
                $lastRealProd->update(['prod_output' => $prod_output, 'tanggal' => $dateD]);
            }
            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollBack();
            return false;
            throw new Exception($e->getMessage());
        }
    }
}
