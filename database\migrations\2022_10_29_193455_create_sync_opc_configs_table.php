<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSyncOpcConfigsTable extends Migration
{
    public function up()
    {
        if (!Schema::hasTable('sync_opc_configs')) {
            Schema::create('sync_opc_configs', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('name');
                $table->string('type');
                $table->string('url');
                $table->text('parameter');
                $table->enum('schedule', ['minutely', 'everyfiveminute', 'hourly', 'daily', 'monthly']);
                $table->integer('at_date')->nullable();
                $table->time('at_time')->nullable();
                $table->enum('status', ['active', 'non_active']);
                $table->timestamps();
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('sync_opc_configs');
    }
}
