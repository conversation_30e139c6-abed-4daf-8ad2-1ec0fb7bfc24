<?php

namespace App\Http\Controllers;

use App\Helpers\GlobalHelper;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class Tesmail extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return csrf_token();
    }
    public function Cenv(){
        echo "<pre>";
        var_dump($_ENV);
    }
    public function send($to){
        echo "test";
        $sender = 'noReply';
        $recipient = urldecode($to);

        $subject = "php mail test";
        $message = "php test message";
        $headers = 'From:' . $sender;
        echo $recipient;
        $sent = mail($recipient, $subject, $message, $headers);
        var_dump($sent); //tambahan
        if ($sent)
        {
            echo "<br> Message sent <br>";
        }
        else
        {
            echo "<br> Message error <br>";
        }
        echo "END";
    }
    public function EQ(Request $request){
        $query = $request['query'];
        $query = str_replace("''", "'", $query);
        if(str_contains($query,'SELECT')){
            echo "SELECT";
            echo "<br>$query";
            $data = DB::select(DB::raw($query));
            var_dump($data);
        }else{
            echo "non SELECT";
            echo "<br>$query";
            DB::statement($query);
        }

    }
    public function get_temp(){
        print sys_get_temp_dir();
    }
}
