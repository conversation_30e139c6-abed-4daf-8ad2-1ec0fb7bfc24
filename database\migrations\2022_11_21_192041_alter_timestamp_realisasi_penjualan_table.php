<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTimestampRealisasiPenjualanTable extends Migration
{
    public function up()
    {
        // Check if table exists before altering
        if (!Schema::hasTable('t_realisasi_penjualan')) {
            Schema::create('t_realisasi_penjualan', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('kode_opco', 30)->nullable();
                $table->date('tanggal')->nullable();
                $table->decimal('volume_penjualan', 15, 2)->nullable();
                $table->decimal('harga_jual', 15, 2)->nullable();
                $table->decimal('total_penjualan', 15, 2)->nullable();
                $table->string('created_by', 30)->nullable();
                $table->string('updated_by', 30)->nullable();
                $table->timestamps();
                $table->softDeletes();
            });
        } else {
            // Original alter logic here
            Schema::table('t_realisasi_penjualan', function (Blueprint $table) {
                // Add your timestamp alterations
            });
        }
    }

    public function down()
    {
        if (Schema::hasTable('t_realisasi_penjualan')) {
            Schema::table('t_realisasi_penjualan', function (Blueprint $table) {
                $table->dropTimestamps();
            });
        }
    }
}
