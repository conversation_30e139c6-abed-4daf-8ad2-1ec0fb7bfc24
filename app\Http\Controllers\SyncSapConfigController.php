<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\SyncSapConfig;
use App\Services\SyncSapService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class SyncSapConfigController extends Controller
{
    public function datatable(Request $request)
    {
        $query    = SyncSapConfig::get();

        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|max:255|unique:sync_sap_configs,name',
            'type' => ['required', Rule::in(array_keys(SyncSapConfig::types()))],
            'tcode' => 'required|max:255',
            'parameter' => 'required',
            'kode_opco' => 'required',
            'schedule' => ['required', Rule::in(array_keys(SyncSapConfig::schedules()))],
            'at_date' => Rule::requiredIf($request->input('schedule') == 'monthly'),
            'at_time' => Rule::requiredIf(in_array($request->input('schedule'), ['daily', 'monthly'])),
            'status' => ['required', Rule::in(array_keys(SyncSapConfig::statuses()))],
        ]);

        if ($request->at_date == '') {
            $request['at_date'] = NULL;
        }

        try {
            $model = SyncSapConfig::create($request->all());

            $response = responseSuccess(trans('messages.create-success'), $model);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function show($id)
    {
        $model = SyncSapConfig::findOrFail($id);

        $response = responseSuccess(trans('messages.read-success'), $model);
        return response()->json($response, 200);
    }

    public function update(Request $request, $id)
    {
        $model = SyncSapConfig::findOrFail($id);

        $request->validate([
            'name' => 'required|max:255|unique:sync_sap_configs,name,' . $id . ',id',
            'type' => ['required', Rule::in(array_keys(SyncSapConfig::types()))],
            'tcode' => 'required|max:255',
            'parameter' => 'required',
            'kode_opco' => 'required',
            'schedule' => ['required', Rule::in(array_keys(SyncSapConfig::schedules()))],
            'at_date' => Rule::requiredIf($request->input('schedule') == 'monthly'),
            'at_time' => Rule::requiredIf(in_array($request->input('schedule'), ['daily', 'monthly'])),
            'status' => ['required', Rule::in(array_keys(SyncSapConfig::statuses()))],
        ]);

        if ($request->at_date == '') {
            $request['at_date'] = NULL;
        }

        try {
            $model->update($request->all());
            $response = responseSuccess(trans('messages.update-success'), $model);
            return response()->json($response, 200);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.update-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function destroy($id)
    {
        $model = SyncSapConfig::findOrFail($id);

        try {
            $model->delete();
            $response = responseSuccess(trans('messages.delete-success'), $model);
            return response()->json($response, 200);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.delete-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function testSync(Request $request)
    {
        $config = (object) $request->all();
        $response = (new SyncSapService)->execute(null, null, null, $config, 'test');
        if ($response['status'] == 'fail') {
            return response()->json($response, 500);
        }
        return response()->json($response, 200);
    }
}
