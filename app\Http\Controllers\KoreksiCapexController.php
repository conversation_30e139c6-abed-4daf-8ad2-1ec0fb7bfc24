<?php

namespace App\Http\Controllers;

use App\Models\Menu;
use Illuminate\Http\Request;
use App\Models\RealPerformance;
use App\Models\KilnPlant;
use App\Models\Opco;
use App\Models\KoreksiCapex;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportKoreksiCapex;
use App\Exports\ExportDataKoreksiCapex;
use App\Exports\ExportDataKoreksiFRCapex;
use App\Imports\ImportKoreksiCapex;
use App\Models\ExchangeRate;
use Carbon\Carbon;

class KoreksiCapexController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = [
            'title' => 'Koreksi Capex',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/koreksi-capex',
                ],
                [
                    'title'=>'Koreksi Capex',
                    'url'=>'',
                ]
            ],
        ];
        return view('koreksiCapex', $data);
    }

    public function insertData(Request $request)
    {
        $excel = json_decode($request->excel);
        $result = [];
        $success = true;
        //list opco
        $opco = Opco::select('kode_opco')->get()->toArray();
        $arrOpco = [];
        foreach ($opco as $value) {
            array_push($arrOpco, $value['kode_opco']);
        }
        foreach ($excel as $data) {
            $oldData = $data;
            $data['no'] = $data[0];
            $data['kode_opco'] = $data[1];
            $data['tanggal'] = $data[2];
            $data['rkap_capex'] = $data[3];
            $data['real_capex'] = $data[4];
            $data['is_valid'] = $data[5];
            $data = array_diff_key($data, $oldData);
            $format = $data;
            $message = "";
            $status = "Valid";

            if ($data['kode_opco'] == NULL) {
                $status = 'Invalid';
                $success = false;
                $message = $message . "Kolom opco tidak boleh kosong ";
            } else if (gettype($data['kode_opco']) != 'string') {
                $status = 'Invalid';
                $success = false;
                $message = $message . "Kolom opco hanya berisi huruf ";
            } else if (!in_array($data['kode_opco'], $arrOpco)) {
                $status = "Invalid";
                $success = false;
                $message = $message . "Kolom opco tidak ditemukan";
            }


            if ($data['tanggal'] == NULL) {
                $status = 'Invalid';
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom tanggal tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom tanggal tidak boleh kosong";
                }
            }

            if ($data['rkap_capex'] == NULL) {
                $format['rkap_capex'] = 0;
            } else if (gettype($data['rkap_capex']) != 'integer'  and gettype($data['rkap_capex']) != 'float'  and gettype($data['rkap_capex']) != 'double') {
                $status = 'Invalid';
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom RKAP Capex hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom RKAP Capex hanya berisi angka";
                }
            }

            if ($data['real_capex'] == NULL) {
                $format['real_capex'] = 0;
            } else if (gettype($data['real_capex']) != 'integer' and gettype($data['real_capex']) != 'float'  and gettype($data['real_capex']) != 'double') {
                $status = 'Invalid';
                $success = false;
                if ($message == "") {
                    $message = $message . "Kolom Real Capex hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom Real Capex hanya berisi angka";
                }
            }
            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }

        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function import(Request $request)
    {
        // validasi
        $this->validate($request, [
            'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author
        ]);

        // menangkap file excel
        $file = $request->file('excel');

        // import data
        $import = new ImportKoreksiCapex;
        Excel::import($import, $file);
        //list opco
        $opco = Opco::select('kode_opco')->get()->toArray();
        $arrOpco = [];
        foreach ($opco as $value) {
            array_push($arrOpco, $value['kode_opco']);
        }
        $datas = ($import->data);

        $result = [];
        foreach ($datas as $data) {
            $format = $data;
            $message = "";
            $status = "Valid";

            if ($data['kode_opco'] == NULL) {
                $status = 'Invalid';
                $message = $message . "Kolom opco tidak boleh kosong ";
            } else if (gettype($data['kode_opco']) != 'string') {
                $status = 'Invalid';
                $message = $message . "Kolom opco hanya berisi huruf ";
            } else if (!in_array($data['kode_opco'], $arrOpco)) {
                $status = "Invalid";
                $message = $message . "Kolom opco tidak ditemukan";
            }


            if ($data['tanggal'] == NULL) {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom Tanggal tidak boleh kosong ";
                } else {
                    $message = $message . ", Kolom Tanggal tidak boleh kosong";
                }
            }


            if ($data['rkap_capex'] == NULL) {
                $format['rkap_capex'] = 0;
            } else if (gettype($data['rkap_capex']) != 'integer' and gettype($data['rkap_capex']) != 'float'  and gettype($data['rkap_capex']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom RKAP Capex hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom RKAP Capex hanya berisi angka";
                }
            }

            if ($data['real_capex'] == NULL) {
                $format['real_capex'] = 0;
            } else if (gettype($data['real_capex']) != 'integer' and gettype($data['real_capex']) != 'float'  and gettype($data['real_capex']) != 'double') {
                $status = 'Invalid';
                if ($message == "") {
                    $message = $message . "Kolom RKAP Capex hanya berisi angka ";
                } else {
                    $message = $message . ", Kolom RKAP Capex hanya berisi angka";
                }
            }

            $format['is_valid'] = $status;
            $format['note'] = $message;
            array_push($result, $format);
        }

        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);

        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function exportTemplate(Request $request)
    {
        $request->validate([
            'filter_tahun' => 'nullable|numeric',
            'filter_bulan' => 'nullable|numeric',
        ]);
        $data_koreksi =  DB::table('vw_tk_capex')->select(
            'kode_opco',
            DB::raw("TO_CHAR(tanggal::timestamp, 'DD/MM/YYYY') AS tanggal"),
            'rkap_capex',
            'real_capex');
        if($request->filter_tahun){
            $data_koreksi = $data_koreksi -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $request->filter_tahun);
        }
        if($request->filter_bulan){
            $data_koreksi = $data_koreksi -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $request->filter_bulan);
        }
        $data_koreksi = $data_koreksi->get();

        return Excel::download(new ExportKoreksiCapex($data_koreksi), 'Template Koreksi Capex.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
    }

    public function export(Request $request)
    {
        $request->validate([
            'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
        ]);

        $opco = $request->filter_opco;
        $tahun = $request->filter_tahun;
        $bulan = $request->filter_bulan;

		return Excel::download(new ExportDataKoreksiCapex($opco, $tahun, $bulan), 'Data Koreksi Capex.xlsx');
    }

    public function importKoreksiCapex()
    {
        $data = [
            'title' => 'Import Koreksi Capex',
            'breadcrumb' => [
                [
                    'title'=>'Data Koreksi',
                    'url'=>'/koreksi-capex',
                ],
                [
                    'title'=>'Koreksi Data Capex',
                    'url'=>'/import-koreksi-capex',
                ],
                [
                    'title'=>'Import Koreksi Capex',
                    'url'=>'',
                ]
            ],
        ];
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();
        $opt_opco = "";
        foreach ($opco as $value) {
            $kode_opco = $value['kode_opco'];
            $nama_opco = $value['nama_opco'];
            $opt_opco .= "<option value='$kode_opco'>$nama_opco</option>";
        }
        $data['opco'] = $opt_opco;
        return view('koreksiCapexImport', $data);
    }

    public function filter()
    {
        //filter opco
        $opco = Opco::select(['kode_opco', 'nama_opco'])->orderBy('id')->get()->toArray();

        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow; $i++) {
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);

        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $bulan = [];
        for ($i=0; $i < count($months); $i++) {
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => true
                ];
            }
            else{
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => false
                ];
            }
        }

        $data = [
            'kode_opco' => $opco,
            'tahun'     => $tahun,
            'bulan'     => $bulan
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function getDataKoreksiCapex(Request $request)
    {
        try {
            $request->validate([
                'filter_opco'  => 'nullable|exists:m_opco,kode_opco',
                'filter_bulan' => 'nullable|numeric',
                'filter_tahun' => 'nullable|numeric',
            ]);

            $data_koreksi =  DB::table('vw_tk_capex')->select(
                'holding',
                'kode_opco',
                'tanggal',
                DB::raw("to_char(rkap_capex, '999G999G999G999G990D9') as rkap_capex"),
                DB::raw("to_char(real_capex, '999G999G999G999G990D9') as real_capex"),
            );
            if($request->filter_opco){
                $data_koreksi = $data_koreksi -> where('kode_opco', $request->filter_opco);
            }
            if($request->filter_tahun){
                $data_koreksi = $data_koreksi -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY')"), $request->filter_tahun);
            }
            if($request->filter_bulan){
                $data_koreksi = $data_koreksi -> where(DB::raw("TO_CHAR(tanggal::timestamp, 'MM')"), $request->filter_bulan);
            }
            $data_koreksi = $data_koreksi->get();

            return DataTables::of($data_koreksi)->make();
            return [
                'message' => 'Successfull Get Data',
                'status' => 'success',
                'data' => $data_koreksi,
            ];
        } catch (Exception $e) {
            return [
                'status' => 'failed',
                'message' => $e->getMessage()
            ];
        }
    }

    public function saveDataKoreksi(Request $request)
    {

        $excel = json_decode($request->excel);

        $no_pbi = DB::table('m_opco')->select(
            'kode_opco','no_pbi')->get()->pluck('no_pbi', 'kode_opco')->toArray();

        foreach ($excel as $item) {

            $time = strtotime($item[2]);
            $newformat = date('Y-m-d', $time);
            $cek_similiarity = KoreksiCapex::where(DB::raw('date(tanggal)'), $newformat)
            ->where('kode_opco',$item[1])
            ->count();


            if ($cek_similiarity != 0) {
                KoreksiCapex::where(DB::raw('date(tanggal)'), $newformat)
                ->where('kode_opco',$item[1])
                ->delete();
            }

            KoreksiCapex::create([
                'no' => array_key_exists($item[1],$no_pbi)?$no_pbi[$item[1]]:0,
                'kode_opco' => $item[1],
                'holding' => 'SIG',
                'tanggal' => date('Y-m-d', strtotime($item[2])),
                'rkap_capex' => $item[3],
                'real_capex' => $item[4],
            ]);
        }

        $response = responseSuccess('Data added successfully');
        return response()->json($response, 200);
    }

    public function exportMTCCost(Request $request)
    {
        $data = $this->getDataExportFR($request);
        $tahun = $request->filter_tahun;
        return Excel::download(new ExportDataKoreksiFRCapex($data, $tahun), 'Export Data Koreksi MTC Cost.xlsx', null, [\Maatwebsite\Excel\Excel::XLSX]);
    }

    public function getDataExportFR($request)
    {
        $request->validate([
            'filter_bulan' => 'nullable|numeric',
            'filter_tahun' => 'nullable|numeric',
          ]);
        $tahun = $request->filter_tahun;
        $bulan = $request->filter_bulan;
    
        $now = Carbon::now();
        $nowMonth = strlen($bulan) == 1? '0'.$bulan: $bulan;
        $startMonth = Carbon::now()->startOfYear()->format('m');

        $dataMTC = DB::table('vw_tk_fr')
        ->join("vw_tk_capex",function($join){
                    $join->on("vw_tk_capex.kode_opco","=","vw_tk_fr.kd_opco")
                        ->on("vw_tk_capex.tanggal","=","vw_tk_fr.tanggal");
                })
        ->select(
            'vw_tk_fr.no',
            'vw_tk_fr.kd_opco as kode_opco',
            DB::raw("TO_CHAR(vw_tk_fr.tanggal::timestamp, 'YYYY') as tahun"),
            DB::raw("TO_CHAR(vw_tk_fr.tanggal::timestamp, 'MM') as bulan"),
            DB::raw('SUM(vw_tk_capex.rkap_capex) as rkap_capex'),
            DB::raw('SUM(vw_tk_capex.real_capex) as real_capex'),
            DB::raw('vw_tk_fr.prod_semen + vw_tk_fr.clinker_sold as produksi'),
            DB::raw('vw_tk_fr.rkap_prod_semen + vw_tk_fr.rkap_clinker_sold as rkap_produksi')
        );
        $arr_tahun = [$request->filter_tahun, ($request->filter_tahun - 1)];
        $dataMTC = $dataMTC->whereIn(DB::raw("TO_CHAR(vw_tk_fr.tanggal::timestamp, 'YYYY')"), $arr_tahun);
        $dataMTC = $dataMTC->where(DB::raw("TO_CHAR(vw_tk_fr.tanggal::timestamp, 'MM')"), $nowMonth);
        $dataMTC = $dataMTC->groupBy('vw_tk_fr.no',
        'vw_tk_fr.kd_opco',
        'vw_tk_fr.tanggal',
        'vw_tk_fr.prod_semen',
        'vw_tk_fr.clinker_sold',
        'vw_tk_fr.rkap_prod_semen',
        'vw_tk_fr.rkap_clinker_sold')->get()->toArray();

        $dataMTCYtd = DB::table('vw_tk_fr')
        ->join("vw_tk_capex",function($join){
                    $join->on("vw_tk_capex.kode_opco","=","vw_tk_fr.kd_opco")
                        ->on("vw_tk_capex.tanggal","=","vw_tk_fr.tanggal");
                })
        ->select(
            'vw_tk_fr.no',
            'vw_tk_fr.kd_opco as kode_opco',
            DB::raw("TO_CHAR(vw_tk_fr.tanggal::timestamp, 'YYYY') as tahun"),
            DB::raw('SUM(vw_tk_capex.rkap_capex) as rkap_capex'),
            DB::raw('SUM(vw_tk_capex.real_capex) as real_capex'),
            DB::raw('SUM(vw_tk_fr.prod_semen + vw_tk_fr.clinker_sold) as produksi'),
            DB::raw('SUM(vw_tk_fr.rkap_prod_semen + vw_tk_fr.rkap_clinker_sold) as rkap_produksi')
        );
        $arr_tahun = [$request->filter_tahun, ($request->filter_tahun - 1)];
        $dataMTCYtd = $dataMTCYtd->whereIn(DB::raw("TO_CHAR(vw_tk_fr.tanggal::timestamp, 'YYYY')"), $arr_tahun);
        $dataMTCYtd = $dataMTCYtd->whereBetween(DB::raw("TO_CHAR(vw_tk_fr.tanggal::timestamp, 'MM')"), [ $startMonth, $nowMonth]);
        $dataMTCYtd = $dataMTCYtd->groupBy('vw_tk_fr.no',
        'vw_tk_fr.kd_opco',
        DB::raw("TO_CHAR(vw_tk_fr.tanggal::timestamp, 'YYYY')"))->get()->toArray();

        $exchange_rate = ExchangeRate::pluck('nilai',DB::raw("TO_CHAR(tanggal::timestamp, 'YYYY') as tahun"))->toArray();
        foreach ($dataMTC as $key => $value) {
            $data[$value->tahun][$value->kode_opco]=  array(
              'kode_opco' => $value->kode_opco,
              'tahun' => $value->tahun,
              'rkap_capex' => $value->rkap_capex,
              'real_capex' => $value->real_capex,
              'rkap_produksi' => $value->rkap_produksi,
              'produksi' => $value->produksi,
            );
            $opco[$value->kode_opco]=[];
        }
        foreach ($dataMTCYtd as $key => $value) {
            $dataYTD[$value->tahun][$value->kode_opco]=  array(
              'kode_opco' => $value->kode_opco,
              'tahun' => $value->tahun,
              'rkap_capex' => $value->rkap_capex,
              'real_capex' => $value->real_capex,
              'rkap_produksi' => $value->rkap_produksi,
              'produksi' => $value->produksi,
            );
        }
        $kategori = ['CAPEX','RUPIAH/TON','USD/TON'];
        foreach ($kategori as $key => $kat) {
            $dataAll[$kat]['SIG']=$opco;
        }
        foreach ($data as $thn => $tahun) {
            $sumRealCapex = 0;
            $sumRkapCapex = 0;
            $sumRealProd = 0;
            $sumRkapProd = 0;
            $exchange = array_key_exists($thn, $exchange_rate)? $exchange_rate[$thn] : 15000;
            foreach ($tahun as $kd_opco => $opco) {
                $real_capex = $opco['real_capex'];
                $rkap_capex = $opco['rkap_capex'];
                $real_rupiah_ton = $opco['produksi'] <= 0 ? ($real_capex *1000000): ($real_capex *1000000) / $opco['produksi']; 
                $rkap_rupiah_ton = $opco['rkap_produksi'] <= 0 ? ($rkap_capex *1000000): ($rkap_capex *1000000) / $opco['rkap_produksi'];
                $real_usd_ton = $real_rupiah_ton / $exchange; 
                $rkap_usd_ton = $rkap_rupiah_ton / $exchange;

                $dtNow[$thn][$kd_opco] =[
                    'kode_opco' => $opco['kode_opco'],
                    'real_capex' => round($real_capex,0),
                    'rkap_capex' => round($rkap_capex,0),
                    'real_rupiah_ton'   => round($real_rupiah_ton,2),
                    'rkap_rupiah_ton'   => round($rkap_rupiah_ton,2),
                    'real_usd_ton'  => round($real_usd_ton,2),
                    'rkap_usd_ton'  => round($rkap_usd_ton,2)
                ];  

                $sumRealCapex += $opco['real_capex'];
                $sumRkapCapex += $opco['rkap_capex'];
                $sumRealProd += $opco['produksi'];
                $sumRkapProd += $opco['rkap_produksi'];
            }
            $real_capex = $sumRealCapex;
            $rkap_capex = $sumRkapCapex;
            $real_rupiah_ton = $sumRealProd <= 0 ? ($real_capex *1000000): ($real_capex *1000000) / $sumRealProd; 
            $rkap_rupiah_ton = $sumRkapProd <= 0 ? ($rkap_capex *1000000): ($rkap_capex *1000000) / $sumRkapProd;
            $real_usd_ton = $real_rupiah_ton / $exchange; 
            $rkap_usd_ton = $rkap_rupiah_ton / $exchange;

            $dtNow[$thn]['SIG'] =[
                'kode_opco' => 'SIG',
                'real_capex' => round($real_capex,0),
                'rkap_capex' => round($rkap_capex,0),
                'real_rupiah_ton'   => round($real_rupiah_ton,2),
                'rkap_rupiah_ton'   => round($rkap_rupiah_ton,2),
                'real_usd_ton'  => round($real_usd_ton,2),
                'rkap_usd_ton'  => round($rkap_usd_ton,2)
            ];  
        }
        foreach ($dataYTD as $thn => $tahun) {
            $sumRealCapex = 0;
            $sumRkapCapex = 0;
            $sumRealProd = 0;
            $sumRkapProd = 0;
            $exchange = array_key_exists($thn, $exchange_rate)? $exchange_rate[$thn] : 15000;
            foreach ($tahun as $kd_opco => $opco) {
                $real_capex = $opco['real_capex'];
                $rkap_capex = $opco['rkap_capex'];
                $real_rupiah_ton = $opco['produksi'] <= 0 ? ($real_capex *1000000): ($real_capex *1000000) / $opco['produksi']; 
                $rkap_rupiah_ton = $opco['rkap_produksi'] <= 0 ? ($rkap_capex *1000000): ($rkap_capex *1000000) / $opco['rkap_produksi'];
                $real_usd_ton = $real_rupiah_ton / $exchange; 
                $rkap_usd_ton = $rkap_rupiah_ton / $exchange;

                $dtYtd[$thn][$kd_opco] =[
                    'kode_opco' => $opco['kode_opco'],
                    'real_capex' => round($real_capex,0),
                    'rkap_capex' => round($rkap_capex,0),
                    'real_rupiah_ton'   => round($real_rupiah_ton,2),
                    'rkap_rupiah_ton'   => round($rkap_rupiah_ton,2),
                    'real_usd_ton'  => round($real_usd_ton,2),
                    'rkap_usd_ton'  => round($rkap_usd_ton,2)
                ];  

                $sumRealCapex += $opco['real_capex'];
                $sumRkapCapex += $opco['rkap_capex'];
                $sumRealProd += $opco['produksi'];
                $sumRkapProd += $opco['rkap_produksi'];
            }
            $real_capex = $sumRealCapex;
            $rkap_capex = $sumRkapCapex;
            $real_rupiah_ton = $sumRealProd <= 0 ? ($real_capex *1000000): ($real_capex *1000000) / $sumRealProd; 
            $rkap_rupiah_ton = $sumRkapProd <= 0 ? ($rkap_capex *1000000): ($rkap_capex *1000000) / $sumRkapProd;
            $real_usd_ton = $real_rupiah_ton / $exchange; 
            $rkap_usd_ton = $rkap_rupiah_ton / $exchange;

            $dtYtd[$thn]['SIG'] =[
                'kode_opco' => 'SIG',
                'real_capex' => round($real_capex,0),
                'rkap_capex' => round($rkap_capex,0),
                'real_rupiah_ton'   => round($real_rupiah_ton,2),
                'rkap_rupiah_ton'   => round($rkap_rupiah_ton,2),
                'real_usd_ton'  => round($real_usd_ton,2),
                'rkap_usd_ton'  => round($rkap_usd_ton,2)
            ];  
        }
        $tahun = $request->filter_tahun;
        foreach ($dataAll as $kKet => $ket) {
            if($kKet == 'CAPEX'){
                $tagRkap = 'rkap_capex';
                $tagReal = 'real_capex';
            }
            else if($kKet == 'RUPIAH/TON'){
                $tagRkap = 'rkap_rupiah_ton';
                $tagReal = 'real_rupiah_ton';
            }
            else{
                $tagRkap = 'rkap_usd_ton';
                $tagReal = 'real_usd_ton';
            }
            foreach ($ket as $kSig => $sig) {
              foreach ($sig as $kOpco => $opco) {
                $dataAll[$kKet][$kSig][$kOpco] = [
                  'kode_opco'   => $kOpco,
                  'rkap'.$tahun.'_this_month'       => $dtNow[$tahun][$kOpco][$tagRkap],
                  'real'.$tahun.'_this_month'       => $dtNow[$tahun][$kOpco][$tagReal],
                  'real'.($tahun-1).'_this_month'   => $dtNow[$tahun - 1][$kOpco][$tagReal],
                  '2_1'  => $dtNow[$tahun][$kOpco][$tagRkap] > 0 ? round(($dtNow[$tahun][$kOpco][$tagReal] / $dtNow[$tahun][$kOpco][$tagRkap])*100,2)  : 0,
                  '2_3'  => $dtNow[$tahun - 1][$kOpco][$tagReal] > 0 ? round(($dtNow[$tahun][$kOpco][$tagReal] / $dtNow[$tahun - 1][$kOpco][$tagReal])*100,2)  : 0,
                  'rkap'.$tahun.'_until_month'      =>$dtYtd[$tahun][$kOpco][$tagRkap],
                  'real'.$tahun.'_until_month'      =>$dtYtd[$tahun][$kOpco][$tagReal],
                  'real'.($tahun-1).'_until_month'  =>$dtYtd[$tahun - 1][$kOpco][$tagReal],
                  '5_4'  => $dtYtd[$tahun][$kOpco][$tagRkap] > 0 ? round(($dtYtd[$tahun][$kOpco][$tagReal] / $dtYtd[$tahun][$kOpco][$tagRkap])*100,2)  : 0,
                  '5_6'  => $dtYtd[$tahun - 1][$kOpco][$tagReal] > 0 ? round(($dtYtd[$tahun][$kOpco][$tagReal] / $dtYtd[$tahun - 1][$kOpco][$tagReal])*100,2)  : 0,
                ];
              }
            }
            $dataAll[$kKet][$kSig]['kode'] = 'SIG';
            
            $dataAll[$kKet][$kSig]['SIG'] = [
                'rkap'.$tahun.'_this_month'       => $dtNow[$tahun]['SIG'][$tagRkap],
                'real'.$tahun.'_this_month'       => $dtNow[$tahun]['SIG'][$tagReal],
                'real'.($tahun-1).'_this_month'   => $dtNow[$tahun - 1]['SIG'][$tagReal],
                '2_1'  => $dtNow[$tahun]['SIG'][$tagRkap] > 0 ? round(($dtNow[$tahun]['SIG'][$tagReal] / $dtNow[$tahun]['SIG'][$tagRkap])*100,2)  : 0,
                '2_3'  => $dtNow[$tahun - 1]['SIG'][$tagReal] > 0 ? round(($dtNow[$tahun]['SIG'][$tagReal] / $dtNow[$tahun - 1]['SIG'][$tagReal])*100,2)  : 0,
                'rkap'.$tahun.'_until_month'      =>$dtYtd[$tahun]['SIG'][$tagRkap],
                'real'.$tahun.'_until_month'      =>$dtYtd[$tahun]['SIG'][$tagReal],
                'real'.($tahun-1).'_until_month'  =>$dtYtd[$tahun - 1]['SIG'][$tagReal],
                '5_4'  => $dtYtd[$tahun]['SIG'][$tagRkap] > 0 ? round(($dtYtd[$tahun]['SIG'][$tagReal] / $dtYtd[$tahun]['SIG'][$tagRkap])*100,2)  : 0,
                '5_6'  => $dtYtd[$tahun - 1]['SIG'][$tagReal] > 0 ? round(($dtYtd[$tahun]['SIG'][$tagReal] / $dtYtd[$tahun - 1]['SIG'][$tagReal])*100,2)  : 0,
            ];
        }
        return($dataAll);
    }
}
