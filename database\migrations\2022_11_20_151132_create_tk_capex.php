<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTkCapex extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tk_capex', function (Blueprint $table) {
            $table->bigIncrements('id_koreksi_capex');
            $table->integer('no', false,false)->length(4)->nullable();
            $table->string('holding', 10);
            $table->string('kode_opco', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->decimal('rkap_capex',20, 2)->nullable();
            $table->decimal('real_capex',20, 2)->nullable();
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->timestamps();
            $table->index(['kode_opco', 'tanggal']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tk_capex');
    }
}
