<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTSRealisasiPerformance extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('ts_realisasi_performance', function (Blueprint $table) {
            $table->bigIncrements('id_realisasi_performance');
            $table->string('kode_plant', 30);
            $table->date('tanggal')->nullable()->default(NULL);
            $table->float('oph', 8, 5);
            $table->float('updt', 8, 5);
            $table->float('pdt', 8, 5);
            $table->float('stop_idle', 8, 5);
            $table->float('fy_stop', 8, 5);
            $table->float('frek_updt', 8, 5);
            $table->string('problem', 200);
            $table->string('source_system', 30)->nullable();
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->softDeletes();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('ts_realisasi_performance');
    }
}
