<?php

namespace App\Services;

use App\Models\Kiln_Stop;
use App\Models\User;
use App\Mail\CauseStopMail;

use Carbon\Carbon;
use DateTime;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class MailCauseStopService
{
    public function execute()
    {
        $date_current = 0;
        $data = Kiln_Stop::select('t_kiln_stop.*', 'm_kiln_plant.kode_opco', 'trp.fy_stop as trp_fy_stop')
            ->leftJoin('m_kiln_plant', 't_kiln_stop.kode_plant', '=', 'm_kiln_plant.kode_plant')
            ->leftjoin('ts_rkap_perfomance as trp', function($join){
                $join->on('t_kiln_stop.kode_plant', '=', 'trp.kode_plant');
                $join->on(DB::raw("to_char(t_kiln_stop.tanggal_mulai::timestamp with time zone, 'YYYY-FMMM'::text)"), '=',DB::raw("concat(trp.tahun, '-', trp.bulan)"));
            })
            ->whereNull('t_kiln_stop.jenis_downtime')
            ->where('t_kiln_stop.is_sent', 0)
            ->orderby('t_kiln_stop.id_kiln_stop','ASC')
            ->get();

        foreach ($data as $val) {
            if ((int)$val->trp_fy_stop == 0) {
                $create_start = $val->tanggal_mulai;
                $createdate = date_create($val->tanggal_mulai);
                $date_current = date_format($createdate,"Y-m-d H:i");
                $date_current_2 = date_format($createdate,"Y-m-d");
                $start_date = new DateTime($date_current);
                $data_kiln_stop = Kiln_Stop::select('is_sent','id_kiln_stop', 'tanggal_mulai','tanggal_selesai', 'jenis_downtime', 'id_kategori', 'is_sent')
                    ->where('tanggal_selesai','=', DB::raw("TO_CHAR(tanggal_mulai::timestamp, 'YYYY-MM-DD')::date + INTERVAL '1 day'"))
                    ->where('id_kiln_stop','<', $val->id_kiln_stop)
                    ->where('kode_plant', '=', $val->kode_plant)
                    ->whereNull('jenis_downtime')
                    ->orderby('id_kiln_stop', 'ASC')
                    ->first();

                $data_kiln_stop_now_tanggal_mulai = Kiln_Stop::select('tanggal_mulai')
                    ->where('id_kiln_stop','=', $val->id_kiln_stop)
                    ->where('kode_plant', '=', $val->kode_plant)
                    ->whereNull('jenis_downtime')
                    ->first();

                if($data_kiln_stop_now_tanggal_mulai){
                    $createdate_now_tanggal_mulai = date_create($data_kiln_stop_now_tanggal_mulai->tanggal_mulai);
                    $date_now_tanggal_mulai = date_format($createdate_now_tanggal_mulai,"d");
                    $tanggal_now = $date_now_tanggal_mulai;
                }else{
                    $tanggal_now = "0";
                }

                // jika data sebelumnya mati maka tidak kirim email
                $data_lintas_hari = Kiln_Stop::select('is_sent','id_kiln_stop', 'tanggal_mulai','tanggal_selesai', 'jenis_downtime', 'id_kategori', 'is_sent', 'alasan')
                    ->where('id_kiln_stop','<', $val->id_kiln_stop)
                    ->where('kode_plant', '=', $val->kode_plant)
                    // ->whereNull('jenis_downtime')
                    ->orderby('id_kiln_stop', 'DESC')
                    ->first();

                if($data_lintas_hari){
                    $create_end = $data_lintas_hari->tanggal_selesai;
                    $createdate_before = date_create($data_lintas_hari->tanggal_selesai);
                    $date_before = date_format($createdate_before,"Y-m-d H:i");
                    $date_before_2 = date_format($createdate_before,"Y-m-d");
                    $since_start = $start_date->diff(new DateTime($date_before));
                    $minutes = (strtotime($create_start) - strtotime($create_end))/60;
                }
                // toleransi lintas hari 10 menit
                if ($data_lintas_hari && date('H', strtotime($create_start)) == '00' && $minutes <= 10 && $date_current_2 == $date_before_2 && $tanggal_now != "01") {
                        Kiln_Stop::where('id_kiln_stop', '=', $val->id_kiln_stop)->update([
                            'is_sent' => 4,
                            'keterangan_sent' => 'Off Lintas Hari!',
                            'jenis_downtime' => $data_lintas_hari['jenis_downtime'],
                            'id_kategori' => $data_lintas_hari['id_kategori'],
                            'alasan' => $data_lintas_hari['alasan'],
                        ]);
                    if ($data_kiln_stop) {
                        // jika pengiriman sebelumnya gagal (2) maka mengirim ulang dengan id_kiln_stop sebelumnya
                        if($data_kiln_stop['is_sent'] == 2){
                            $user = User::select('email')->where('plant_relation', 'ilike', '%' . $val->kode_plant . '%')->where('received_email',true)->where('status',1)->pluck('email');
                            $details = [
                                'opco' => $val->kode_opco ?? '-',
                                'plant' => $val->kode_plant,
                                'url' => config('app.url').'/cause-stop/'.$data_kiln_stop['id_kiln_stop']
                            ];
                            if(!empty($user)){
                                $row_kiln = Kiln_Stop::find($data_kiln_stop['id_kiln_stop']);
                                try {
                                    Mail::to($user)->send(new CauseStopMail($details));
                                    $row_kiln->update([
                                        'is_sent' => 1,
                                        'keterangan_sent' => 'Berhasil Terkirim!',
                                    ]);
                                } catch (Exception $e) {
                                    $row_kiln->update([
                                        'is_sent' => 2,
                                        'keterangan_sent' => 'Gagal Terkirim: '.$e,
                                    ]);
                                }
                            }
                        }
                    }
                }else{
                    $user = User::select('email')->where('plant_relation', 'ilike', '%' . $val->kode_plant . '%')->where('received_email',true)->where('status',1)->pluck('email');
                    $details = [
                        'opco' => $val->kode_opco ?? '-',
                        'plant' => $val->kode_plant,
                        'url' => config('app.url').'/cause-stop/'.$val->id_kiln_stop
                    ];
                    if(!empty($user)){
                        $row_kiln = Kiln_Stop::find($val->id_kiln_stop);
                        try {
                            Mail::to($user)->send(new CauseStopMail($details));
                            $row_kiln->update([
                                'is_sent' => 1,
                                'keterangan_sent' => 'Berhasil Terkirim!',
                            ]);
                        } catch (Exception $e) {
                            $row_kiln->update([
                                'is_sent' => 2,
                                'keterangan_sent' => 'Gagal Terkirim: '.$e,
                            ]);
                        }
                        }
                }

            }else{
                // $date = date_create((String)\Carbon\Carbon::parse($val->tanggal_mulai)->format('Y-m-d')." 23:59:00");
                // $create_date = date_format($date,"Y-m-d H:i:s");

                Kiln_Stop::where('id_kiln_stop', $val->id_kiln_stop)->update([
                    // 'tanggal_selesai' => $create_date,
                    'jenis_downtime' => 'FY STOP',
                    'is_sent' => 3,
                    'alasan' => 'Full Year Stop',
                ]);

            }
        }
    }
}
