<?php

namespace App\Http\Controllers;

use App\Models\PlantICS;
use DataTables;
use Illuminate\Http\Request;
use App\Models\Opco;
use Exception;
use Illuminate\Support\Facades\Auth;
use App\Models\Menu;
use App\Models\Routes;
use Illuminate\Support\Facades\DB;

use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportTempPlantICS;

use Illuminate\Support\Facades\Hash;
use Storage;

use App\Rules\ExcelRule;

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;



class PlantICSController extends Controller
{
    public function index()
    {
        $data = [
            'title' => 'Plant ICS',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'/plant-ics',
                ],
                [
                    'title'=>'Plant ICS',
                    'url'=>'',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        $data['opcos'] = Opco::get();
        return view('PlantICS', $data);
    }

    public function datatables(Request $request)
    {
        $query    = PlantICS::get();

        foreach ($query as $key => $value) {
            if($value['kode_opco'] == NULL){
                $query[$key]['kode_opco'] = '-';
            }
            if($value['opco'] == NULL){
                $query[$key]['opco'] = '-';
            }
        }

        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function store(Request $request)
    {
        $request->validate([
            'opco' => 'required|max:30',
            'plant' => 'required|max:30|unique:m_plant_ics',
            'plant_name' => 'required|max:100',
            // 'kode_opco' => 'required',
        ]);

        $attributes = $request->only('opco', 'plant', 'plant_name', 'kode_opco');

        $roles = [
            'opco' => 'required|max:30',
            'plant' => 'required|max:30|unique:m_plant_ics',
            'plant_name' => 'required|max:100',
            // 'kode_opco' => 'required',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'max'    => trans('messages.max'),
        ];
        $this->validators($attributes, $roles, $messages);

        $plant = PlantICS::create([
            'opco' => $request->opco,
            'plant' => $request->plant,
            'plant_name' => $request->plant_name,
            'kode_opco' => $request->kode_opco,
            'create_by' => Auth::user()->username,
        ]);

        $response = responseSuccess(trans("messages.create-success"),$plant);
        // return $response;
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function show($plant)
    {
        $query   = PlantICS::find($plant);

        if($query->kode_opco == NULL){
            $query->kode_opco = '-';
        }

        $response = responseSuccess(trans("messages.read-success"), $query);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function update($id, Request $request)
    {
        if($request->kode_opcoEdit == ""){
            $request->kode_opcoEdit = NULL;
        }
        if($request->opcoEdit == ""){
            $request->opcoEdit = NULL;
        }

        $data = $this->findDataWhere(PlantICS::class, ['plant' => $id]);

          DB::beginTransaction();
          try {
              $data->update([
                'opco' => $request->opcoEdit,
                'plant' => $request->plantEdit,
                'plant_name' => $request->plant_nameEdit,
                'kode_opco' => $request->kode_opcoEdit,
                'update_by' => Auth::user()->username,
              ]);
              DB::commit();
              $response = responseSuccess(trans("messages.update-success"), ['data' => $id]);
              return response()->json($response, 200, [], JSON_PRETTY_PRINT);
          } catch (Exception $e) {
              DB::rollback();
              $response = responseFail(trans("messages.update-fail"), $e->getMessage());
              return response()->json($response, 500, [], JSON_PRETTY_PRINT);
            }
    }

    public function destroy($id)
    {
        DB::beginTransaction();
        try {
            $data = PlantICS::where('plant', $id)->delete();
            DB::commit();
            $response = responseSuccess(trans("messages.delete-success"), ['data' => $id]);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            DB::rollback();
            $response           = responseFail(trans("messages.delete-fail"));
            $response['errors'] = $e->getMessage();
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function template()
    {
        return Excel::download(new ExportTempPlantICS, 'Template Plant ICS.xlsx');
    }

    public function upload(Request $request)
    {
        $request->validate([
            'excel' => ['required', new ExcelRule($request->file('excel'))],
        ]);
        $filename = $request->file('excel')->hashName();

        Storage::disk('public')->put('planticstemplate', $request->file('excel'));

        $path = storage_path('app/public/planticstemplate/' . $filename);

        $reader = new Xlsx();
        $spreadsheet = $reader->load($path);

        $sheetData = $spreadsheet->getActiveSheet()->toArray();

        if(count($sheetData) >= 2){
            for ($c = 1; $c < count($sheetData); $c++) {

                if ($sheetData[$c][1] != ""){
                    $planticsExist = PlantICS::where("plant",$sheetData[$c][1])->first();
                    // echo '<pre>';
                    // var_dump($planticsExist);
                    // die();
                    if(!$planticsExist){
                        $plantics = PlantICS::create([
                            'opco' => $sheetData[$c][0],
                            'plant' => $sheetData[$c][1],
                            'plant_name' => $sheetData[$c][2],
                            'kode_opco' => $sheetData[$c][3],
                            'create_by' => Auth::user()->username,
                        ]);
                    }
                    elseif ($planticsExist['opco'] == $sheetData[$c][0] && $planticsExist['plant'] == $sheetData[$c][1]
                    && $planticsExist['plant_name'] == $sheetData[$c][2] && $planticsExist['kode_opco'] == $sheetData[$c][3]){
                        continue;
                    }
                    else{
                        $update = DB::table('m_plant_ics')
                            ->where("plant",$sheetData[$c][1])
                            ->update([
                                'opco' => $sheetData[$c][0],
                                'plant_name' => $sheetData[$c][2],
                                'kode_opco' => $sheetData[$c][3],
                                'update_by' => Auth::user()->username,
                            ]);
                    }
                }
                else{
                    continue;
                }
            }
        }
        // Storage::delete('app/public/planticstemplate/' . $filename);
        Storage::disk('public')->delete('planticstemplate/' . $filename);
        return redirect('plant-ics');
    }

}
