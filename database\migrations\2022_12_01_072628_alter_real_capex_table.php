<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterRealCapexTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //Delete View relate first
       DB::statement("DROP VIEW IF EXISTS vw_capex");
       DB::statement("DROP VIEW IF EXISTS vw_tk_capex");
       DB::commit();
       
       Schema::table('ts_realisasi_capex', function (Blueprint $table) {
        $table->float('real_capex', 52, 0)->change();
       });

      // create view again
      Artisan::call('migrate:refresh', [
          '--path' => 'database/migrations/2022_11_30_072628_create_vw_capex.php',
      ]);
      Artisan::call('migrate:refresh', [
          '--path' => 'database/migrations/2022_11_30_072629_create_vw_tk_capex.php',
      ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
