<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterSyncOpcConfigsTableAddSoftdelete extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sync_opc_configs', function (Blueprint $table) {
            // Only add deleted_at if it doesn't exist
            if (!Schema::hasColumn('sync_opc_configs', 'deleted_at')) {
                $table->softDeletes();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('sync_opc_configs', function (Blueprint $table) {
            if (Schema::hasColumn('sync_opc_configs', 'deleted_at')) {
                $table->dropSoftDeletes();
            }
        });
    }
}
