<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMmMaterialSbiTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('mm_material_sbi', function (Blueprint $table) {
            $table->string('kode_material',30)->primary();
            $table->string('nama_material',100);
            $table->string('jenis_material',30);
            $table->string('kd_jenis',10);
            $table->string('create_by', 30)->nullable();
            $table->string('update_by', 30)->nullable();
            $table->softDeletes();
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('mm_material_sbi');
    }
}
