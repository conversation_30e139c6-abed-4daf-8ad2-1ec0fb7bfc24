.login.login-5 .login-aside {
    background-color: #ffffff;
}

.login.login-5 .login-aside .wizard-nav {
    padding: 0;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step {
    padding: 0.75rem 0;
    -webkit-transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease, -webkit-box-shadow 0.15s ease;
    margin-bottom: 1.5rem;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step:last-child {
    margin-bottom: 0;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease, -webkit-box-shadow 0.15s ease;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    background-color: #F3F6F9;
    margin-right: 1rem;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-icon .wizard-check {
    display: none;
    font-size: 1.4rem;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-icon .wizard-number {
    font-weight: 600;
    color: #464E5F;
    font-size: 1.35rem;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-label {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-title {
    color: #212121;
    font-weight: 500;
    font-size: 1.2rem;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step .wizard-label .wizard-desc {
    color: #B5B5C3;
    font-size: 0.925rem;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"], .login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] {
    -webkit-transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease, -webkit-box-shadow 0.15s ease;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-icon, .login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-icon {
    -webkit-transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, -webkit-box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease;
    transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease, box-shadow 0.15s ease, -webkit-box-shadow 0.15s ease;
    background-color: #C9F7F5;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-icon .wizard-check, .login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-icon .wizard-check {
    color: #1BC5BD;
    display: inline-block;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-icon .wizard-number, .login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-icon .wizard-number {
    display: none;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-label .wizard-title, .login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-title {
    color: #B5B5C3;
}

.login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="done"] .wizard-label .wizard-desc, .login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step[data-wizard-state="current"] .wizard-label .wizard-desc {
    color: #B5B5C3;
}

.login.login-5 .login-aside .aside-img-wizard {
    min-height: 320px !important;
    background-size: 400px;
}

.login.login-5 .login-content {
    background-color: #F3F6F9;
}

@media (min-width: 992px) {
    .login.login-5 .login-aside {
        width: 100%;
        max-width: 600px;
    }

    .login.login-5 .login-form {
        width: 100%;
        max-width: 450px;
    }

    .login.login-5 .login-form.login-form-signup {
        max-width: 650px;
    }
}

@media (min-width: 992px) and (max-width: 1399.98px) {
    .login.login-5 .login-aside {
        width: 100%;
        max-width: 400px;
    }
}

@media (max-width: 991.98px) {
    .login.login-5 .login-aside .aside-img {
        min-height: 500px !important;
        background-size: 600px;
    }

    .login.login-5 .login-aside .login-logo {
        text-align: center;
    }

    .login.login-5 .login-aside .wizard-nav {
        padding: 0;
        -ms-flex-line-pack: center;
        align-content: center;
    }

    .login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step {
        margin-bottom: .5rem;
    }

    .login.login-5 .login-aside .wizard-nav .wizard-steps .wizard-step:last-child {
        margin-bottom: 0;
    }

    .login.login-5 .login-form {
        width: 100%;
        max-width: 400px;
        background-size: 500px;
    }
}
