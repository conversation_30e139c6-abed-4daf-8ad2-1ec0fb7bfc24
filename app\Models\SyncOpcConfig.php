<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SyncOpcConfig extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'name',
        'type',
        'url',
        'parameter',
        'schedule',
        'at_date',
        'at_time',
        'status',
        'created_at',
        'updated_at',
        'kode_opco'
    ];

    public static function apiTypes()
    {
        return [
            'api_opc_client' => 'API OPC Client',
            'api_opc_sp' => 'API OPC SP',
            'api_opc_tonasa' => 'API OPC Tonasa',
            'api_opc_tlcc' => 'API OPC TLCC'
        ];
    }

    public static function schedules()
    {
        return [
            'minutely' => 'Minutely',
            'everyfiveminute' => 'Every 5 Minutes',
            'hourly' => 'Hourly',
            'daily' => 'Daily',
            'monthly' => 'Monthly'
        ];
    }

    public static function statuses()
    {
        return [
            'active' => 'Active',
            'non_active' => 'Non Active'
        ];
    }

    public function logs()
    {
        return $this->hasMany(SyncOpcLog::class);
    }
}
