<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class DmmPeriodePenilaian extends Model
{
    //
    protected $table = 'dmm_periode_penilaian';
    protected $fillable = [
        'periode',
        'tahun',
        'start_date',
        'end_date',
        'status',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at',
    ];

    public static function list()
    {
        $query = DB::table('dmm_periode_penilaian')
            ->leftjoin('users', 'dmm_periode_penilaian.created_by', '=', 'users.id')
            ->select('dmm_periode_penilaian.*', 'users.username as created_by_name');
        return $query;
    }
    public function penilaian_opco()
    {
        return $this->hasMany(PenilaianOpco::class, 'periode_penilaian_id', 'id');
    }
}
