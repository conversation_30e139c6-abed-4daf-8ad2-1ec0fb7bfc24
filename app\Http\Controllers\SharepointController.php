<?php


namespace App\Http\Controllers;

use App\Http\Requests\SharepointRequest;
use App\Models\Sharepoint;
use DataTables;
use Illuminate\Http\Request;
use App\Models\Menu;
use App\Models\Routes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use GuzzleHttp\Client;

class SharepointController extends Controller
{
    public function index()
    {
        // $query = new Company();
        $data['menus'] = $this->getDashboardMenu();
        $data['menu']  = Menu::select('id', 'name')->get();
        // $parenth1 = Company::whereNull('parenth1')->get();
        // $parenth2 = Company::whereNotNull('parenth1')->whereNull('parenth2')->get();
        // $data['parenth1'] = $parenth1;
        // $data['parenth2'] = $parenth2;

        return view('sharepoint', $data);
    }
    public function list_document(Request $request)
    {
        // dd($request->all());exit;
        // dd($request->getListRequest['folderPath']);
        $fpath = base64_decode($request->getListRequest['folderPath']);
        $headers = [
                    'Content-Type'  => 'application/json',
                    "Message-Id"=> "TEST-UAT-001",
                    "Source-Name"=> "TEST",
                    "Authorization"=> "Basic bHBzX3VzZXI6cEBzc3dvcmQ=", 
                    'Accept' => 'application/json',
                    
        ];

        // JSON Data for API post
            $GetOrder = '{
                "getListDocumentRequest": {
                    "folderPath": "'.$fpath.'"
                        
                }
            }';
            
            $client = new client();
            $response = $client->request('POST', 'http://10.121.88.36:5556/api/sharepoint/getListDocument', ['http_errors'=>false,'headers' => $headers,'body' => $GetOrder]);
            // dd($response->getBody());exit;
            $response2 = json_decode($response->getBody()->getContents(), true);
            $data     = DataTables::of($response2)->make(true);
            $response4 = $data->getData(true);
            $array = $response4['data'][0]['data'];
            //Get List Folder
            $GetOrder = '{
                "getListRequest": {
                    "folderPath": "'.$fpath.'"
                        
                }
            }';
            
            $client = new client();
            $response = $client->request('POST', 'http://10.121.88.36:5556/api/sharepoint/getListFolder', ['http_errors'=>false,'headers' => $headers,'body' => $GetOrder]);
            $response2 = json_decode($response->getBody()->getContents(), true);
            $data     = DataTables::of($response2)->make(true);
            $response4 = $data->getData(true);
            // dd($response4);exit;
            $array2 = $response4['data'][0]['data'];
            $array = array_merge($array2,$array);
            
            return response()->json($array, 200, [], JSON_PRETTY_PRINT);
            // return $response2;
            
        
    }
  
    public function datatables(Request $request)
    {
        $query    = Sharepoint::get();


        
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    
    public function store(Request $request)
    {
        $headers = [
            'Content-Type'  => 'application/json',
            "Message-Id"=> "TEST-UAT-001",
            "Source-Name"=> "TEST",
            "Authorization"=> "Basic bHBzX3VzZXI6cEBzc3dvcmQ=", 
            'Accept' => 'application/json',
            
        ];
        // if(substr($request->type,0,4) == 'xlsx' || substr($request->type,0,4) == 'docx'){
        //     $type = '.'.$request->type;
        // }else{
        //     $type = $request->type;
        // }
        
        // JSON Data for API post
            $GetOrder = '{
                "uploadDocumentRequest": {
                    "folderPath": "'.base64_decode($request->uploadDocumentRequest['folderPath']).'",
                    "filename": "'.$request->uploadDocumentRequest['filename'].'.'.$request->uploadDocumentRequest['type'].'",
                    "fileContent": "'.$request->uploadDocumentRequest['fileContent'].'"
                        
                }
            }';
            // dd($GetOrder);exit;
            $client = new client();
            $response = $client->request('POST', 'http://10.121.88.36:5556/api/sharepoint/uploadDocument', ['headers' => $headers,'body' => $GetOrder]);
            return $response->getBody()->getContents();
    }
    public function store_folder(Request $request)
    {
        $headers = [
            'Content-Type'  => 'application/json',
            "Message-Id"=> "TEST-UAT-001",
            "Source-Name"=> "TEST",
            "Authorization"=> "Basic bHBzX3VzZXI6cEBzc3dvcmQ=", 
            'Accept' => 'application/json',
            
        ];

        // JSON Data for API post
            $GetOrder = '{
                "createFolderByPathRequest": {
                    "folderPath": "'.base64_decode($request->createFolderByPathRequest['filename']).'"   
                }
            }';
            // dd($GetOrder);exit;
            $client = new client();
            $response = $client->request('POST', 'http://10.121.88.36:5556/api/sharepoint/createFolderByPath', ['headers' => $headers,'body' => $GetOrder]);
            return $response->getBody()->getContents();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {

        $headers = [
            'Content-Type'  => 'application/json',
            "Message-Id"=> "TEST-UAT-001",
            "Source-Name"=> "TEST",
            "Authorization"=> "Basic bHBzX3VzZXI6cEBzc3dvcmQ=", 
            'Accept' => 'application/json',
            
        ];

        // JSON Data for API post
            $GetOrder = '{
                "readDocumentRequest": {
                    "documentId": "'.$request->readDocumentRequest['documentId'].'",
                    "includeFile": "N"
                        
                }
            }';
            // dd($GetOrder);exit;
            $client = new client();
            $response = $client->request('POST', 'http://10.121.88.36:5556/api/sharepoint/readDocument', ['headers' => $headers,'body' => $GetOrder]);
            return $response->getBody()->getContents();
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $headers = [
            'Content-Type'  => 'application/json',
            "Message-Id"=> "TEST-UAT-001",
            "Source-Name"=> "TEST",
            "Authorization"=> "Basic bHBzX3VzZXI6cEBzc3dvcmQ=", 
            'Accept' => 'application/json',
            
        ];

        // JSON Data for API post
            $GetOrder = '{
                "updateDocumentRequest": {
                    "documentId": "'.$request->updateDocumentRequest['documentId'].'",
                    "fileContent": "'.$request->updateDocumentRequest['fileContent'].'"
                        
                }
            }';
            // dd($GetOrder);exit;
            $client = new client();
            $response = $client->request('POST', 'http://10.121.88.36:5556/api/sharepoint/updateDocument', ['headers' => $headers,'body' => $GetOrder]);
            return $response->getBody()->getContents();

    }


    public function destroy(Request $request)
    {
        // dd($request->deleteDocumentRequest);exit;
        $headers = [
            'Content-Type'  => 'application/json',
            "Message-Id"=> "TEST-UAT-001",
            "Source-Name"=> "TEST",
            "Authorization"=> "Basic bHBzX3VzZXI6cEBzc3dvcmQ=", 
            'Accept' => 'application/json',
            
        ];

        // JSON Data for API post
            $GetOrder = '{
                "deleteDocumentRequest": {
                    "documentId": "'.$request->deleteDocumentRequest['documentId'].'"
                        
                }
            }';
            // dd($GetOrder);exit;
            $client = new client();
            $response = $client->request('POST', 'http://10.121.88.36:5556/api/sharepoint/deleteDocument', ['headers' => $headers,'body' => $GetOrder]);
            return $response->getBody()->getContents();
    }
    public function destroy_folder(Request $request)
    {
        // dd($request->deleteDocumentRequest);exit;
        $headers = [
            'Content-Type'  => 'application/json',
            "Message-Id"=> "TEST-UAT-001",
            "Source-Name"=> "TEST",
            "Authorization"=> "Basic bHBzX3VzZXI6cEBzc3dvcmQ=", 
            'Accept' => 'application/json',
            
        ];

        // JSON Data for API post
            $GetOrder = '{
                "deleteFolderByPathRequest": {
                    "folderPath": "'.$request->deleteFolderByPathRequest['folderPath'].'"
                        
                }
            }';
            // dd($GetOrder);exit;
            $client = new client();
            $response = $client->request('POST', 'http://10.121.88.36:5556/api/sharepoint/deleteFolderByPath', ['headers' => $headers,'body' => $GetOrder]);
            return $response->getBody()->getContents();
    }
    public function fileUpload()
    {
        return view('sharepoint');
    }

    public function fileUploadPost(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:pdf,xlx,csv|max:2048',
        ]);

        $fileName = time().'.'.$request->file->extension();  

        $request->file->move(public_path('asset'), $fileName);

        return back()
            ->with('success','You have successfully upload file.')
            ->with('file',$fileName);
    }
    public function download(Request $request)
    {
        $request_id = $request->id;
        $downloadReport = Sharepoint::where('id', $request_id)->first();
        $upload_report = $downloadReport->filename;
        $headers = array(
            'Content-Type: application/jpg',
            'Content-Type: application/docx',
            'Content-Type: application/pdf',
        );
        $url=  public_path().'/assets/attachment/'. $upload_report;
        // print_r($url);exit;
        // $response = responseSuccess(trans('message.read-success'),$url);
        // return response()->json($response,200);
        return response()->download($url);
    }
}
