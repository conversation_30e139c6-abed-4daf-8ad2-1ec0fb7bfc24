<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\MasterKuisioner;
use App\Models\SubMasterKuisioner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Yajra\DataTables\Facades\DataTables;

class SubMasterKuisionerController extends Controller
{
    //
    //
    public function index()
    {
        $data = [
            'title' => 'Sub Kuisioner',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'#',
                ],
                [
                    'title'=>' Sub Master Kuisioner',
                    'url'=>'sub-master-kuisioner',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['master_kuisioner']  = MasterKuisioner::select('id', 'kuisioner')->where('status','y')->get();
        $data['isSub'] = false;
        $data['uuid'] = '';
        return view('master.sub_master_kuisioner', $data);
    }

    public function sub($uuid,Request $request)
    {
        $data = [
            'title' => 'Sub Kuisioner',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'#',
                ],
                [
                    'title'=>'Sub Master Kuisioner',
                    'url'=>'sub-master-kuisioner',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();
        $data['isSub'] = true;
        $data['master_kuisioner_id']  = MasterKuisioner::select('id', 'kuisioner')->where('uuid',$uuid)->first()->id;
        $data['uuid'] = $uuid;
        return view('master.sub_master_kuisioner', $data);
    }



     public function datatables(Request $request)
    {
        $query    = SubMasterKuisioner::select('sub_master_kuisioner.uuid','sub_master_kuisioner.sub_kuisioner','sub_master_kuisioner.status','sub_master_kuisioner.urutan','sub_master_kuisioner.jenis','sub_master_kuisioner.max_pts')
        ->leftjoin('master_kuisioner','sub_master_kuisioner.master_kuisioner_id','=','master_kuisioner.id');
       
        if($request->sub_kuisioner){
            $query->where('sub_master_kuisioner.sub_kuisioner','like','%'.$request->sub_kuisioner.'%');
        }
        if($request->f_status){
            $query->where('sub_master_kuisioner.status',$request->status);
        }
        if($request->uuid && $request->uuid != ''){
            $query->where('master_kuisioner.uuid',$request->uuid);
        }
        if($request->uuid && $request->uuid != ''){
            $query->where('master_kuisioner.uuid',$request->uuid);
        }
        if($request->jenis){
            $query->where('sub_master_kuisioner.jenis',$request->jenis);
        }
        if($request->max_pts){
            $query->where('sub_master_kuisioner.max_pts',$request->max_pts);
        }
        if($request->status){
            $query->where('sub_master_kuisioner.status',$request->status);
        }
        
        $data     = DataTables::of($query)->make(true);
        $response = $data->getData(true);

        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function store(Request $request) {
        $attributes = $request->only(['master_kuisioner_id','sub_kuisioner','max_pts','jenis','urutan','status']);
        $roles = [
            'master_kuisioner_id' => 'required|exists:master_kuisioner,id',
            'sub_kuisioner' => 'required',
            'max_pts' => 'required|numeric',
            'jenis' => 'required|in:yn,abcd',
            'urutan' => ['required',
                            Rule::unique('sub_master_kuisioner')->where(function ($query)  use ($request){
                                return $query->where('master_kuisioner_id', $request->master_kuisioner_id)
                                            ->where('status', 'y');
                            }),
                        ],
            'status' => 'required|in:y,n',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
        ];
        $this->validators($attributes, $roles, $messages);
        $attributes['created_by'] = Auth()->user()->id;
        $attributes['updated_by'] = Auth()->user()->id;
        DB::beginTransaction();
        try {
            //code...
            $data = SubMasterKuisioner::create($attributes);
            DB::commit();
            $response = responseSuccess(trans("messages.create-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.create-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function show($uuid)
    {
        $attributes['uuid'] = $uuid;

        $roles = [  
            'uuid' => 'required|exists:sub_master_kuisioner',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);
        $data     = $this->findDataWhere(SubMasterKuisioner::class, ['uuid' => $uuid]);
        $response = responseSuccess(trans("messages.read-success"), $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }

    public function update($uuid, Request $request) {
        $attributes = $request->only(['master_kuisioner_id','sub_kuisioner','max_pts','jenis','urutan','status']);
        $roles = [
            'master_kuisioner_id' => 'required|exists:master_kuisioner,id',
            'sub_kuisioner' => 'required',
            'max_pts' => 'required|numeric',
            'jenis' => 'required|in:yn,abcd',
            'urutan' => ['required',
                            Rule::unique('sub_master_kuisioner')->where(function ($query)  use ($request, $uuid){
                                return $query->where('master_kuisioner_id', $request->master_kuisioner_id)
                                            ->where('status', 'y')
                                            ->where('uuid', '!=', $uuid);
                            }),
                        ],
            'status' => 'required|in:y,n',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'in'   => trans('messages.in'),
        ];
        $this->validators($attributes, $roles, $messages);
        $attributes['updated_by'] = Auth()->user()->id;
        $attributes['urutan'] = intval($attributes['urutan']);
        $data = $this->findDataWhere(SubMasterKuisioner::class, ['uuid' => $uuid]);
        DB::beginTransaction();
        try {
            //code...
            $data->update($attributes);
            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }

    public function destroy($uuid)
    {
        $attributes['uuid'] = $uuid;
        $roles = [  
            'uuid' => 'required|exists:sub_master_kuisioner',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);
        DB::beginTransaction();
        try {
            //code...
            SubMasterKuisioner::where('uuid', $uuid)->delete();
            DB::commit();
            $response = responseSuccess(trans("messages.delete-success"), []);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.delete-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
}
