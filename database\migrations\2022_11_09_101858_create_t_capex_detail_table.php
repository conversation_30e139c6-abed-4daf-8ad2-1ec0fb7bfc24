<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTCapexDetailTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('t_detail_capex', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('no_billing', 30)->nullable();
            $table->string('kode_opco', 30);
            $table->string('no_project', 30);
            $table->date('tanggal');
            $table->decimal('biaya_capex', 20, 2)->nullable();
            $table->string('mutasi', 1)->nullable();
            $table->string('created_by')->nullable();
            $table->string('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('t_detail_capex');
    }
}
