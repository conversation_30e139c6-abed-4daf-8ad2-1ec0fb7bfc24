<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CostElement extends Model
{
    use SoftDeletes;

    protected $table = 'm_cost_element';
    protected $primaryKey = 'cost_element';
    public $incrementing = false;

    protected $fillable = [
        'cost_element',
        'cost_element_name',
        'cost_element_group',
        'biaya_group',
        'create_by',
        'update_by',
        'kode_opco'
    ];
}
