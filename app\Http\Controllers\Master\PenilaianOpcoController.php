<?php

namespace App\Http\Controllers\Master;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\DmmPeriodePenilaian;
use App\Models\PenilaianOpco;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class PenilaianOpcoController extends Controller
{
    //
    public function index()
    {
        $data = [
            'title' => 'Penilaian OPCO',
            'breadcrumb' => [
                [
                    'title'=>'Master Data',
                    'url'=>'#',
                ],
                [
                    'title'=>'Penilaian OPCO',
                    'url'=>'penilaian-opco',
                ]
            ],
        ];
        $data['company'] = Company::all();
        $data['periode_penilaian'] = DmmPeriodePenilaian::select('id','tahun','periode','status')->get();
        $data['menus'] = $this->getDashboardMenu();
        return view('master.penilaian-opco', $data);
    }
    public function datatables(Request $request)
    {
        $query = PenilaianOpco::list();

        $columns = [
            'dmm_penilaian_opco.uuid'=>'uuid',
            'mc.description'=>'company_name',
            'dpp.tahun'=>'tahun',
            'dpp.periode'=>'periode',
            'dmm_penilaian_opco.start_date'=>'start_date',
            'dmm_penilaian_opco.end_date'=>'end_date',
            'dmm_penilaian_opco.status'=>'status',
            'u.username' => 'created_by_name'
        ];
        
        $data  = DataTables::of($query)
            ->filter(function ($query) use ($request, $columns) {
                $this->filterColumn($columns, $request, $query);
            })
            ->order(function ($query) use ($request, $columns) {
                $this->orderColumn($columns, $request, $query);
            })
            ->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }
    public function show($uuid)
    {
        $attributes['uuid'] = $uuid;

        $roles = [  
            'uuid' => 'required|exists:dmm_penilaian_opco',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);
        $data = PenilaianOpco::list()->where('dmm_penilaian_opco.uuid', $uuid)->first();
        $response = responseSuccess(trans("messages.read-success"), $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
    }
    public function store(Request $request)
    {
        $attributes =  $request->only(['company_id','periode_penilaian_id','start_date','end_date','status']);
        $roles = [
            'company_id' => 'required|exists:m_company,id',
            'periode_penilaian_id' => 'required|exists:dmm_periode_penilaian,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'date'   => trans('messages.date'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);

        $attributes['created_by'] = Auth()->user()->id;

        DB::beginTransaction();
        try {
            //code...
            $data = PenilaianOpco::create($attributes);
            DB::commit();
            $response = responseSuccess(trans("messages.create-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.create-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
    public function update($uuid,Request $request)
    {
        $attributes =  $request->only(['company_id','periode_penilaian_id','start_date','end_date','status']);
        $roles = [
            'company_id' => 'required|exists:m_company,id',
            'periode_penilaian_id' => 'required|exists:dmm_periode_penilaian,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'unique'   => trans('messages.unique'),
            'date'   => trans('messages.date'),
        ];
        $this->validators($attributes, $roles, $messages);

        $attributes['updated_by'] = Auth()->user()->id;
        $data = $this->findDataWhere(PenilaianOpco::class, ['uuid' => $uuid]);
        DB::beginTransaction();
        try {
            //code...
            $data->update($attributes);
            DB::commit();
            $response = responseSuccess(trans("messages.update-success"), $data);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
    public function destroy($uuid)
    {
        $attributes['uuid'] = $uuid;
        $roles = [  
            'uuid' => 'required|exists:dmm_penilaian_opco',
        ];
        $messages = [
            'required' => trans('messages.required'),
            'exists'   => trans('messages.exists'),
        ];
        $this->validators($attributes, $roles, $messages);
        DB::beginTransaction();
        try {
            //code...
            PenilaianOpco::where('uuid', $uuid)->delete();
            DB::commit();
            $response = responseSuccess(trans("messages.delete-success"), []);
            return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (\Exception $ex) {
            DB::rollback();
            $response = responseFail(trans("messages.delete-fail"), $ex->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }
    }
}
