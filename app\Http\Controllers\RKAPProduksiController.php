<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\RKAPProduksi;
use App\Models\Opco;
use App\Models\KilnPlant;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Auth;
use DataTables;

use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ExportTempRKAPProduksi;
use App\Imports\ImportRKAPProduksi;

class RKAPProduksiController extends Controller
{

    private $listOpco;

    public function __construct(){
        $opco = Opco::select('kode_opco')->orderBy('no_pbi')->get()->pluck('kode_opco');
        $this->listOpco = $opco;
    }

    public function index()
    {
        $data = [
            'title' => 'RKAP Produksi',
            'breadcrumb' => [
                [
                    'title'=>'Input RKAP',
                    'url'=>'/rkap-produksi',
                ],
                [
                    'title'=>'RKAP Produksi',
                    'url'=>'',
                ]
            ],
        ];
        $data['menus'] = $this->getDashboardMenu();

        return view('rkapProduksi', $data);    
    }

    public function viewImport()
    {
        $data = [
            'title' => 'RKAP Produksi',
            'breadcrumb' => [
                [
                    'title'=>'Input RKAP',
                    'url'=>'/rkap-produksi',
                ],
                [
                    'title'=>'RKAP Produksi',
                    'url'=>'/rkap-produksi/view-import',
                ],
                [
                    'title'=>'Import Data',
                    'url'=>'',
                ]
            ],
        ];
        $opco = Opco::select(['kode_opco', 'nama_opco'])->where('kode_opco','!=','SI2000')->orderBy('no_pbi')->get()->toArray();
        $opt_opco = "";
        foreach ($opco as $value) {
            $kode_opco = $value['kode_opco'];
            $nama_opco = $value['nama_opco'];
            $opt_opco .= "<option value='$kode_opco'>$nama_opco</option>";
        }
        $data['opco'] = $opt_opco;
        return view('rkapProduksiImport', $data);    
    }

    public function filter()
    {
        //filter opco
        $opco = Opco::select(['kode_opco', 'nama_opco'])->where('kode_opco','!=','SI2000')->orderBy('no_pbi')->get()->toArray();
        //filter plant
        $kilnPlant = KilnPlant::select(['kode_opco', 'name_plant'])->orderBy('no_pbi')->get()->toArray();
        //filter tahun
        $yearNow = now()->year;
        $tahun = [];
        $minus = 0;
        for ($i=2019; $i <= $yearNow+1; $i++) {
            if($i == $yearNow){
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => true
                ];
            }
            else{
                $tahun[]=[
                    'tahun' => $i,
                    'selected' => false
                ];  
            }
            $minus++;
        }
        $tahun = array_reverse($tahun);
        //filter bulan
        $months = ['January', 'February', 'March', 'April', 'Mei', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $bulan = [];
        for ($i=0; $i < count($months); $i++) { 
            $noBulan = strval($i+1);
            if ($i+1 == date('m')) {
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => true
                ];
            } 
            else{
                $bulan[] = [
                    'bulan' => $months[$i],
                    'no_bulan'  => $noBulan,
                    'selected' => false
                ];
            }       
        }

        $data = [
            'opco'      => $opco,         
            'kilnPlant' => $kilnPlant,               
            'tahun'     => $tahun,
            'bulan'     => $bulan
        ];
        return response()->json($data, 200, [], JSON_PRETTY_PRINT);
    }

    public function datatables(Request $request)
    {
        $rkap = RKAPProduksi::select(['id_rkap_produksi','kode_opco','tahun','bulan','bln_indo','bln_english','rkap_prod_klinker','rkap_prod_semen','rkap_clinker_sold','rkap_prod_output',
        DB::raw("to_char(rkap_prod_klinker, '999G999G999G999G999') as new_rkap_prod_klinker"),
        DB::raw("to_char(rkap_prod_semen, '999G999G999G999G999') as new_rkap_prod_semen"),
        DB::raw("to_char(rkap_clinker_sold, '999G999G999G999G999') as new_rkap_clinker_sold"),
        DB::raw("to_char(rkap_prod_output, '999G999G999G999G999') as new_rkap_prod_output")])
        ->join('m_bulan', 'bulan', '=', 'kode_bulan');
        if($request->filter_opco){
            $rkap = $rkap -> where('kode_opco', $request->filter_opco);
        }
        if($request->filter_tahun){
            $rkap = $rkap -> where('tahun', $request->filter_tahun);
        }
        if($request->filter_bulan){
            $rkap = $rkap -> where('bulan', $request->filter_bulan);
        }
        if($request->filter_search){
            $rkap = $rkap -> where('kode_opco', 'ilike','%'.$filter.'%')
            -> orWhere('tahun', 'ilike','%'.$filter.'%')
            -> orWhere('bln_indo', 'ilike','%'.$filter.'%')
            -> orWhere('bln_english', 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_prod_klinker, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_prod_semen, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_clinker_sold, '999G999G999G999G999')"), 'ilike','%'.$filter.'%')
            -> orWhere(DB::raw("to_char(rkap_prod_output, '999G999G999G999G999')"), 'ilike','%'.$filter.'%');
        }

        $data     = DataTables::of($rkap)
        ->addIndexColumn()
        ->addColumn('action', function($row){
            $btn = '<button type="button" class="edits btn btn-sm btn-icon btn-outline-primary mr-2" title="Edit" data-toggle="tooltip" data-id="'.$row->id_rkap_produksi.'" ><i class="fa fa-edit"></i></button>';
            $btn = $btn.'<button type="button" class="deletes btn btn-sm btn-icon btn-outline-danger" title="Delete" data-toggle="tooltip" data-id="'.$row->id_rkap_produksi.'" ><i class="fa fa-trash"></i></button>';
            return $btn;
        })
        ->rawColumns(['action'])        
        ->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);        
    }

    public function store(Request $request)
    {
        //update value request
        $request->merge([
            'rkap_prod_klinker' => $request->rkap_prod_klinker == null ? 0 : str_replace('.','',$request->rkap_prod_klinker),
            'rkap_prod_semen' => $request->rkap_prod_semen == null ? 0 : str_replace('.','',$request->rkap_prod_semen),
            'rkap_clinker_sold' => $request->rkap_clinker_sold == null ? 0 : str_replace('.','',$request->rkap_clinker_sold),
            'rkap_prod_output' => $request->rkap_prod_output== null ? 0 : str_replace('.','',$request->rkap_prod_output),
        ]);

        $request->validate([
            'kode_opco' => ['required'],
            'tahun' => ['required'],
            'bulan' => ['required'],
            'rkap_prod_klinker' => ['nullable','integer'],
            'rkap_prod_semen' =>  ['nullable','integer'],
            'rkap_clinker_sold' => ['nullable','integer'],
            'rkap_prod_output' =>  ['nullable','integer'],
        ]);

        $cekRKAP = RKAPProduksi::where('kode_opco', $request->kode_opco)
        ->where('tahun', $request->tahun)
        ->where('bulan', $request->bulan)->first();
        if($cekRKAP){
            $response = [
                'success' => false,
                'data' => $cekRKAP
            ];
            return response()->json($response,200);    
        }
        try {
            $rkap = RKAPProduksi::create([
                'kode_opco' => $request->kode_opco,
                'tahun' => $request->tahun,
                'bulan' => $request->bulan,
                'create_by' => Auth::user()->username,
                'rkap_prod_klinker' => $request->rkap_prod_klinker,
                'rkap_prod_semen' => $request->rkap_prod_semen,
                'rkap_clinker_sold' => $request->rkap_clinker_sold,
                'rkap_prod_output' => $request->rkap_prod_output,
            ]);

            $response = responseSuccess(trans('messages.create-success'), $rkap);
            return response()->json($response, 201);
        } catch (Exception $e) {
            $response = responseFail(trans('messages.create-fail'), [$e->getMessage()]);
            return response()->json($response, 500);
        }
    }

    public function show($rkap)
    {
        $query   = RKAPProduksi::find($rkap);
        $response = responseSuccess('Data successfully displayed',$query);
        return response()->json($response,200);    
    }

    public function update(Request $request, $id)
    {
        $data = $this->findDataWhere(RKAPProduksi::class, ['id_rkap_produksi' => $id]);

        //update value request
        $request->merge([
            'rkap_prod_klinker' => $request->rkap_prod_klinker == null ? 0 : str_replace('.','',$request->rkap_prod_klinker),
            'rkap_prod_semen' => $request->rkap_prod_semen == null ? 0 : str_replace('.','',$request->rkap_prod_semen),
            'rkap_clinker_sold' => $request->rkap_clinker_sold == null ? 0 : str_replace('.','',$request->rkap_clinker_sold),
            'rkap_prod_output' => $request->rkap_prod_output== null ? 0 : str_replace('.','',$request->rkap_prod_output),
        ]);

        $request->validate([
            'kode_opco' => ['required'],
            'tahun' => ['required'],
            'bulan' => ['required'],
            'rkap_prod_klinker' => ['nullable','integer'],
            'rkap_prod_semen' =>  ['nullable','integer'],
            'rkap_clinker_sold' => ['nullable','integer'],
            'rkap_prod_output' =>  ['nullable','integer'],
        ]);

        DB::beginTransaction();
        try {
            $data->update([
                'kode_opco' => $request->kode_opco,
                'tahun' => $request->tahun,
                'bulan' => $request->bulan,
                'update_by' => Auth::user()->username,
                'rkap_prod_klinker' => $request->rkap_prod_klinker,
                'rkap_prod_semen' => $request->rkap_prod_semen,
                'rkap_clinker_sold' => $request->rkap_clinker_sold,
                'rkap_prod_output' => $request->rkap_prod_output,
            ]);
        DB::commit();
        $response = responseSuccess('Data updated successfully ', $data);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT);
        } catch (Exception $e) {
            DB::rollback();
            $response = responseFail(trans("messages.update-fail"), $e->getMessage());
            return response()->json($response, 500, [], JSON_PRETTY_PRINT);
        }    
    }

    public function destroy($id)
    {
        RKAPProduksi::destroy($id);
        $response = responseSuccess("Data deleted successfully");
        return response()->json($response,200);    
    }

    public function temp(Request $request)
    {
            $request->validate([
                'filter_tahun' => 'nullable|numeric',
            ]);
            $data =  RKAPProduksi::select(
                'kode_opco',
                'tahun',
                'bulan',
                'rkap_prod_klinker',
                'rkap_prod_semen',
                'rkap_clinker_sold',
                'rkap_prod_output',
            );
            if ($request->filter_tahun) {
                $filter_tahun = " tahun = ? ";
                $data->whereRaw($filter_tahun,[$request->filter_tahun]);
            }
            $data = $data->get();

		return Excel::download(new ExportTempRKAPProduksi($data), 'Template RKAP Produksi.xlsx');
    }

    public function import(Request $request)
    {
		// validasi
		$this->validate($request, [
			'excel' => 'required|mimes:csv,xls,xlsx' //spekati satu file, validasi filename -> header -> coba cek dimeta data author 
		]);

        // menangkap file excel
        $file = $request->file('excel');
        
        // import data
        $import = new ImportRKAPProduksi;
        Excel::import($import, $file);

        $datas = ($import->data);
        //mapping data from import excel
        $newData = [];
        foreach ($datas as $value) {
            $temp = [];
            $temp['kode_opco'] = $value['opco'];
            $temp['tahun'] = $value['year'];
            $temp['bulan'] = $value['month'];
            $temp['rkap_prod_klinker'] = $value['rkap_prod_klinker_ton'] == null ? 0 : $value['rkap_prod_klinker_ton'];
            $temp['rkap_prod_semen'] = $value['rkap_prod_cement_ton'] == null ? 0 : $value['rkap_prod_cement_ton'];
            $temp['rkap_clinker_sold'] = $value['rkap_clinker_sales_ton'] == null ? 0 : $value['rkap_clinker_sales_ton'];
            $temp['rkap_prod_output'] = $value['rkap_prod_output_ton'] == null ? 0 : $value['rkap_prod_output_ton'];
            array_push($newData, $temp);
        }
        //validation 
        $result = [];
        foreach ($newData as $value) {
            $format = $value;
            $validator = Validator::make($value,[
                'kode_opco' => ['required',Rule::in($this->listOpco)],
                'tahun' => ['required', 'integer'],
                'bulan' => ['required', 'integer'],
                'rkap_prod_klinker' => 'nullable|integer',
                'rkap_prod_semen' =>  'nullable|integer',
                'rkap_clinker_sold' => 'nullable|integer',
                'rkap_prod_output' =>  'nullable|integer',
            ]);
            $format['is_valid'] = 'Valid';
            $format['note'] = array_values($validator->errors()->messages());
            $tempMessage = '';
            foreach ($format['note'] as $message) {
                $tempMessage .= $message[0];
            }
            $format['note'] = $tempMessage;
            $format['note'] == '' ? $format['is_valid'] = 'Valid' : $format['is_valid'] = 'Invalid';
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT); 
    }

    public function insertData(Request $request)
    {
        $excel = json_decode($request->excel);
        $listCol = ['kode_opco', 'tahun', 'bulan', 'rkap_prod_klinker', 'rkap_prod_semen', 'rkap_clinker_sold', 'rkap_prod_output'];
        $result = [];
        foreach ($excel as $data) {
            $oldData = $data;
            foreach ($listCol as $index => $col) {
                $data[$col] = $data[$index] == null ? 0 : $data[$index];
            }
            $data = array_diff_key($data, $oldData); 
            $format = $data;
            $validator = Validator::make($data,[
                'kode_opco' => ['required',Rule::in($this->listOpco)],
                'tahun' => ['required', 'integer'],
                'bulan' => ['required', 'integer'],
                'rkap_prod_klinker' => 'nullable|integer',
                'rkap_prod_semen' =>  'nullable|integer',
                'rkap_clinker_sold' => 'nullable|integer',
                'rkap_prod_output' =>  'nullable|integer',
            ]);
            $format['is_valid'] = 'Valid';
            $format['note'] = array_values($validator->errors()->messages());
            $tempMessage = '';
            foreach ($format['note'] as $message) {
                $tempMessage .= $message[0];
            }
            $format['note'] = $tempMessage;
            $format['note'] == '' ? $format['is_valid'] = 'Valid' : $format['is_valid'] = 'Invalid';
            array_push($result, $format);
        }
        $data     = DataTables::of($result)->make(true);
        $response = $data->getData(true);
        return response()->json($response, 200, [], JSON_PRETTY_PRINT); 
    }

    public function saveData(Request $request)
    {
        $excel = json_decode($request->excel);
        foreach ($excel as $item) {
            RKAPProduksi::updateOrCreate([
                'kode_opco' => $item[0],
                'tahun' => $item[1],
                'bulan' => $item[2],
            ],[
                'kode_opco' => $item[0],
                'tahun' => $item[1],
                'bulan' => $item[2],
                'rkap_prod_klinker' => $item[3],
                'rkap_prod_semen' => $item[4],
                'rkap_clinker_sold' => $item[5],
                'rkap_prod_output' => $item[6],
                'create_by' => Auth::user()->username,
                'update_by' => Auth::user()->username
            ]);
        }
        $response = responseSuccess('Data added successfully');
        return response()->json($response,200);
    }
    
}
