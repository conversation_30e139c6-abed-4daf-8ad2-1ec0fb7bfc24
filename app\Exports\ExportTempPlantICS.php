<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ExportTempPlantICS implements WithHeadings, ShouldAutoSize, WithEvents, WithTitle, WithColumnWidths, WithStyles
{
    //set header value
    public function headings():array
    {
        return [
            'ORG',
            'Kode Plant',
            'Name Plant',
            'Opco',
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                //set background color for header (A1:D1)
                $event->sheet->getDelegate()
                ->getStyle('A1:D1')
                ->getFill()
                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB('FFFF00');

            },
        ];
    }

    //set name for worksheet
    public function title(): string
    {
        return 'Plant ICS';
    }

    //set width for header
    public function columnWidths(): array
    {
        return [
            'A' => 10,
            'B' => 10,    
            'C' => 30,
            'D' => 10,
        ];
    }

    //set bold for header
    public function styles(Worksheet $sheet)
    {
        return [
            'A'    => ['font' => ['bold' => true]],
            'B'    => ['font' => ['bold' => true]],
            'C'    => ['font' => ['bold' => true]],
            'D'    => ['font' => ['bold' => true]],
        ];
    }

}
